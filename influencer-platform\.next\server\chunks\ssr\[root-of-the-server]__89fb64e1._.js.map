{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/chat-permissions.ts"], "sourcesContent": ["import { supabase } from '@/lib/supabase';\n\nexport interface ChatPermission {\n  id: string;\n  business_id: string;\n  influencer_id: string;\n  offer_id: string | null;\n  campaign_application_id: string | null;\n  business_approved: boolean | null;\n  influencer_approved: boolean | null;\n  chat_enabled: boolean | null;\n  created_at: string | null;\n  updated_at: string | null;\n}\n\n/**\n * Create or update chat permission for direct offer\n */\nexport async function upsertOfferChatPermission(\n  businessId: string,\n  influencerId: string,\n  offerId: string,\n  businessApproved: boolean = false,\n  influencerApproved: boolean = false\n) {\n  const { data, error } = await supabase.rpc('upsert_chat_permission', {\n    p_business_id: businessId,\n    p_influencer_id: influencerId,\n    p_offer_id: offerId,\n    p_business_approved: businessApproved,\n    p_influencer_approved: influencerApproved\n  });\n\n  if (error) {\n    console.error('Error upserting offer chat permission:', error);\n    throw error;\n  }\n\n  return data;\n}\n\n/**\n * Create or update chat permission for campaign application\n */\nexport async function upsertApplicationChatPermission(\n  businessId: string,\n  influencerId: string,\n  applicationId: string,\n  businessApproved: boolean = false,\n  influencerApproved: boolean = false\n) {\n  const { data, error } = await supabase.rpc('upsert_chat_permission', {\n    p_business_id: businessId,\n    p_influencer_id: influencerId,\n    p_campaign_application_id: applicationId,\n    p_business_approved: businessApproved,\n    p_influencer_approved: influencerApproved\n  });\n\n  if (error) {\n    console.error('Error upserting application chat permission:', error);\n    throw error;\n  }\n\n  return data;\n}\n\n/**\n * Check if chat is enabled between business and influencer for specific offer/application\n */\nexport async function isChatEnabled(\n  businessId: string,\n  influencerId: string,\n  offerId?: string,\n  applicationId?: string\n): Promise<boolean> {\n  let query = supabase\n    .from('chat_permissions')\n    .select('chat_enabled')\n    .eq('business_id', businessId)\n    .eq('influencer_id', influencerId);\n\n  if (offerId) {\n    query = query.eq('offer_id', offerId);\n  } else if (applicationId) {\n    query = query.eq('campaign_application_id', applicationId);\n  } else {\n    throw new Error('Either offerId or applicationId must be provided');\n  }\n\n  const { data, error } = await query.single();\n\n  if (error) {\n    if (error.code === 'PGRST116') {\n      // No permission record found, chat not enabled\n      return false;\n    }\n    console.error('Error checking chat permission:', error);\n    throw error;\n  }\n\n  return data?.chat_enabled || false;\n}\n\n/**\n * Get chat permission details\n */\nexport async function getChatPermission(\n  businessId: string,\n  influencerId: string,\n  offerId?: string,\n  applicationId?: string\n): Promise<ChatPermission | null> {\n  let query = supabase\n    .from('chat_permissions')\n    .select('*')\n    .eq('business_id', businessId)\n    .eq('influencer_id', influencerId);\n\n  if (offerId) {\n    query = query.eq('offer_id', offerId);\n  } else if (applicationId) {\n    query = query.eq('campaign_application_id', applicationId);\n  } else {\n    throw new Error('Either offerId or applicationId must be provided');\n  }\n\n  const { data, error } = await query.single();\n\n  if (error) {\n    if (error.code === 'PGRST116') {\n      // No permission record found\n      return null;\n    }\n    console.error('Error getting chat permission:', error);\n    throw error;\n  }\n\n  return data;\n}\n\n/**\n * Approve chat from business side\n */\nexport async function approveBusinessChat(\n  businessId: string,\n  influencerId: string,\n  offerId?: string,\n  applicationId?: string\n) {\n  let query = supabase\n    .from('chat_permissions')\n    .update({ business_approved: true })\n    .eq('business_id', businessId)\n    .eq('influencer_id', influencerId);\n\n  if (offerId) {\n    query = query.eq('offer_id', offerId);\n  } else if (applicationId) {\n    query = query.eq('campaign_application_id', applicationId);\n  } else {\n    throw new Error('Either offerId or applicationId must be provided');\n  }\n\n  const { error } = await query;\n\n  if (error) {\n    console.error('Error approving business chat:', error);\n    throw error;\n  }\n}\n\n/**\n * Approve chat from influencer side\n */\nexport async function approveInfluencerChat(\n  businessId: string,\n  influencerId: string,\n  offerId?: string,\n  applicationId?: string\n) {\n  let query = supabase\n    .from('chat_permissions')\n    .update({ influencer_approved: true })\n    .eq('business_id', businessId)\n    .eq('influencer_id', influencerId);\n\n  if (offerId) {\n    query = query.eq('offer_id', offerId);\n  } else if (applicationId) {\n    query = query.eq('campaign_application_id', applicationId);\n  } else {\n    throw new Error('Either offerId or applicationId must be provided');\n  }\n\n  const { error } = await query;\n\n  if (error) {\n    console.error('Error approving influencer chat:', error);\n    throw error;\n  }\n}\n\n/**\n * Get all chat permissions for a user (business or influencer)\n */\nexport async function getUserChatPermissions(userId: string): Promise<ChatPermission[]> {\n  const { data, error } = await supabase\n    .from('chat_permissions')\n    .select('*')\n    .or(`business_id.eq.${userId},influencer_id.eq.${userId}`)\n    .order('created_at', { ascending: false });\n\n  if (error) {\n    console.error('Error getting user chat permissions:', error);\n    throw error;\n  }\n\n  return data || [];\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAkBO,eAAe,0BACpB,UAAkB,EAClB,YAAoB,EACpB,OAAe,EACf,mBAA4B,KAAK,EACjC,qBAA8B,KAAK;IAEnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,0BAA0B;QACnE,eAAe;QACf,iBAAiB;QACjB,YAAY;QACZ,qBAAqB;QACrB,uBAAuB;IACzB;IAEA,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,0CAA0C;QACxD,MAAM;IACR;IAEA,OAAO;AACT;AAKO,eAAe,gCACpB,UAAkB,EAClB,YAAoB,EACpB,aAAqB,EACrB,mBAA4B,KAAK,EACjC,qBAA8B,KAAK;IAEnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,0BAA0B;QACnE,eAAe;QACf,iBAAiB;QACjB,2BAA2B;QAC3B,qBAAqB;QACrB,uBAAuB;IACzB;IAEA,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,MAAM;IACR;IAEA,OAAO;AACT;AAKO,eAAe,cACpB,UAAkB,EAClB,YAAoB,EACpB,OAAgB,EAChB,aAAsB;IAEtB,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,oBACL,MAAM,CAAC,gBACP,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,iBAAiB;IAEvB,IAAI,SAAS;QACX,QAAQ,MAAM,EAAE,CAAC,YAAY;IAC/B,OAAO,IAAI,eAAe;QACxB,QAAQ,MAAM,EAAE,CAAC,2BAA2B;IAC9C,OAAO;QACL,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,MAAM;IAE1C,IAAI,OAAO;QACT,IAAI,MAAM,IAAI,KAAK,YAAY;YAC7B,+CAA+C;YAC/C,OAAO;QACT;QACA,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;IAEA,OAAO,MAAM,gBAAgB;AAC/B;AAKO,eAAe,kBACpB,UAAkB,EAClB,YAAoB,EACpB,OAAgB,EAChB,aAAsB;IAEtB,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,iBAAiB;IAEvB,IAAI,SAAS;QACX,QAAQ,MAAM,EAAE,CAAC,YAAY;IAC/B,OAAO,IAAI,eAAe;QACxB,QAAQ,MAAM,EAAE,CAAC,2BAA2B;IAC9C,OAAO;QACL,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,MAAM;IAE1C,IAAI,OAAO;QACT,IAAI,MAAM,IAAI,KAAK,YAAY;YAC7B,6BAA6B;YAC7B,OAAO;QACT;QACA,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;IAEA,OAAO;AACT;AAKO,eAAe,oBACpB,UAAkB,EAClB,YAAoB,EACpB,OAAgB,EAChB,aAAsB;IAEtB,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,oBACL,MAAM,CAAC;QAAE,mBAAmB;IAAK,GACjC,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,iBAAiB;IAEvB,IAAI,SAAS;QACX,QAAQ,MAAM,EAAE,CAAC,YAAY;IAC/B,OAAO,IAAI,eAAe;QACxB,QAAQ,MAAM,EAAE,CAAC,2BAA2B;IAC9C,OAAO;QACL,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM;IAExB,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAKO,eAAe,sBACpB,UAAkB,EAClB,YAAoB,EACpB,OAAgB,EAChB,aAAsB;IAEtB,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,oBACL,MAAM,CAAC;QAAE,qBAAqB;IAAK,GACnC,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,iBAAiB;IAEvB,IAAI,SAAS;QACX,QAAQ,MAAM,EAAE,CAAC,YAAY;IAC/B,OAAO,IAAI,eAAe;QACxB,QAAQ,MAAM,EAAE,CAAC,2BAA2B;IAC9C,OAAO;QACL,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM;IAExB,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAKO,eAAe,uBAAuB,MAAc;IACzD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,EAAE,CAAC,CAAC,eAAe,EAAE,OAAO,kBAAkB,EAAE,QAAQ,EACxD,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,wCAAwC;QACtD,MAAM;IACR;IAEA,OAAO,QAAQ,EAAE;AACnB", "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/campaigns.ts"], "sourcesContent": ["import { supabase } from './supabase';\nimport { upsertApplicationChatPermission } from './chat-permissions';\nimport { Database } from './database.types';\n\ntype Campaign = Database['public']['Tables']['campaigns']['Row'];\ntype CampaignInsert = Database['public']['Tables']['campaigns']['Insert'];\ntype CampaignUpdate = Database['public']['Tables']['campaigns']['Update'];\ntype CampaignApplication = Database['public']['Tables']['campaign_applications']['Row'];\ntype CampaignApplicationInsert = Database['public']['Tables']['campaign_applications']['Insert'];\n\n// Campaign CRUD operations\nexport async function createCampaign(campaign: CampaignInsert) {\n  const { data, error } = await supabase\n    .from('campaigns')\n    .insert(campaign)\n    .select()\n    .single();\n\n  return { data, error };\n}\n\nexport async function getCampaign(id: string) {\n  const { data, error } = await supabase\n    .from('campaigns')\n    .select(`\n      *,\n      businesses!inner(\n        company_name,\n        industry,\n        profiles!inner(\n          username,\n          avatar_url\n        )\n      )\n    `)\n    .eq('id', id)\n    .single();\n\n  return { data, error };\n}\n\nexport async function getCampaignWithDetails(id: string) {\n  const { data, error } = await supabase\n    .from('campaigns_with_details')\n    .select('*')\n    .eq('id', id)\n    .single();\n\n  return { data, error };\n}\n\n\n\nexport async function deleteCampaign(id: string) {\n  const { data, error } = await supabase\n    .from('campaigns')\n    .delete()\n    .eq('id', id);\n\n  return { data, error };\n}\n\n// Business campaigns\nexport async function getBusinessCampaigns(businessId: string, status?: string) {\n  let query = supabase\n    .from('campaigns')\n    .select(`\n      *,\n      campaign_applications(count)\n    `)\n    .eq('business_id', businessId)\n    .order('created_at', { ascending: false });\n\n  if (status) {\n    query = query.eq('status', status);\n  }\n\n  const { data, error } = await query;\n  return { data, error };\n}\n\n// Campaign search and filtering for influencers\nexport interface CampaignFilters {\n  search?: string;\n  categories?: number[];\n  platforms?: number[];\n  minBudget?: number;\n  maxBudget?: number;\n  location?: string;\n  minFollowers?: number;\n  maxFollowers?: number;\n  gender?: string;\n  deadlineBefore?: string;\n  sortBy?: 'created_at' | 'budget' | 'application_deadline' | 'applications_count';\n  sortOrder?: 'asc' | 'desc';\n  limit?: number;\n  offset?: number;\n}\n\nexport async function searchCampaigns(filters: CampaignFilters = {}) {\n  const {\n    search,\n    categories,\n    platforms,\n    minBudget,\n    maxBudget,\n    location,\n    minFollowers,\n    maxFollowers,\n    gender,\n    deadlineBefore,\n    sortBy = 'created_at',\n    sortOrder = 'desc',\n    limit = 20,\n    offset = 0\n  } = filters;\n\n  // Get current user for debugging\n  const { data: { user } } = await supabase.auth.getUser();\n\n  // Use RPC function to bypass RLS issues\n  const { data: allCampaigns, error: rpcError } = await supabase\n    .rpc('get_active_campaigns_for_influencers');\n\n  if (rpcError) {\n    console.error('RPC error:', rpcError);\n    return { data: [], error: rpcError };\n  }\n\n\n\n  // Apply client-side filtering\n  let filteredCampaigns = allCampaigns || [];\n\n  // Basic text search in title and description\n  if (search) {\n    const searchLower = search.toLowerCase();\n    filteredCampaigns = filteredCampaigns.filter(campaign =>\n      campaign.title.toLowerCase().includes(searchLower) ||\n      campaign.description.toLowerCase().includes(searchLower)\n    );\n  }\n\n  // Budget range\n  if (minBudget !== undefined) {\n    filteredCampaigns = filteredCampaigns.filter(campaign => campaign.budget >= minBudget);\n  }\n  if (maxBudget !== undefined) {\n    filteredCampaigns = filteredCampaigns.filter(campaign => campaign.budget <= maxBudget);\n  }\n\n  // Skip advanced filters for now since RPC returns basic fields only\n  // TODO: Add these filters back when we have all fields in RPC\n\n  // Sorting\n  filteredCampaigns.sort((a, b) => {\n    let aValue, bValue;\n\n    switch (sortBy) {\n      case 'budget':\n        aValue = a.budget;\n        bValue = b.budget;\n        break;\n      default: // 'created_at'\n        aValue = new Date(a.created_at).getTime();\n        bValue = new Date(b.created_at).getTime();\n    }\n\n    return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;\n  });\n\n  // Pagination\n  const startIndex = offset;\n  const endIndex = offset + limit;\n  const paginatedCampaigns = filteredCampaigns.slice(startIndex, endIndex);\n\n\n\n  return { data: paginatedCampaigns, error: null };\n}\n\n// Get campaign for editing (only for business owners and draft campaigns)\nexport async function getCampaignForEdit(campaignId: string) {\n  const { data: { user } } = await supabase.auth.getUser();\n\n  if (!user) {\n    return { data: null, error: { message: 'User not authenticated' } };\n  }\n\n  // Get campaign with all related data\n  const { data: campaign, error } = await supabase\n    .from('campaigns')\n    .select(`\n      *,\n      campaign_platforms (\n        platform_id,\n        platforms (name)\n      ),\n      campaign_categories (\n        category_id,\n        categories (name)\n      )\n    `)\n    .eq('id', campaignId)\n    .eq('business_id', user.id)\n    .eq('status', 'draft')\n    .single();\n\n  if (error) {\n    return { data: null, error };\n  }\n\n  if (!campaign) {\n    return { data: null, error: { message: 'Campaign not found or not editable' } };\n  }\n\n  return { data: campaign, error: null };\n}\n\n// Update campaign (only for business owners and draft campaigns)\nexport async function updateCampaign(campaignId: string, campaignData: any) {\n  const { data: { user } } = await supabase.auth.getUser();\n\n  if (!user) {\n    return { data: null, error: { message: 'User not authenticated' } };\n  }\n\n  // First check if campaign exists and is editable\n  const { data: existingCampaign, error: checkError } = await supabase\n    .from('campaigns')\n    .select('id, status, business_id')\n    .eq('id', campaignId)\n    .eq('business_id', user.id)\n    .eq('status', 'draft')\n    .single();\n\n  if (checkError || !existingCampaign) {\n    return { data: null, error: { message: 'Campaign not found or not editable' } };\n  }\n\n  // Update campaign\n  const { data: updatedCampaign, error: updateError } = await supabase\n    .from('campaigns')\n    .update({\n      title: campaignData.title,\n      description: campaignData.description,\n      budget: campaignData.budget,\n      content_types: campaignData.content_types,\n      min_followers: campaignData.min_followers,\n      max_followers: campaignData.max_followers,\n      age_range_min: campaignData.age_range_min,\n      age_range_max: campaignData.age_range_max,\n      gender: campaignData.gender,\n      updated_at: new Date().toISOString()\n    })\n    .eq('id', campaignId)\n    .select()\n    .single();\n\n  if (updateError) {\n    return { data: null, error: updateError };\n  }\n\n  // Update platforms\n  if (campaignData.platforms && campaignData.platforms.length > 0) {\n    // Delete existing platforms\n    await supabase\n      .from('campaign_platforms')\n      .delete()\n      .eq('campaign_id', campaignId);\n\n    // Insert new platforms\n    const platformInserts = campaignData.platforms.map((platformId: string) => ({\n      campaign_id: campaignId,\n      platform_id: platformId\n    }));\n\n    await supabase\n      .from('campaign_platforms')\n      .insert(platformInserts);\n  }\n\n  // Update categories\n  if (campaignData.categories && campaignData.categories.length > 0) {\n    // Delete existing categories\n    await supabase\n      .from('campaign_categories')\n      .delete()\n      .eq('campaign_id', campaignId);\n\n    // Insert new categories\n    const categoryInserts = campaignData.categories.map((categoryId: string) => ({\n      campaign_id: campaignId,\n      category_id: categoryId\n    }));\n\n    await supabase\n      .from('campaign_categories')\n      .insert(categoryInserts);\n  }\n\n  return { data: updatedCampaign, error: null };\n}\n\n// Update campaign status (with validation)\nexport async function updateCampaignStatus(campaignId: string, newStatus: 'draft' | 'active' | 'paused' | 'completed') {\n  const { data: { user } } = await supabase.auth.getUser();\n\n  if (!user) {\n    return { data: null, error: { message: 'User not authenticated' } };\n  }\n\n  // First check if campaign exists and user owns it\n  const { data: existingCampaign, error: checkError } = await supabase\n    .from('campaigns')\n    .select('id, status, business_id')\n    .eq('id', campaignId)\n    .eq('business_id', user.id)\n    .single();\n\n  if (checkError || !existingCampaign) {\n    return { data: null, error: { message: 'Campaign not found or access denied' } };\n  }\n\n  // Validation rules\n  if (existingCampaign.status === 'active' && newStatus === 'draft') {\n    return { data: null, error: { message: 'Cannot change active campaign back to draft' } };\n  }\n\n  if (existingCampaign.status === 'completed') {\n    return { data: null, error: { message: 'Cannot change status of completed campaign' } };\n  }\n\n  // Update status\n  const { data: updatedCampaign, error: updateError } = await supabase\n    .from('campaigns')\n    .update({\n      status: newStatus,\n      updated_at: new Date().toISOString()\n    })\n    .eq('id', campaignId)\n    .select()\n    .single();\n\n  if (updateError) {\n    return { data: null, error: updateError };\n  }\n\n  return { data: updatedCampaign, error: null };\n}\n\n// Get business campaigns for dashboard with counts\nexport async function getBusinessCampaignsForDashboard() {\n  const { data: { user } } = await supabase.auth.getUser();\n\n  if (!user) {\n    console.log('User not authenticated');\n    return { data: [], error: { message: 'User not authenticated' } };\n  }\n\n  console.log('Loading campaigns for business:', user.id);\n\n  const { data: campaigns, error } = await supabase\n    .from('campaigns')\n    .select(`\n      id,\n      title,\n      description,\n      budget,\n      status,\n      created_at,\n      content_types,\n      campaign_applications(count)\n    `)\n    .eq('business_id', user.id)\n    .order('created_at', { ascending: false });\n\n  console.log('Campaigns query result:', { campaigns, error });\n\n  if (error) {\n    console.error('Error loading campaigns:', error);\n    return { data: [], error };\n  }\n\n  return { data: campaigns || [], error: null };\n}\n\n// Featured campaigns - for now just return latest active campaigns\nexport async function getFeaturedCampaigns(limit: number = 6) {\n  const { data, error } = await supabase\n    .from('campaigns')\n    .select('*')\n    .eq('status', 'active')\n    .order('created_at', { ascending: false })\n    .limit(limit);\n\n  if (error) {\n    console.error('Featured campaigns error:', error);\n  }\n\n  return { data, error };\n}\n\n// Campaign applications\nexport async function createCampaignApplication(application: CampaignApplicationInsert) {\n  const { data, error } = await supabase\n    .from('campaign_applications')\n    .insert(application)\n    .select()\n    .single();\n\n  return { data, error };\n}\n\n// Check if influencer already applied to campaign\nexport async function hasInfluencerApplied(campaignId: string, influencerId: string) {\n  const { data, error } = await supabase\n    .from('campaign_applications')\n    .select('id, status, applied_at')\n    .eq('campaign_id', campaignId)\n    .eq('influencer_id', influencerId)\n    .single();\n\n  return { data, error };\n}\n\nexport async function getCampaignApplications(campaignId: string, status?: string) {\n  let query = supabase\n    .from('campaign_applications')\n    .select(`\n      *,\n      influencers!inner(\n        *,\n        profiles!inner(\n          username,\n          full_name,\n          avatar_url,\n          bio,\n          location\n        )\n      )\n    `)\n    .eq('campaign_id', campaignId)\n    .order('applied_at', { ascending: false });\n\n  if (status) {\n    query = query.eq('status', status);\n  }\n\n  const { data, error } = await query;\n  return { data, error };\n}\n\nexport async function getInfluencerApplications(influencerId: string, status?: string) {\n  try {\n    console.log('Fetching applications for influencer:', influencerId);\n\n    let query = supabase\n      .from('campaign_applications')\n      .select(`\n        id,\n        campaign_id,\n        influencer_id,\n        status,\n        proposed_rate,\n        proposal_text,\n        delivery_timeframe,\n        portfolio_links,\n        additional_services,\n        available_start_date,\n        experience_relevant,\n        audience_insights,\n        applied_at,\n        campaigns!inner(\n          id,\n          title,\n          description,\n          budget,\n          status,\n          business_id\n        )\n      `)\n      .eq('influencer_id', influencerId)\n      .order('applied_at', { ascending: false });\n\n    if (status) {\n      query = query.eq('status', status);\n    }\n\n    const { data: applicationsData, error: applicationsError } = await query;\n\n    if (applicationsError) {\n      console.error('Error fetching applications:', applicationsError);\n      throw applicationsError;\n    }\n\n    console.log('Applications data:', applicationsData);\n\n    if (!applicationsData || applicationsData.length === 0) {\n      return { data: [], error: null };\n    }\n\n    // Get business data separately\n    const businessIds = applicationsData.map(app => app.campaigns.business_id);\n    const { data: businessesData, error: businessesError } = await supabase\n      .from('businesses')\n      .select(`\n        id,\n        company_name\n      `)\n      .in('id', businessIds);\n\n    if (businessesError) {\n      console.error('Error fetching businesses:', businessesError);\n      throw businessesError;\n    }\n\n    // Get business profiles\n    const { data: profilesData, error: profilesError } = await supabase\n      .from('profiles')\n      .select('id, username, avatar_url')\n      .in('id', businessIds);\n\n    if (profilesError) {\n      console.error('Error fetching business profiles:', profilesError);\n    }\n\n    console.log('Businesses data:', businessesData);\n    console.log('Profiles data:', profilesData);\n\n    // Transform data to match expected interface\n    const transformedData = applicationsData.map(app => {\n      const business = businessesData?.find(b => b.id === app.campaigns.business_id);\n      const profile = profilesData?.find(p => p.id === app.campaigns.business_id);\n\n      return {\n        id: app.id,\n        campaign_id: app.campaign_id,\n        status: app.status,\n        proposed_rate: app.proposed_rate,\n        proposal_text: app.proposal_text,\n        portfolio_links: app.portfolio_links,\n        delivery_timeframe: app.delivery_timeframe,\n        additional_services: app.additional_services,\n        applied_at: app.applied_at,\n        campaigns: {\n          title: app.campaigns.title,\n          description: app.campaigns.description,\n          budget: app.campaigns.budget,\n          status: app.campaigns.status,\n          businesses: {\n            company_name: business?.company_name || 'Unknown Company',\n            profiles: {\n              username: profile?.username || 'Unknown',\n              avatar_url: profile?.avatar_url || null\n            }\n          }\n        }\n      };\n    });\n\n    return { data: transformedData, error: null };\n  } catch (error: any) {\n    console.error('Error in getInfluencerApplications:', error);\n    return { data: null, error: error.message };\n  }\n}\n\n\n\n// Campaign platforms and categories\nexport async function addCampaignPlatforms(campaignId: string, platforms: Array<{\n  platform_id: number;\n  content_type_ids: number[];\n  posts_required?: number;\n  budget_per_post?: number;\n}>) {\n  const platformData = platforms.map(platform => ({\n    campaign_id: campaignId,\n    ...platform\n  }));\n\n  const { data, error } = await supabase\n    .from('campaign_platforms')\n    .insert(platformData)\n    .select();\n\n  return { data, error };\n}\n\nexport async function addCampaignCategories(campaignId: string, categoryIds: number[]) {\n  const categoryData = categoryIds.map(categoryId => ({\n    campaign_id: campaignId,\n    category_id: categoryId\n  }));\n\n  const { data, error } = await supabase\n    .from('campaign_categories')\n    .insert(categoryData)\n    .select();\n\n  return { data, error };\n}\n\nexport async function removeCampaignPlatforms(campaignId: string) {\n  const { data, error } = await supabase\n    .from('campaign_platforms')\n    .delete()\n    .eq('campaign_id', campaignId);\n\n  return { data, error };\n}\n\nexport async function removeCampaignCategories(campaignId: string) {\n  const { data, error } = await supabase\n    .from('campaign_categories')\n    .delete()\n    .eq('campaign_id', campaignId);\n\n  return { data, error };\n}\n\n// Utility functions\n\nexport async function refreshCampaignsSearchView() {\n  const { data, error } = await supabase\n    .rpc('refresh_campaigns_search_view');\n\n  return { data, error };\n}\n\n// Get campaign statistics for business dashboard\nexport async function getCampaignStats(businessId: string) {\n  const { data: campaigns, error: campaignsError } = await supabase\n    .from('campaigns')\n    .select('status')\n    .eq('business_id', businessId);\n\n  if (campaignsError) return { data: null, error: campaignsError };\n\n  const { data: applications, error: applicationsError } = await supabase\n    .from('campaign_applications')\n    .select('status, campaign_id')\n    .in('campaign_id', campaigns?.map(c => c.id) || []);\n\n  if (applicationsError) return { data: null, error: applicationsError };\n\n  const stats = {\n    totalCampaigns: campaigns?.length || 0,\n    activeCampaigns: campaigns?.filter(c => c.status === 'active').length || 0,\n    completedCampaigns: campaigns?.filter(c => c.status === 'completed').length || 0,\n    totalApplications: applications?.length || 0,\n    pendingApplications: applications?.filter(a => a.status === 'pending').length || 0,\n    acceptedApplications: applications?.filter(a => a.status === 'accepted').length || 0,\n  };\n\n  return { data: stats, error: null };\n}\n\n\n\n// Get all applications for business campaigns\nexport async function getBusinessCampaignApplications(businessId: string, status?: string) {\n  try {\n    console.log('Fetching applications for business:', businessId);\n\n    // Use the correct field names from the actual database schema\n    let query = supabase\n      .from('campaign_applications')\n      .select(`\n        id,\n        campaign_id,\n        influencer_id,\n        status,\n        proposed_rate,\n        proposal_text,\n        delivery_timeframe,\n        portfolio_links,\n        additional_services,\n        available_start_date,\n        experience_relevant,\n        audience_insights,\n        applied_at,\n        campaigns!inner(\n          id,\n          title,\n          budget,\n          business_id\n        )\n      `)\n      .eq('campaigns.business_id', businessId)\n      .order('applied_at', { ascending: false });\n\n    if (status) {\n      query = query.eq('status', status);\n    }\n\n    const { data: applicationsData, error: applicationsError } = await query;\n\n    if (applicationsError) {\n      console.error('Error fetching applications:', applicationsError);\n      throw applicationsError;\n    }\n\n    console.log('Applications data:', applicationsData);\n\n    if (!applicationsData || applicationsData.length === 0) {\n      return { data: [], error: null };\n    }\n\n    // Get influencer data with profiles joined\n    const influencerIds = applicationsData.map(app => app.influencer_id);\n    const { data: influencersData, error: influencersError } = await supabase\n      .from('influencers')\n      .select(`\n        id,\n        profiles!inner(\n          id,\n          username,\n          full_name,\n          avatar_url\n        )\n      `)\n      .in('id', influencerIds);\n\n    if (influencersError) {\n      console.error('Error fetching influencers:', influencersError);\n      throw influencersError;\n    }\n\n    console.log('Influencers data:', influencersData);\n\n    // Transform data to match expected interface\n    const transformedData = applicationsData?.map(app => {\n      const influencer = influencersData?.find(inf => inf.id === app.influencer_id);\n      return {\n        id: app.id,\n        campaign_id: app.campaign_id,\n        influencer_id: app.influencer_id,\n        status: app.status,\n        proposed_rate: app.proposed_rate,\n        proposal_text: app.proposal_text,\n        delivery_timeframe: app.delivery_timeframe || 'Nije specificirano',\n        portfolio_links: app.portfolio_links || [],\n        experience_relevant: app.experience_relevant || app.proposal_text,\n        audience_insights: app.audience_insights || '',\n        additional_services: app.additional_services || '',\n        available_start_date: app.available_start_date,\n        applied_at: app.applied_at,\n        campaigns: {\n          id: app.campaigns.id,\n          title: app.campaigns.title,\n          budget: app.campaigns.budget,\n          business_id: app.campaigns.business_id\n        },\n        profiles: {\n          id: influencer?.profiles?.id || app.influencer_id,\n          username: influencer?.profiles?.username || 'Unknown',\n          full_name: influencer?.profiles?.full_name || 'Unknown User',\n          avatar_url: influencer?.profiles?.avatar_url || null\n        }\n      };\n    });\n\n    return { data: transformedData, error: null };\n  } catch (error: any) {\n    console.error('Error fetching campaign applications:', error);\n    return { data: null, error: error.message };\n  }\n}\n\n// Get single application with detailed info\nexport async function getCampaignApplication(applicationId: string) {\n  try {\n    const { data, error } = await supabase\n      .from('campaign_applications')\n      .select(`\n        id,\n        campaign_id,\n        influencer_id,\n        status,\n        proposed_price,\n        message,\n        portfolio_urls,\n        estimated_delivery_days,\n        additional_services,\n        applied_at,\n        campaigns!inner(\n          id,\n          title,\n          description,\n          budget,\n          requirements,\n          deliverables\n        ),\n        influencers!inner(\n          id,\n          full_name,\n          username,\n          avatar_url,\n          bio,\n          followers_count\n        )\n      `)\n      .eq('id', applicationId)\n      .single();\n\n    if (error) throw error;\n\n    // Get influencer categories and platforms\n    const { data: categoriesData } = await supabase\n      .from('influencer_categories')\n      .select('categories(name)')\n      .eq('influencer_id', data.influencer_id);\n\n    const { data: platformsData } = await supabase\n      .from('influencer_platforms')\n      .select(`\n        handle,\n        followers_count,\n        platforms(name)\n      `)\n      .eq('influencer_id', data.influencer_id);\n\n    // Transform data\n    const transformedData = {\n      id: data.id,\n      campaign_id: data.campaign_id,\n      influencer_id: data.influencer_id,\n      status: data.status,\n      proposed_price: data.proposed_price,\n      delivery_timeframe: `${data.estimated_delivery_days} dana`,\n      portfolio_links: data.portfolio_urls || [],\n      relevant_experience: data.message,\n      audience_insights: data.additional_services || '',\n      additional_services: [],\n      created_at: data.applied_at,\n      campaign: {\n        id: data.campaigns.id,\n        title: data.campaigns.title,\n        description: data.campaigns.description,\n        budget: data.campaigns.budget,\n        requirements: data.campaigns.requirements,\n        deliverables: data.campaigns.deliverables,\n      },\n      influencer: {\n        id: data.influencers.id,\n        full_name: data.influencers.full_name,\n        username: data.influencers.username,\n        avatar_url: data.influencers.avatar_url,\n        bio: data.influencers.bio,\n        followers_count: data.influencers.followers_count,\n        categories: categoriesData?.map(c => c.categories?.name).filter(Boolean) || [],\n        platforms: platformsData?.map(p => ({\n          platform_name: p.platforms?.name || '',\n          handle: p.handle,\n          followers_count: p.followers_count,\n        })) || [],\n      },\n    };\n\n    return { data: transformedData, error: null };\n  } catch (error: any) {\n    console.error('Error fetching campaign application:', error);\n    return { data: null, error: error.message };\n  }\n}\n\n// Update application status\nexport async function updateApplicationStatus(\n  applicationId: string,\n  status: 'accepted' | 'rejected',\n  rejectionReason?: string\n) {\n  try {\n    const updateData: any = { status };\n\n    if (status === 'rejected' && rejectionReason) {\n      updateData.rejection_reason = rejectionReason;\n    }\n\n    const { data, error } = await supabase\n      .from('campaign_applications')\n      .update(updateData)\n      .eq('id', applicationId)\n      .select(`\n        *,\n        campaigns!inner(business_id)\n      `)\n      .single();\n\n    if (error) throw error;\n\n    // Kreiraj chat dozvolu kada se aplikacija prihvati\n    if (status === 'accepted' && data) {\n      try {\n        // Business je odobrio prihvatanjem aplikacije\n        await upsertApplicationChatPermission(\n          data.campaigns.business_id,\n          data.influencer_id,\n          data.id,\n          true,  // business_approved - business je odobrio prihvatanjem\n          false  // influencer_approved - čeka da influencer odobri\n        );\n      } catch (chatError) {\n        console.error('Error creating chat permission:', chatError);\n        // Ne prekidamo proces ako chat dozvola ne uspije\n      }\n    }\n\n    return { data, error: null };\n  } catch (error: any) {\n    console.error('Error updating application status:', error);\n    return { data: null, error: error.message };\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAUO,eAAe,eAAe,QAAwB;IAC3D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,UACP,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,YAAY,EAAU;IAC1C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,CAAC;;;;;;;;;;IAUT,CAAC,EACA,EAAE,CAAC,MAAM,IACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,uBAAuB,EAAU;IACrD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,0BACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAIO,eAAe,eAAe,EAAU;IAC7C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,GACN,EAAE,CAAC,MAAM;IAEZ,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,eAAe,qBAAqB,UAAkB,EAAE,MAAe;IAC5E,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,aACL,MAAM,CAAC,CAAC;;;IAGT,CAAC,EACA,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,IAAI,QAAQ;QACV,QAAQ,MAAM,EAAE,CAAC,UAAU;IAC7B;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAC9B,OAAO;QAAE;QAAM;IAAM;AACvB;AAoBO,eAAe,gBAAgB,UAA2B,CAAC,CAAC;IACjE,MAAM,EACJ,MAAM,EACN,UAAU,EACV,SAAS,EACT,SAAS,EACT,SAAS,EACT,QAAQ,EACR,YAAY,EACZ,YAAY,EACZ,MAAM,EACN,cAAc,EACd,SAAS,YAAY,EACrB,YAAY,MAAM,EAClB,QAAQ,EAAE,EACV,SAAS,CAAC,EACX,GAAG;IAEJ,iCAAiC;IACjC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IAEtD,wCAAwC;IACxC,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,QAAQ,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC3D,GAAG,CAAC;IAEP,IAAI,UAAU;QACZ,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO;YAAE,MAAM,EAAE;YAAE,OAAO;QAAS;IACrC;IAIA,8BAA8B;IAC9B,IAAI,oBAAoB,gBAAgB,EAAE;IAE1C,6CAA6C;IAC7C,IAAI,QAAQ;QACV,MAAM,cAAc,OAAO,WAAW;QACtC,oBAAoB,kBAAkB,MAAM,CAAC,CAAA,WAC3C,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACtC,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC;IAEhD;IAEA,eAAe;IACf,IAAI,cAAc,WAAW;QAC3B,oBAAoB,kBAAkB,MAAM,CAAC,CAAA,WAAY,SAAS,MAAM,IAAI;IAC9E;IACA,IAAI,cAAc,WAAW;QAC3B,oBAAoB,kBAAkB,MAAM,CAAC,CAAA,WAAY,SAAS,MAAM,IAAI;IAC9E;IAEA,oEAAoE;IACpE,8DAA8D;IAE9D,UAAU;IACV,kBAAkB,IAAI,CAAC,CAAC,GAAG;QACzB,IAAI,QAAQ;QAEZ,OAAQ;YACN,KAAK;gBACH,SAAS,EAAE,MAAM;gBACjB,SAAS,EAAE,MAAM;gBACjB;YACF;gBACE,SAAS,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;gBACvC,SAAS,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;QAC3C;QAEA,OAAO,cAAc,QAAQ,SAAS,SAAS,SAAS;IAC1D;IAEA,aAAa;IACb,MAAM,aAAa;IACnB,MAAM,WAAW,SAAS;IAC1B,MAAM,qBAAqB,kBAAkB,KAAK,CAAC,YAAY;IAI/D,OAAO;QAAE,MAAM;QAAoB,OAAO;IAAK;AACjD;AAGO,eAAe,mBAAmB,UAAkB;IACzD,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IAEtD,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,MAAM;YAAM,OAAO;gBAAE,SAAS;YAAyB;QAAE;IACpE;IAEA,qCAAqC;IACrC,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7C,IAAI,CAAC,aACL,MAAM,CAAC,CAAC;;;;;;;;;;IAUT,CAAC,EACA,EAAE,CAAC,MAAM,YACT,EAAE,CAAC,eAAe,KAAK,EAAE,EACzB,EAAE,CAAC,UAAU,SACb,MAAM;IAET,IAAI,OAAO;QACT,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,IAAI,CAAC,UAAU;QACb,OAAO;YAAE,MAAM;YAAM,OAAO;gBAAE,SAAS;YAAqC;QAAE;IAChF;IAEA,OAAO;QAAE,MAAM;QAAU,OAAO;IAAK;AACvC;AAGO,eAAe,eAAe,UAAkB,EAAE,YAAiB;IACxE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IAEtD,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,MAAM;YAAM,OAAO;gBAAE,SAAS;YAAyB;QAAE;IACpE;IAEA,iDAAiD;IACjD,MAAM,EAAE,MAAM,gBAAgB,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACjE,IAAI,CAAC,aACL,MAAM,CAAC,2BACP,EAAE,CAAC,MAAM,YACT,EAAE,CAAC,eAAe,KAAK,EAAE,EACzB,EAAE,CAAC,UAAU,SACb,MAAM;IAET,IAAI,cAAc,CAAC,kBAAkB;QACnC,OAAO;YAAE,MAAM;YAAM,OAAO;gBAAE,SAAS;YAAqC;QAAE;IAChF;IAEA,kBAAkB;IAClB,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACjE,IAAI,CAAC,aACL,MAAM,CAAC;QACN,OAAO,aAAa,KAAK;QACzB,aAAa,aAAa,WAAW;QACrC,QAAQ,aAAa,MAAM;QAC3B,eAAe,aAAa,aAAa;QACzC,eAAe,aAAa,aAAa;QACzC,eAAe,aAAa,aAAa;QACzC,eAAe,aAAa,aAAa;QACzC,eAAe,aAAa,aAAa;QACzC,QAAQ,aAAa,MAAM;QAC3B,YAAY,IAAI,OAAO,WAAW;IACpC,GACC,EAAE,CAAC,MAAM,YACT,MAAM,GACN,MAAM;IAET,IAAI,aAAa;QACf,OAAO;YAAE,MAAM;YAAM,OAAO;QAAY;IAC1C;IAEA,mBAAmB;IACnB,IAAI,aAAa,SAAS,IAAI,aAAa,SAAS,CAAC,MAAM,GAAG,GAAG;QAC/D,4BAA4B;QAC5B,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,sBACL,MAAM,GACN,EAAE,CAAC,eAAe;QAErB,uBAAuB;QACvB,MAAM,kBAAkB,aAAa,SAAS,CAAC,GAAG,CAAC,CAAC,aAAuB,CAAC;gBAC1E,aAAa;gBACb,aAAa;YACf,CAAC;QAED,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,sBACL,MAAM,CAAC;IACZ;IAEA,oBAAoB;IACpB,IAAI,aAAa,UAAU,IAAI,aAAa,UAAU,CAAC,MAAM,GAAG,GAAG;QACjE,6BAA6B;QAC7B,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,uBACL,MAAM,GACN,EAAE,CAAC,eAAe;QAErB,wBAAwB;QACxB,MAAM,kBAAkB,aAAa,UAAU,CAAC,GAAG,CAAC,CAAC,aAAuB,CAAC;gBAC3E,aAAa;gBACb,aAAa;YACf,CAAC;QAED,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,uBACL,MAAM,CAAC;IACZ;IAEA,OAAO;QAAE,MAAM;QAAiB,OAAO;IAAK;AAC9C;AAGO,eAAe,qBAAqB,UAAkB,EAAE,SAAsD;IACnH,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IAEtD,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,MAAM;YAAM,OAAO;gBAAE,SAAS;YAAyB;QAAE;IACpE;IAEA,kDAAkD;IAClD,MAAM,EAAE,MAAM,gBAAgB,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACjE,IAAI,CAAC,aACL,MAAM,CAAC,2BACP,EAAE,CAAC,MAAM,YACT,EAAE,CAAC,eAAe,KAAK,EAAE,EACzB,MAAM;IAET,IAAI,cAAc,CAAC,kBAAkB;QACnC,OAAO;YAAE,MAAM;YAAM,OAAO;gBAAE,SAAS;YAAsC;QAAE;IACjF;IAEA,mBAAmB;IACnB,IAAI,iBAAiB,MAAM,KAAK,YAAY,cAAc,SAAS;QACjE,OAAO;YAAE,MAAM;YAAM,OAAO;gBAAE,SAAS;YAA8C;QAAE;IACzF;IAEA,IAAI,iBAAiB,MAAM,KAAK,aAAa;QAC3C,OAAO;YAAE,MAAM;YAAM,OAAO;gBAAE,SAAS;YAA6C;QAAE;IACxF;IAEA,gBAAgB;IAChB,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACjE,IAAI,CAAC,aACL,MAAM,CAAC;QACN,QAAQ;QACR,YAAY,IAAI,OAAO,WAAW;IACpC,GACC,EAAE,CAAC,MAAM,YACT,MAAM,GACN,MAAM;IAET,IAAI,aAAa;QACf,OAAO;YAAE,MAAM;YAAM,OAAO;QAAY;IAC1C;IAEA,OAAO;QAAE,MAAM;QAAiB,OAAO;IAAK;AAC9C;AAGO,eAAe;IACpB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IAEtD,IAAI,CAAC,MAAM;QACT,QAAQ,GAAG,CAAC;QACZ,OAAO;YAAE,MAAM,EAAE;YAAE,OAAO;gBAAE,SAAS;YAAyB;QAAE;IAClE;IAEA,QAAQ,GAAG,CAAC,mCAAmC,KAAK,EAAE;IAEtD,MAAM,EAAE,MAAM,SAAS,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC9C,IAAI,CAAC,aACL,MAAM,CAAC,CAAC;;;;;;;;;IAST,CAAC,EACA,EAAE,CAAC,eAAe,KAAK,EAAE,EACzB,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,QAAQ,GAAG,CAAC,2BAA2B;QAAE;QAAW;IAAM;IAE1D,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;YAAE,MAAM,EAAE;YAAE;QAAM;IAC3B;IAEA,OAAO;QAAE,MAAM,aAAa,EAAE;QAAE,OAAO;IAAK;AAC9C;AAGO,eAAe,qBAAqB,QAAgB,CAAC;IAC1D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,KACP,EAAE,CAAC,UAAU,UACb,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM,GACvC,KAAK,CAAC;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,6BAA6B;IAC7C;IAEA,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,eAAe,0BAA0B,WAAsC;IACpF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yBACL,MAAM,CAAC,aACP,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,eAAe,qBAAqB,UAAkB,EAAE,YAAoB;IACjF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yBACL,MAAM,CAAC,0BACP,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,iBAAiB,cACpB,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,wBAAwB,UAAkB,EAAE,MAAe;IAC/E,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,yBACL,MAAM,CAAC,CAAC;;;;;;;;;;;;IAYT,CAAC,EACA,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,IAAI,QAAQ;QACV,QAAQ,MAAM,EAAE,CAAC,UAAU;IAC7B;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAC9B,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,0BAA0B,YAAoB,EAAE,MAAe;IACnF,IAAI;QACF,QAAQ,GAAG,CAAC,yCAAyC;QAErD,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,yBACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;MAsBT,CAAC,EACA,EAAE,CAAC,iBAAiB,cACpB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,QAAQ;YACV,QAAQ,MAAM,EAAE,CAAC,UAAU;QAC7B;QAEA,MAAM,EAAE,MAAM,gBAAgB,EAAE,OAAO,iBAAiB,EAAE,GAAG,MAAM;QAEnE,IAAI,mBAAmB;YACrB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;QAEA,QAAQ,GAAG,CAAC,sBAAsB;QAElC,IAAI,CAAC,oBAAoB,iBAAiB,MAAM,KAAK,GAAG;YACtD,OAAO;gBAAE,MAAM,EAAE;gBAAE,OAAO;YAAK;QACjC;QAEA,+BAA+B;QAC/B,MAAM,cAAc,iBAAiB,GAAG,CAAC,CAAA,MAAO,IAAI,SAAS,CAAC,WAAW;QACzE,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACpE,IAAI,CAAC,cACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,EAAE,CAAC,MAAM;QAEZ,IAAI,iBAAiB;YACnB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;QAEA,wBAAwB;QACxB,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAChE,IAAI,CAAC,YACL,MAAM,CAAC,4BACP,EAAE,CAAC,MAAM;QAEZ,IAAI,eAAe;YACjB,QAAQ,KAAK,CAAC,qCAAqC;QACrD;QAEA,QAAQ,GAAG,CAAC,oBAAoB;QAChC,QAAQ,GAAG,CAAC,kBAAkB;QAE9B,6CAA6C;QAC7C,MAAM,kBAAkB,iBAAiB,GAAG,CAAC,CAAA;YAC3C,MAAM,WAAW,gBAAgB,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,IAAI,SAAS,CAAC,WAAW;YAC7E,MAAM,UAAU,cAAc,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,IAAI,SAAS,CAAC,WAAW;YAE1E,OAAO;gBACL,IAAI,IAAI,EAAE;gBACV,aAAa,IAAI,WAAW;gBAC5B,QAAQ,IAAI,MAAM;gBAClB,eAAe,IAAI,aAAa;gBAChC,eAAe,IAAI,aAAa;gBAChC,iBAAiB,IAAI,eAAe;gBACpC,oBAAoB,IAAI,kBAAkB;gBAC1C,qBAAqB,IAAI,mBAAmB;gBAC5C,YAAY,IAAI,UAAU;gBAC1B,WAAW;oBACT,OAAO,IAAI,SAAS,CAAC,KAAK;oBAC1B,aAAa,IAAI,SAAS,CAAC,WAAW;oBACtC,QAAQ,IAAI,SAAS,CAAC,MAAM;oBAC5B,QAAQ,IAAI,SAAS,CAAC,MAAM;oBAC5B,YAAY;wBACV,cAAc,UAAU,gBAAgB;wBACxC,UAAU;4BACR,UAAU,SAAS,YAAY;4BAC/B,YAAY,SAAS,cAAc;wBACrC;oBACF;gBACF;YACF;QACF;QAEA,OAAO;YAAE,MAAM;YAAiB,OAAO;QAAK;IAC9C,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;YAAE,MAAM;YAAM,OAAO,MAAM,OAAO;QAAC;IAC5C;AACF;AAKO,eAAe,qBAAqB,UAAkB,EAAE,SAK7D;IACA,MAAM,eAAe,UAAU,GAAG,CAAC,CAAA,WAAY,CAAC;YAC9C,aAAa;YACb,GAAG,QAAQ;QACb,CAAC;IAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,sBACL,MAAM,CAAC,cACP,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,sBAAsB,UAAkB,EAAE,WAAqB;IACnF,MAAM,eAAe,YAAY,GAAG,CAAC,CAAA,aAAc,CAAC;YAClD,aAAa;YACb,aAAa;QACf,CAAC;IAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,uBACL,MAAM,CAAC,cACP,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,wBAAwB,UAAkB;IAC9D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,sBACL,MAAM,GACN,EAAE,CAAC,eAAe;IAErB,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,yBAAyB,UAAkB;IAC/D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,uBACL,MAAM,GACN,EAAE,CAAC,eAAe;IAErB,OAAO;QAAE;QAAM;IAAM;AACvB;AAIO,eAAe;IACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,GAAG,CAAC;IAEP,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,eAAe,iBAAiB,UAAkB;IACvD,MAAM,EAAE,MAAM,SAAS,EAAE,OAAO,cAAc,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC9D,IAAI,CAAC,aACL,MAAM,CAAC,UACP,EAAE,CAAC,eAAe;IAErB,IAAI,gBAAgB,OAAO;QAAE,MAAM;QAAM,OAAO;IAAe;IAE/D,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,iBAAiB,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACpE,IAAI,CAAC,yBACL,MAAM,CAAC,uBACP,EAAE,CAAC,eAAe,WAAW,IAAI,CAAA,IAAK,EAAE,EAAE,KAAK,EAAE;IAEpD,IAAI,mBAAmB,OAAO;QAAE,MAAM;QAAM,OAAO;IAAkB;IAErE,MAAM,QAAQ;QACZ,gBAAgB,WAAW,UAAU;QACrC,iBAAiB,WAAW,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,UAAU;QACzE,oBAAoB,WAAW,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,UAAU;QAC/E,mBAAmB,cAAc,UAAU;QAC3C,qBAAqB,cAAc,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,UAAU;QACjF,sBAAsB,cAAc,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,UAAU;IACrF;IAEA,OAAO;QAAE,MAAM;QAAO,OAAO;IAAK;AACpC;AAKO,eAAe,gCAAgC,UAAkB,EAAE,MAAe;IACvF,IAAI;QACF,QAAQ,GAAG,CAAC,uCAAuC;QAEnD,8DAA8D;QAC9D,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,yBACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;MAoBT,CAAC,EACA,EAAE,CAAC,yBAAyB,YAC5B,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,QAAQ;YACV,QAAQ,MAAM,EAAE,CAAC,UAAU;QAC7B;QAEA,MAAM,EAAE,MAAM,gBAAgB,EAAE,OAAO,iBAAiB,EAAE,GAAG,MAAM;QAEnE,IAAI,mBAAmB;YACrB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;QAEA,QAAQ,GAAG,CAAC,sBAAsB;QAElC,IAAI,CAAC,oBAAoB,iBAAiB,MAAM,KAAK,GAAG;YACtD,OAAO;gBAAE,MAAM,EAAE;gBAAE,OAAO;YAAK;QACjC;QAEA,2CAA2C;QAC3C,MAAM,gBAAgB,iBAAiB,GAAG,CAAC,CAAA,MAAO,IAAI,aAAa;QACnE,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,gBAAgB,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACtE,IAAI,CAAC,eACL,MAAM,CAAC,CAAC;;;;;;;;MAQT,CAAC,EACA,EAAE,CAAC,MAAM;QAEZ,IAAI,kBAAkB;YACpB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;QAEA,QAAQ,GAAG,CAAC,qBAAqB;QAEjC,6CAA6C;QAC7C,MAAM,kBAAkB,kBAAkB,IAAI,CAAA;YAC5C,MAAM,aAAa,iBAAiB,KAAK,CAAA,MAAO,IAAI,EAAE,KAAK,IAAI,aAAa;YAC5E,OAAO;gBACL,IAAI,IAAI,EAAE;gBACV,aAAa,IAAI,WAAW;gBAC5B,eAAe,IAAI,aAAa;gBAChC,QAAQ,IAAI,MAAM;gBAClB,eAAe,IAAI,aAAa;gBAChC,eAAe,IAAI,aAAa;gBAChC,oBAAoB,IAAI,kBAAkB,IAAI;gBAC9C,iBAAiB,IAAI,eAAe,IAAI,EAAE;gBAC1C,qBAAqB,IAAI,mBAAmB,IAAI,IAAI,aAAa;gBACjE,mBAAmB,IAAI,iBAAiB,IAAI;gBAC5C,qBAAqB,IAAI,mBAAmB,IAAI;gBAChD,sBAAsB,IAAI,oBAAoB;gBAC9C,YAAY,IAAI,UAAU;gBAC1B,WAAW;oBACT,IAAI,IAAI,SAAS,CAAC,EAAE;oBACpB,OAAO,IAAI,SAAS,CAAC,KAAK;oBAC1B,QAAQ,IAAI,SAAS,CAAC,MAAM;oBAC5B,aAAa,IAAI,SAAS,CAAC,WAAW;gBACxC;gBACA,UAAU;oBACR,IAAI,YAAY,UAAU,MAAM,IAAI,aAAa;oBACjD,UAAU,YAAY,UAAU,YAAY;oBAC5C,WAAW,YAAY,UAAU,aAAa;oBAC9C,YAAY,YAAY,UAAU,cAAc;gBAClD;YACF;QACF;QAEA,OAAO;YAAE,MAAM;YAAiB,OAAO;QAAK;IAC9C,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,yCAAyC;QACvD,OAAO;YAAE,MAAM;YAAM,OAAO,MAAM,OAAO;QAAC;IAC5C;AACF;AAGO,eAAe,uBAAuB,aAAqB;IAChE,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yBACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;MA2BT,CAAC,EACA,EAAE,CAAC,MAAM,eACT,MAAM;QAET,IAAI,OAAO,MAAM;QAEjB,0CAA0C;QAC1C,MAAM,EAAE,MAAM,cAAc,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC5C,IAAI,CAAC,yBACL,MAAM,CAAC,oBACP,EAAE,CAAC,iBAAiB,KAAK,aAAa;QAEzC,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC3C,IAAI,CAAC,wBACL,MAAM,CAAC,CAAC;;;;MAIT,CAAC,EACA,EAAE,CAAC,iBAAiB,KAAK,aAAa;QAEzC,iBAAiB;QACjB,MAAM,kBAAkB;YACtB,IAAI,KAAK,EAAE;YACX,aAAa,KAAK,WAAW;YAC7B,eAAe,KAAK,aAAa;YACjC,QAAQ,KAAK,MAAM;YACnB,gBAAgB,KAAK,cAAc;YACnC,oBAAoB,GAAG,KAAK,uBAAuB,CAAC,KAAK,CAAC;YAC1D,iBAAiB,KAAK,cAAc,IAAI,EAAE;YAC1C,qBAAqB,KAAK,OAAO;YACjC,mBAAmB,KAAK,mBAAmB,IAAI;YAC/C,qBAAqB,EAAE;YACvB,YAAY,KAAK,UAAU;YAC3B,UAAU;gBACR,IAAI,KAAK,SAAS,CAAC,EAAE;gBACrB,OAAO,KAAK,SAAS,CAAC,KAAK;gBAC3B,aAAa,KAAK,SAAS,CAAC,WAAW;gBACvC,QAAQ,KAAK,SAAS,CAAC,MAAM;gBAC7B,cAAc,KAAK,SAAS,CAAC,YAAY;gBACzC,cAAc,KAAK,SAAS,CAAC,YAAY;YAC3C;YACA,YAAY;gBACV,IAAI,KAAK,WAAW,CAAC,EAAE;gBACvB,WAAW,KAAK,WAAW,CAAC,SAAS;gBACrC,UAAU,KAAK,WAAW,CAAC,QAAQ;gBACnC,YAAY,KAAK,WAAW,CAAC,UAAU;gBACvC,KAAK,KAAK,WAAW,CAAC,GAAG;gBACzB,iBAAiB,KAAK,WAAW,CAAC,eAAe;gBACjD,YAAY,gBAAgB,IAAI,CAAA,IAAK,EAAE,UAAU,EAAE,MAAM,OAAO,YAAY,EAAE;gBAC9E,WAAW,eAAe,IAAI,CAAA,IAAK,CAAC;wBAClC,eAAe,EAAE,SAAS,EAAE,QAAQ;wBACpC,QAAQ,EAAE,MAAM;wBAChB,iBAAiB,EAAE,eAAe;oBACpC,CAAC,MAAM,EAAE;YACX;QACF;QAEA,OAAO;YAAE,MAAM;YAAiB,OAAO;QAAK;IAC9C,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO;YAAE,MAAM;YAAM,OAAO,MAAM,OAAO;QAAC;IAC5C;AACF;AAGO,eAAe,wBACpB,aAAqB,EACrB,MAA+B,EAC/B,eAAwB;IAExB,IAAI;QACF,MAAM,aAAkB;YAAE;QAAO;QAEjC,IAAI,WAAW,cAAc,iBAAiB;YAC5C,WAAW,gBAAgB,GAAG;QAChC;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,eACT,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,MAAM;QAET,IAAI,OAAO,MAAM;QAEjB,mDAAmD;QACnD,IAAI,WAAW,cAAc,MAAM;YACjC,IAAI;gBACF,8CAA8C;gBAC9C,MAAM,CAAA,GAAA,iIAAA,CAAA,kCAA+B,AAAD,EAClC,KAAK,SAAS,CAAC,WAAW,EAC1B,KAAK,aAAa,EAClB,KAAK,EAAE,EACP,MACA,MAAO,kDAAkD;;YAE7D,EAAE,OAAO,WAAW;gBAClB,QAAQ,KAAK,CAAC,mCAAmC;YACjD,iDAAiD;YACnD;QACF;QAEA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;YAAE,MAAM;YAAM,OAAO,MAAM,OAAO;QAAC;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 1208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/profiles.ts"], "sourcesContent": ["import { supabase } from './supabase';\nimport { Database } from './database.types';\n\ntype Profile = Database['public']['Tables']['profiles']['Row'];\ntype ProfileInsert = Database['public']['Tables']['profiles']['Insert'];\ntype ProfileUpdate = Database['public']['Tables']['profiles']['Update'];\n\ntype Influencer = Database['public']['Tables']['influencers']['Row'];\ntype InfluencerInsert = Database['public']['Tables']['influencers']['Insert'];\ntype InfluencerUpdate = Database['public']['Tables']['influencers']['Update'];\n\ntype Business = Database['public']['Tables']['businesses']['Row'];\ntype BusinessInsert = Database['public']['Tables']['businesses']['Insert'];\ntype BusinessUpdate = Database['public']['Tables']['businesses']['Update'];\n\n// Profile functions\nexport const getProfile = async (userId: string) => {\n  const { data, error } = await supabase\n    .from('profiles')\n    .select('*')\n    .eq('id', userId)\n    .single();\n  \n  return { data, error };\n};\n\nexport const updateProfile = async (userId: string, updates: ProfileUpdate) => {\n  const { data, error } = await supabase\n    .from('profiles')\n    .update(updates)\n    .eq('id', userId)\n    .select()\n    .single();\n\n  return { data, error };\n};\n\nexport const createProfile = async (profileData: ProfileInsert) => {\n  const { data, error } = await supabase\n    .from('profiles')\n    .insert(profileData)\n    .select()\n    .single();\n\n  return { data, error };\n};\n\n// Get public influencer profile by username\nexport const getPublicInfluencerProfile = async (username: string) => {\n  // Use RPC function to get influencer data\n  const { data: influencerData, error: influencerError } = await supabase\n    .rpc('get_influencers_with_details', {\n      search_term: username,\n      min_followers: 0,\n      max_followers: 999999999,\n      min_price: 0,\n      max_price: 999999,\n      platform_filter: '',\n      category_filter: '',\n      location_filter: '',\n      limit_count: 10\n    });\n\n  if (influencerError || !influencerData || influencerData.length === 0) {\n    return { data: null, error: influencerError || { message: 'Influencer not found' } };\n  }\n\n  // Find the exact username match\n  const exactMatch = influencerData.find(item => item.username === username);\n  if (!exactMatch) {\n    return { data: null, error: { message: 'Influencer not found' } };\n  }\n\n  const data = exactMatch;\n\n  // Transform data to match expected structure\n  const transformedData = {\n    id: data.id,\n    username: data.username,\n    full_name: data.full_name,\n    avatar_url: data.avatar_url,\n    bio: data.bio,\n    location: data.location,\n    created_at: data.created_at,\n    gender: data.gender,\n    age: data.age,\n    is_verified: data.is_verified,\n    platforms: [\n      ...(data.instagram_followers > 0 ? [{\n        platform_id: 1,\n        platform_name: 'Instagram',\n        platform_icon: '📷',\n        handle: `@${data.username}`,\n        followers_count: data.instagram_followers,\n        is_verified: data.is_verified\n      }] : []),\n      ...(data.tiktok_followers > 0 ? [{\n        platform_id: 2,\n        platform_name: 'TikTok',\n        platform_icon: '🎵',\n        handle: `@${data.username}`,\n        followers_count: data.tiktok_followers,\n        is_verified: false\n      }] : []),\n      ...(data.youtube_subscribers > 0 ? [{\n        platform_id: 3,\n        platform_name: 'YouTube',\n        platform_icon: '📺',\n        handle: `@${data.username}`,\n        followers_count: data.youtube_subscribers,\n        is_verified: false\n      }] : [])\n    ],\n    categories: [], // TODO: Add categories when implemented\n    pricing: [\n      ...(data.price_per_post ? [{\n        platform_id: 1,\n        platform_name: 'Instagram',\n        platform_icon: '📷',\n        content_type_id: 1,\n        content_type_name: 'Post',\n        price: Number(data.price_per_post),\n        currency: 'KM'\n      }] : []),\n      ...(data.price_per_story ? [{\n        platform_id: 1,\n        platform_name: 'Instagram',\n        platform_icon: '📷',\n        content_type_id: 2,\n        content_type_name: 'Story',\n        price: Number(data.price_per_story),\n        currency: 'KM'\n      }] : []),\n      ...(data.price_per_reel ? [{\n        platform_id: 1,\n        platform_name: 'Instagram',\n        platform_icon: '📷',\n        content_type_id: 3,\n        content_type_name: 'Reel',\n        price: Number(data.price_per_reel),\n        currency: 'KM'\n      }] : [])\n    ],\n    portfolio_items: [], // TODO: Add portfolio items when implemented\n    total_followers: (data.instagram_followers || 0) +\n                    (data.tiktok_followers || 0) +\n                    (data.youtube_subscribers || 0),\n    min_price: Math.min(\n      ...[data.price_per_post, data.price_per_story, data.price_per_reel]\n        .filter(Boolean)\n        .map(Number)\n    ) || 0,\n    max_price: Math.max(\n      ...[data.price_per_post, data.price_per_story, data.price_per_reel]\n        .filter(Boolean)\n        .map(Number)\n    ) || 0\n  };\n\n  return { data: transformedData, error: null };\n};\n\nexport const upsertProfile = async (userId: string, updates: ProfileUpdate) => {\n  // First try to get existing profile\n  const { data: existingProfile } = await getProfile(userId);\n\n  if (existingProfile) {\n    // Profile exists, update it\n    return updateProfile(userId, updates);\n  } else {\n    // Profile doesn't exist, create it\n    const profileData: ProfileInsert = {\n      id: userId,\n      user_type: updates.user_type || 'influencer',\n      username: updates.username || null,\n      full_name: updates.full_name || null,\n      avatar_url: updates.avatar_url || null,\n      bio: updates.bio || null,\n      website_url: updates.website_url || null,\n      location: updates.location || null,\n    };\n    return createProfile(profileData);\n  }\n};\n\nexport const checkUsernameAvailable = async (username: string, excludeUserId?: string) => {\n  let query = supabase\n    .from('profiles')\n    .select('id')\n    .eq('username', username);\n  \n  if (excludeUserId) {\n    query = query.neq('id', excludeUserId);\n  }\n  \n  const { data, error } = await query;\n  \n  if (error) return { available: false, error };\n  return { available: data.length === 0, error: null };\n};\n\n// Influencer functions\nexport const getInfluencer = async (userId: string) => {\n  const { data, error } = await supabase\n    .from('influencers')\n    .select(`\n      *,\n      profiles (*)\n    `)\n    .eq('id', userId)\n    .single();\n  \n  return { data, error };\n};\n\nexport const createInfluencer = async (influencerData: InfluencerInsert) => {\n  const { data, error } = await supabase\n    .from('influencers')\n    .insert(influencerData)\n    .select()\n    .single();\n  \n  return { data, error };\n};\n\nexport const updateInfluencer = async (userId: string, updates: InfluencerUpdate) => {\n  const { data, error } = await supabase\n    .from('influencers')\n    .update(updates)\n    .eq('id', userId)\n    .select()\n    .single();\n\n  return { data, error };\n};\n\nexport const getInfluencers = async (filters?: {\n  niche?: string;\n  minFollowers?: number;\n  maxFollowers?: number;\n  location?: string;\n  limit?: number;\n  offset?: number;\n}) => {\n  let query = supabase\n    .from('influencers')\n    .select(`\n      *,\n      profiles (*)\n    `);\n\n  if (filters?.niche) {\n    query = query.ilike('niche', `%${filters.niche}%`);\n  }\n\n  if (filters?.minFollowers) {\n    query = query.gte('instagram_followers', filters.minFollowers);\n  }\n\n  if (filters?.maxFollowers) {\n    query = query.lte('instagram_followers', filters.maxFollowers);\n  }\n\n  if (filters?.location) {\n    query = query.eq('profiles.location', filters.location);\n  }\n\n  if (filters?.limit) {\n    query = query.limit(filters.limit);\n  }\n\n  if (filters?.offset) {\n    query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);\n  }\n\n  const { data, error } = await query;\n  return { data, error };\n};\n\n// Business functions\nexport const getBusiness = async (userId: string) => {\n  const { data, error } = await supabase\n    .from('businesses')\n    .select(`\n      *,\n      profiles (*)\n    `)\n    .eq('id', userId)\n    .single();\n  \n  return { data, error };\n};\n\nexport const createBusiness = async (businessData: BusinessInsert) => {\n  const { data, error } = await supabase\n    .from('businesses')\n    .insert(businessData)\n    .select()\n    .single();\n  \n  return { data, error };\n};\n\nexport const updateBusiness = async (userId: string, updates: BusinessUpdate) => {\n  const { data, error } = await supabase\n    .from('businesses')\n    .update(updates)\n    .eq('id', userId)\n    .select()\n    .single();\n\n  return { data, error };\n};\n\n// Category functions\nexport const getCategories = async () => {\n  const { data, error } = await supabase\n    .from('categories')\n    .select('*')\n    .order('name');\n\n  return { data, error };\n};\n\nexport const getInfluencerCategories = async (influencerId: string) => {\n  const { data, error } = await supabase\n    .from('influencer_categories')\n    .select(`\n      category_id,\n      is_primary,\n      categories (*)\n    `)\n    .eq('influencer_id', influencerId);\n\n  return { data, error };\n};\n\nexport const updateInfluencerCategories = async (influencerId: string, categoryIds: number[]) => {\n  // First, delete existing categories\n  await supabase\n    .from('influencer_categories')\n    .delete()\n    .eq('influencer_id', influencerId);\n\n  // Then insert new categories\n  if (categoryIds.length > 0) {\n    const categoryData = categoryIds.map((categoryId, index) => ({\n      influencer_id: influencerId,\n      category_id: categoryId,\n      is_primary: index === 0 // First category is primary\n    }));\n\n    const { data, error } = await supabase\n      .from('influencer_categories')\n      .insert(categoryData)\n      .select();\n\n    return { data, error };\n  }\n\n  return { data: [], error: null };\n};\n\nexport const getBusinessTargetCategories = async (businessId: string) => {\n  const { data, error } = await supabase\n    .from('business_target_categories')\n    .select(`\n      category_id,\n      categories (*)\n    `)\n    .eq('business_id', businessId);\n\n  return { data, error };\n};\n\nexport const updateBusinessTargetCategories = async (businessId: string, categoryIds: number[]) => {\n  // First, delete existing categories\n  await supabase\n    .from('business_target_categories')\n    .delete()\n    .eq('business_id', businessId);\n\n  // Then insert new categories\n  if (categoryIds.length > 0) {\n    const categoryData = categoryIds.map(categoryId => ({\n      business_id: businessId,\n      category_id: categoryId\n    }));\n\n    const { data, error } = await supabase\n      .from('business_target_categories')\n      .insert(categoryData)\n      .select();\n\n    return { data, error };\n  }\n\n  return { data: [], error: null };\n};\n\n// Combined profile functions\nexport const getFullProfile = async (userId: string) => {\n  const { data: profile, error: profileError } = await getProfile(userId);\n  \n  if (profileError || !profile) {\n    return { data: null, error: profileError };\n  }\n\n  if (profile.user_type === 'influencer') {\n    const { data: influencer, error: influencerError } = await getInfluencer(userId);\n    return { \n      data: influencer ? { ...profile, influencer } : profile, \n      error: influencerError \n    };\n  } else if (profile.user_type === 'business') {\n    const { data: business, error: businessError } = await getBusiness(userId);\n    return { \n      data: business ? { ...profile, business } : profile, \n      error: businessError \n    };\n  }\n\n  return { data: profile, error: null };\n};\n\n// Upload avatar function\nexport const uploadAvatar = async (userId: string, file: File) => {\n  const fileExt = file.name.split('.').pop();\n  const fileName = `${userId}-${Math.random()}.${fileExt}`;\n  const filePath = `avatars/${fileName}`;\n\n  const { error: uploadError } = await supabase.storage\n    .from('avatars')\n    .upload(filePath, file);\n\n  if (uploadError) {\n    return { data: null, error: uploadError };\n  }\n\n  const { data } = supabase.storage\n    .from('avatars')\n    .getPublicUrl(filePath);\n\n  // Update profile with new avatar URL\n  const { error: updateError } = await updateProfile(userId, {\n    avatar_url: data.publicUrl,\n  });\n\n  if (updateError) {\n    return { data: null, error: updateError };\n  }\n\n  return { data: data.publicUrl, error: null };\n};\n\n// Platform and Pricing functions\nexport const getInfluencerPlatforms = async (influencerId: string) => {\n  const { data, error } = await supabase\n    .from('influencer_platforms')\n    .select(`\n      *,\n      platforms (*)\n    `)\n    .eq('influencer_id', influencerId)\n    .eq('is_active', true);\n\n  return { data, error };\n};\n\nexport const getInfluencerPricing = async (influencerId: string) => {\n  const { data, error } = await supabase\n    .from('influencer_platform_pricing')\n    .select(`\n      *,\n      platforms (*),\n      content_types (*)\n    `)\n    .eq('influencer_id', influencerId)\n    .eq('is_available', true);\n\n  return { data, error };\n};\n\nexport const updateInfluencerPlatforms = async (influencerId: string, platforms: any[]) => {\n  // First, delete existing platforms\n  await supabase\n    .from('influencer_platforms')\n    .delete()\n    .eq('influencer_id', influencerId);\n\n  // Then insert new platforms\n  if (platforms.length > 0) {\n    const platformData = platforms.map(platform => ({\n      influencer_id: influencerId,\n      platform_id: platform.platform_id,\n      handle: platform.handle || null,\n      followers_count: platform.followers_count || 0,\n      is_verified: false,\n      is_active: true\n    }));\n\n    const { data, error } = await supabase\n      .from('influencer_platforms')\n      .insert(platformData)\n      .select();\n\n    return { data, error };\n  }\n\n  return { data: [], error: null };\n};\n\nexport const updateInfluencerPricing = async (influencerId: string, pricing: any[]) => {\n  // First, delete existing pricing\n  await supabase\n    .from('influencer_platform_pricing')\n    .delete()\n    .eq('influencer_id', influencerId);\n\n  // Then insert new pricing\n  if (pricing.length > 0) {\n    const pricingData = pricing.map(price => ({\n      influencer_id: influencerId,\n      platform_id: price.platform_id,\n      content_type_id: price.content_type_id,\n      price: price.price,\n      currency: 'KM',\n      is_available: price.is_available !== false\n    }));\n\n    const { data, error } = await supabase\n      .from('influencer_platform_pricing')\n      .insert(pricingData)\n      .select();\n\n    return { data, error };\n  }\n\n  return { data: [], error: null };\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAgBO,MAAM,aAAa,OAAO;IAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,gBAAgB,OAAO,QAAgB;IAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,gBAAgB,OAAO;IAClC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,aACP,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,MAAM,6BAA6B,OAAO;IAC/C,0CAA0C;IAC1C,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACpE,GAAG,CAAC,gCAAgC;QACnC,aAAa;QACb,eAAe;QACf,eAAe;QACf,WAAW;QACX,WAAW;QACX,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,aAAa;IACf;IAEF,IAAI,mBAAmB,CAAC,kBAAkB,eAAe,MAAM,KAAK,GAAG;QACrE,OAAO;YAAE,MAAM;YAAM,OAAO,mBAAmB;gBAAE,SAAS;YAAuB;QAAE;IACrF;IAEA,gCAAgC;IAChC,MAAM,aAAa,eAAe,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;IACjE,IAAI,CAAC,YAAY;QACf,OAAO;YAAE,MAAM;YAAM,OAAO;gBAAE,SAAS;YAAuB;QAAE;IAClE;IAEA,MAAM,OAAO;IAEb,6CAA6C;IAC7C,MAAM,kBAAkB;QACtB,IAAI,KAAK,EAAE;QACX,UAAU,KAAK,QAAQ;QACvB,WAAW,KAAK,SAAS;QACzB,YAAY,KAAK,UAAU;QAC3B,KAAK,KAAK,GAAG;QACb,UAAU,KAAK,QAAQ;QACvB,YAAY,KAAK,UAAU;QAC3B,QAAQ,KAAK,MAAM;QACnB,KAAK,KAAK,GAAG;QACb,aAAa,KAAK,WAAW;QAC7B,WAAW;eACL,KAAK,mBAAmB,GAAG,IAAI;gBAAC;oBAClC,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,QAAQ,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;oBAC3B,iBAAiB,KAAK,mBAAmB;oBACzC,aAAa,KAAK,WAAW;gBAC/B;aAAE,GAAG,EAAE;eACH,KAAK,gBAAgB,GAAG,IAAI;gBAAC;oBAC/B,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,QAAQ,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;oBAC3B,iBAAiB,KAAK,gBAAgB;oBACtC,aAAa;gBACf;aAAE,GAAG,EAAE;eACH,KAAK,mBAAmB,GAAG,IAAI;gBAAC;oBAClC,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,QAAQ,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;oBAC3B,iBAAiB,KAAK,mBAAmB;oBACzC,aAAa;gBACf;aAAE,GAAG,EAAE;SACR;QACD,YAAY,EAAE;QACd,SAAS;eACH,KAAK,cAAc,GAAG;gBAAC;oBACzB,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,OAAO,OAAO,KAAK,cAAc;oBACjC,UAAU;gBACZ;aAAE,GAAG,EAAE;eACH,KAAK,eAAe,GAAG;gBAAC;oBAC1B,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,OAAO,OAAO,KAAK,eAAe;oBAClC,UAAU;gBACZ;aAAE,GAAG,EAAE;eACH,KAAK,cAAc,GAAG;gBAAC;oBACzB,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,OAAO,OAAO,KAAK,cAAc;oBACjC,UAAU;gBACZ;aAAE,GAAG,EAAE;SACR;QACD,iBAAiB,EAAE;QACnB,iBAAiB,CAAC,KAAK,mBAAmB,IAAI,CAAC,IAC/B,CAAC,KAAK,gBAAgB,IAAI,CAAC,IAC3B,CAAC,KAAK,mBAAmB,IAAI,CAAC;QAC9C,WAAW,KAAK,GAAG,IACd;YAAC,KAAK,cAAc;YAAE,KAAK,eAAe;YAAE,KAAK,cAAc;SAAC,CAChE,MAAM,CAAC,SACP,GAAG,CAAC,YACJ;QACL,WAAW,KAAK,GAAG,IACd;YAAC,KAAK,cAAc;YAAE,KAAK,eAAe;YAAE,KAAK,cAAc;SAAC,CAChE,MAAM,CAAC,SACP,GAAG,CAAC,YACJ;IACP;IAEA,OAAO;QAAE,MAAM;QAAiB,OAAO;IAAK;AAC9C;AAEO,MAAM,gBAAgB,OAAO,QAAgB;IAClD,oCAAoC;IACpC,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,WAAW;IAEnD,IAAI,iBAAiB;QACnB,4BAA4B;QAC5B,OAAO,cAAc,QAAQ;IAC/B,OAAO;QACL,mCAAmC;QACnC,MAAM,cAA6B;YACjC,IAAI;YACJ,WAAW,QAAQ,SAAS,IAAI;YAChC,UAAU,QAAQ,QAAQ,IAAI;YAC9B,WAAW,QAAQ,SAAS,IAAI;YAChC,YAAY,QAAQ,UAAU,IAAI;YAClC,KAAK,QAAQ,GAAG,IAAI;YACpB,aAAa,QAAQ,WAAW,IAAI;YACpC,UAAU,QAAQ,QAAQ,IAAI;QAChC;QACA,OAAO,cAAc;IACvB;AACF;AAEO,MAAM,yBAAyB,OAAO,UAAkB;IAC7D,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,YACL,MAAM,CAAC,MACP,EAAE,CAAC,YAAY;IAElB,IAAI,eAAe;QACjB,QAAQ,MAAM,GAAG,CAAC,MAAM;IAC1B;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAE9B,IAAI,OAAO,OAAO;QAAE,WAAW;QAAO;IAAM;IAC5C,OAAO;QAAE,WAAW,KAAK,MAAM,KAAK;QAAG,OAAO;IAAK;AACrD;AAGO,MAAM,gBAAgB,OAAO;IAClC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,CAAC;;;IAGT,CAAC,EACA,EAAE,CAAC,MAAM,QACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,mBAAmB,OAAO;IACrC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,gBACP,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,mBAAmB,OAAO,QAAgB;IACrD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,iBAAiB,OAAO;IAQnC,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,eACL,MAAM,CAAC,CAAC;;;IAGT,CAAC;IAEH,IAAI,SAAS,OAAO;QAClB,QAAQ,MAAM,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAC,CAAC,CAAC;IACnD;IAEA,IAAI,SAAS,cAAc;QACzB,QAAQ,MAAM,GAAG,CAAC,uBAAuB,QAAQ,YAAY;IAC/D;IAEA,IAAI,SAAS,cAAc;QACzB,QAAQ,MAAM,GAAG,CAAC,uBAAuB,QAAQ,YAAY;IAC/D;IAEA,IAAI,SAAS,UAAU;QACrB,QAAQ,MAAM,EAAE,CAAC,qBAAqB,QAAQ,QAAQ;IACxD;IAEA,IAAI,SAAS,OAAO;QAClB,QAAQ,MAAM,KAAK,CAAC,QAAQ,KAAK;IACnC;IAEA,IAAI,SAAS,QAAQ;QACnB,QAAQ,MAAM,KAAK,CAAC,QAAQ,MAAM,EAAE,QAAQ,MAAM,GAAG,CAAC,QAAQ,KAAK,IAAI,EAAE,IAAI;IAC/E;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAC9B,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,MAAM,cAAc,OAAO;IAChC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,CAAC;;;IAGT,CAAC,EACA,EAAE,CAAC,MAAM,QACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,iBAAiB,OAAO;IACnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,cACP,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,iBAAiB,OAAO,QAAgB;IACnD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,MAAM,gBAAgB;IAC3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,KACP,KAAK,CAAC;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yBACL,MAAM,CAAC,CAAC;;;;IAIT,CAAC,EACA,EAAE,CAAC,iBAAiB;IAEvB,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,6BAA6B,OAAO,cAAsB;IACrE,oCAAoC;IACpC,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,yBACL,MAAM,GACN,EAAE,CAAC,iBAAiB;IAEvB,6BAA6B;IAC7B,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,MAAM,eAAe,YAAY,GAAG,CAAC,CAAC,YAAY,QAAU,CAAC;gBAC3D,eAAe;gBACf,aAAa;gBACb,YAAY,UAAU,EAAE,4BAA4B;YACtD,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yBACL,MAAM,CAAC,cACP,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,OAAO;QAAE,MAAM,EAAE;QAAE,OAAO;IAAK;AACjC;AAEO,MAAM,8BAA8B,OAAO;IAChD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,8BACL,MAAM,CAAC,CAAC;;;IAGT,CAAC,EACA,EAAE,CAAC,eAAe;IAErB,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,iCAAiC,OAAO,YAAoB;IACvE,oCAAoC;IACpC,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,8BACL,MAAM,GACN,EAAE,CAAC,eAAe;IAErB,6BAA6B;IAC7B,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,MAAM,eAAe,YAAY,GAAG,CAAC,CAAA,aAAc,CAAC;gBAClD,aAAa;gBACb,aAAa;YACf,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,8BACL,MAAM,CAAC,cACP,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,OAAO;QAAE,MAAM,EAAE;QAAE,OAAO;IAAK;AACjC;AAGO,MAAM,iBAAiB,OAAO;IACnC,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,WAAW;IAEhE,IAAI,gBAAgB,CAAC,SAAS;QAC5B,OAAO;YAAE,MAAM;YAAM,OAAO;QAAa;IAC3C;IAEA,IAAI,QAAQ,SAAS,KAAK,cAAc;QACtC,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,cAAc;QACzE,OAAO;YACL,MAAM,aAAa;gBAAE,GAAG,OAAO;gBAAE;YAAW,IAAI;YAChD,OAAO;QACT;IACF,OAAO,IAAI,QAAQ,SAAS,KAAK,YAAY;QAC3C,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,YAAY;QACnE,OAAO;YACL,MAAM,WAAW;gBAAE,GAAG,OAAO;gBAAE;YAAS,IAAI;YAC5C,OAAO;QACT;IACF;IAEA,OAAO;QAAE,MAAM;QAAS,OAAO;IAAK;AACtC;AAGO,MAAM,eAAe,OAAO,QAAgB;IACjD,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;IACxC,MAAM,WAAW,GAAG,OAAO,CAAC,EAAE,KAAK,MAAM,GAAG,CAAC,EAAE,SAAS;IACxD,MAAM,WAAW,CAAC,QAAQ,EAAE,UAAU;IAEtC,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAClD,IAAI,CAAC,WACL,MAAM,CAAC,UAAU;IAEpB,IAAI,aAAa;QACf,OAAO;YAAE,MAAM;YAAM,OAAO;QAAY;IAC1C;IAEA,MAAM,EAAE,IAAI,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAC9B,IAAI,CAAC,WACL,YAAY,CAAC;IAEhB,qCAAqC;IACrC,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,cAAc,QAAQ;QACzD,YAAY,KAAK,SAAS;IAC5B;IAEA,IAAI,aAAa;QACf,OAAO;YAAE,MAAM;YAAM,OAAO;QAAY;IAC1C;IAEA,OAAO;QAAE,MAAM,KAAK,SAAS;QAAE,OAAO;IAAK;AAC7C;AAGO,MAAM,yBAAyB,OAAO;IAC3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wBACL,MAAM,CAAC,CAAC;;;IAGT,CAAC,EACA,EAAE,CAAC,iBAAiB,cACpB,EAAE,CAAC,aAAa;IAEnB,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,uBAAuB,OAAO;IACzC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,+BACL,MAAM,CAAC,CAAC;;;;IAIT,CAAC,EACA,EAAE,CAAC,iBAAiB,cACpB,EAAE,CAAC,gBAAgB;IAEtB,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,4BAA4B,OAAO,cAAsB;IACpE,mCAAmC;IACnC,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,wBACL,MAAM,GACN,EAAE,CAAC,iBAAiB;IAEvB,4BAA4B;IAC5B,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,MAAM,eAAe,UAAU,GAAG,CAAC,CAAA,WAAY,CAAC;gBAC9C,eAAe;gBACf,aAAa,SAAS,WAAW;gBACjC,QAAQ,SAAS,MAAM,IAAI;gBAC3B,iBAAiB,SAAS,eAAe,IAAI;gBAC7C,aAAa;gBACb,WAAW;YACb,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wBACL,MAAM,CAAC,cACP,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,OAAO;QAAE,MAAM,EAAE;QAAE,OAAO;IAAK;AACjC;AAEO,MAAM,0BAA0B,OAAO,cAAsB;IAClE,iCAAiC;IACjC,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,+BACL,MAAM,GACN,EAAE,CAAC,iBAAiB;IAEvB,0BAA0B;IAC1B,IAAI,QAAQ,MAAM,GAAG,GAAG;QACtB,MAAM,cAAc,QAAQ,GAAG,CAAC,CAAA,QAAS,CAAC;gBACxC,eAAe;gBACf,aAAa,MAAM,WAAW;gBAC9B,iBAAiB,MAAM,eAAe;gBACtC,OAAO,MAAM,KAAK;gBAClB,UAAU;gBACV,cAAc,MAAM,YAAY,KAAK;YACvC,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,+BACL,MAAM,CAAC,aACP,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,OAAO;QAAE,MAAM,EAAE;QAAE,OAAO;IAAK;AACjC", "debugId": null}}, {"offset": {"line": 1701, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1871, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/navigation/DesktopNavigation.tsx"], "sourcesContent": ["'use client';\n\nimport { usePathname, useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Button } from '@/components/ui/button';\nimport { cn } from '@/lib/utils';\nimport {\n  LayoutDashboard,\n  User,\n  Settings,\n  Building2,\n  Users,\n  FileText,\n  MessageCircle,\n  DollarSign,\n  LogOut,\n  Menu,\n  Send,\n  Inbox\n} from 'lucide-react';\nimport { useState } from 'react';\nimport {\n  She<PERSON>,\n  SheetContent,\n  SheetHeader,\n  SheetTitle,\n  SheetTrigger,\n} from '@/components/ui/sheet';\n\ninterface SidebarItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  description?: string;\n}\n\ninterface DesktopNavigationProps {\n  userType: 'influencer' | 'business';\n  className?: string;\n}\n\nexport function DesktopNavigation({ userType, className }: DesktopNavigationProps) {\n  const pathname = usePathname();\n  const { signOut } = useAuth();\n  const router = useRouter();\n  const [isOpen, setIsOpen] = useState(false);\n\n  // Navigacija za influencer korisnike\n  const influencerNavigation: SidebarItem[] = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard/influencer',\n      icon: LayoutDashboard,\n      description: 'Pregled aktivnosti i statistika'\n    },\n    {\n      name: 'Moj račun',\n      href: '/dashboard/influencer/account',\n      icon: User,\n      description: 'Lični podaci i sigurnost'\n    },\n    {\n      name: 'Postavke profila',\n      href: '/dashboard/influencer/profile',\n      icon: Settings,\n      description: 'Javni profil i cijene'\n    },\n    {\n      name: 'Kampanje',\n      href: '/marketplace/campaigns',\n      icon: FileText,\n      description: 'Dostupne kampanje'\n    },\n    {\n      name: 'Ponude i aplikacije',\n      href: '/dashboard/influencer/offers',\n      icon: Inbox,\n      description: 'Direktne ponude i aplikacije'\n    },\n    {\n      name: 'Poruke',\n      href: '/dashboard/chat',\n      icon: MessageCircle,\n      description: 'Komunikacija sa brendovima'\n    },\n    {\n      name: 'Zarada',\n      href: '/dashboard/influencer/earnings',\n      icon: DollarSign,\n      description: 'Pregled zarade i isplate'\n    }\n  ];\n\n  // Navigacija za biznis korisnike\n  const businessNavigation: SidebarItem[] = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard/biznis',\n      icon: LayoutDashboard,\n      description: 'Pregled kampanja i statistika'\n    },\n    {\n      name: 'Moj račun',\n      href: '/dashboard/biznis/account',\n      icon: Building2,\n      description: 'Podaci o firmi i sigurnost'\n    },\n    {\n      name: 'Postavke profila',\n      href: '/dashboard/biznis/profile',\n      icon: Settings,\n      description: 'Javni profil firme'\n    },\n    {\n      name: 'Moje kampanje',\n      href: '/dashboard/campaigns',\n      icon: FileText,\n      description: 'Upravljanje kampanjama'\n    },\n    {\n      name: 'Aplikacije',\n      href: '/dashboard/biznis/applications',\n      icon: FileText,\n      description: 'Aplikacije na kampanje'\n    },\n    {\n      name: 'Moje ponude',\n      href: '/dashboard/biznis/offers',\n      icon: Send,\n      description: 'Direktne ponude influencerima'\n    },\n    {\n      name: 'Influenceri',\n      href: '/marketplace/influencers',\n      icon: Users,\n      description: 'Pronađi influencere'\n    },\n    {\n      name: 'Poruke',\n      href: '/dashboard/chat',\n      icon: MessageCircle,\n      description: 'Komunikacija sa influencerima'\n    }\n  ];\n\n  const navigation = userType === 'influencer' ? influencerNavigation : businessNavigation;\n\n  const handleSignOut = async () => {\n    try {\n      await signOut();\n      router.push('/');\n      setIsOpen(false);\n    } catch (error) {\n      console.error('Error signing out:', error);\n    }\n  };\n\n  return (\n    <div className={cn(\"hidden md:block\", className)}>\n      {/* Desktop Navigation Trigger */}\n      <Sheet open={isOpen} onOpenChange={setIsOpen}>\n        <SheetTrigger asChild>\n          <Button\n            variant=\"outline\"\n            size=\"icon\"\n            className=\"fixed top-4 left-4 z-40 bg-card border-border shadow-lg hover:bg-accent\"\n          >\n            <Menu className=\"h-4 w-4\" />\n            <span className=\"sr-only\">Otvori navigaciju</span>\n          </Button>\n        </SheetTrigger>\n        \n        <SheetContent side=\"left\" className=\"w-80 p-0\">\n          <div className=\"flex flex-col h-full\">\n            {/* Header */}\n            <SheetHeader className=\"p-6 border-b border-border\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-primary rounded-lg flex items-center justify-center\">\n                  <span className=\"text-primary-foreground font-bold text-lg\">🔗</span>\n                </div>\n                <SheetTitle className=\"text-lg font-bold text-foreground\">\n                  InfluConnect\n                </SheetTitle>\n              </div>\n            </SheetHeader>\n\n            {/* Navigation */}\n            <nav className=\"flex-1 px-4 py-6 space-y-2 overflow-y-auto\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href || pathname.startsWith(item.href + '/');\n                \n                return (\n                  <Link \n                    key={item.name} \n                    href={item.href}\n                    onClick={() => setIsOpen(false)}\n                  >\n                    <div className={cn(\n                      \"group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-colors\",\n                      isActive\n                        ? \"bg-primary text-primary-foreground\"\n                        : \"text-muted-foreground hover:bg-accent hover:text-accent-foreground\"\n                    )}>\n                      <item.icon className=\"flex-shrink-0 h-5 w-5 mr-3\" />\n                      <div className=\"flex-1\">\n                        <div className=\"font-medium\">{item.name}</div>\n                        {item.description && (\n                          <div className=\"text-xs opacity-75 mt-0.5\">\n                            {item.description}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </Link>\n                );\n              })}\n            </nav>\n\n            {/* Footer */}\n            <div className=\"p-4 border-t border-border\">\n              <Button\n                variant=\"ghost\"\n                onClick={handleSignOut}\n                className=\"w-full justify-start text-destructive hover:text-destructive hover:bg-destructive/10\"\n              >\n                <LogOut className=\"h-4 w-4 mr-2\" />\n                Odjava\n              </Button>\n            </div>\n          </div>\n        </SheetContent>\n      </Sheet>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AAtBA;;;;;;;;;;AA0CO,SAAS,kBAAkB,EAAE,QAAQ,EAAE,SAAS,EAA0B;IAC/E,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qCAAqC;IACrC,MAAM,uBAAsC;QAC1C;YACE,MAAM;YACN,MAAM;YACN,MAAM,4NAAA,CAAA,kBAAe;YACrB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,8MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;YACX,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,wNAAA,CAAA,gBAAa;YACnB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kNAAA,CAAA,aAAU;YAChB,aAAa;QACf;KACD;IAED,iCAAiC;IACjC,MAAM,qBAAoC;QACxC;YACE,MAAM;YACN,MAAM;YACN,MAAM,4NAAA,CAAA,kBAAe;YACrB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,gNAAA,CAAA,YAAS;YACf,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,8MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,8MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;YACX,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,wNAAA,CAAA,gBAAa;YACnB,aAAa;QACf;KACD;IAED,MAAM,aAAa,aAAa,eAAe,uBAAuB;IAEtE,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;YACZ,UAAU;QACZ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;kBAEpC,cAAA,8OAAC,iIAAA,CAAA,QAAK;YAAC,MAAM;YAAQ,cAAc;;8BACjC,8OAAC,iIAAA,CAAA,eAAY;oBAAC,OAAO;8BACnB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;8BAI9B,8OAAC,iIAAA,CAAA,eAAY;oBAAC,MAAK;oBAAO,WAAU;8BAClC,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,iIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;sDAE9D,8OAAC,iIAAA,CAAA,aAAU;4CAAC,WAAU;sDAAoC;;;;;;;;;;;;;;;;;0CAO9D,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,WAAW,aAAa,KAAK,IAAI,IAAI,SAAS,UAAU,CAAC,KAAK,IAAI,GAAG;oCAE3E,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,SAAS,IAAM,UAAU;kDAEzB,cAAA,8OAAC;4CAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,sFACA,WACI,uCACA;;8DAEJ,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;8DACrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAe,KAAK,IAAI;;;;;;wDACtC,KAAK,WAAW,kBACf,8OAAC;4DAAI,WAAU;sEACZ,KAAK,WAAW;;;;;;;;;;;;;;;;;;uCAfpB,KAAK,IAAI;;;;;gCAsBpB;;;;;;0CAIF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD", "debugId": null}}, {"offset": {"line": 2211, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/navigation/MobileBottomNavigation.tsx"], "sourcesContent": ["'use client';\n\nimport { usePathname } from 'next/navigation';\nimport Link from 'next/link';\nimport { cn } from '@/lib/utils';\nimport {\n  LayoutDashboard,\n  FileText,\n  Inbox,\n  MessageCircle,\n  Menu,\n  User,\n  Settings,\n  DollarSign,\n  LogOut\n} from 'lucide-react';\nimport { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport {\n  Sheet,\n  <PERSON>et<PERSON>ontent,\n  SheetHeader,\n  SheetTitle,\n  SheetTrigger,\n} from '@/components/ui/sheet';\nimport { Button } from '@/components/ui/button';\n\ninterface MobileBottomNavigationProps {\n  userType: 'influencer' | 'business';\n}\n\ninterface NavItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  label: string;\n}\n\ninterface MenuNavItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  description?: string;\n}\n\nexport function MobileBottomNavigation({ userType }: MobileBottomNavigationProps) {\n  const pathname = usePathname();\n  const { signOut } = useAuth();\n  const router = useRouter();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  // Glavne 4 ikonice za influencere\n  const influencerMainNav: NavItem[] = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard/influencer',\n      icon: LayoutDashboard,\n      label: 'Home'\n    },\n    {\n      name: 'Kampanje',\n      href: '/marketplace/campaigns',\n      icon: FileText,\n      label: 'Prilike'\n    },\n    {\n      name: 'Ponude',\n      href: '/dashboard/influencer/offers',\n      icon: Inbox,\n      label: 'Ponude'\n    },\n    {\n      name: 'Poruke',\n      href: '/dashboard/chat',\n      icon: MessageCircle,\n      label: 'Poruke'\n    }\n  ];\n\n  // Glavne 4 ikonice za biznis korisnike\n  const businessMainNav: NavItem[] = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard/biznis',\n      icon: LayoutDashboard,\n      label: 'Home'\n    },\n    {\n      name: 'Kampanje',\n      href: '/dashboard/campaigns',\n      icon: FileText,\n      label: 'Kampanje'\n    },\n    {\n      name: 'Aplikacije',\n      href: '/dashboard/biznis/applications',\n      icon: Inbox,\n      label: 'Aplikacije'\n    },\n    {\n      name: 'Poruke',\n      href: '/dashboard/chat',\n      icon: MessageCircle,\n      label: 'Poruke'\n    }\n  ];\n\n  // Ostali elementi za hamburger meni - influenceri\n  const influencerMenuNav: MenuNavItem[] = [\n    {\n      name: 'Moj račun',\n      href: '/dashboard/influencer/account',\n      icon: User,\n      description: 'Lični podaci i sigurnost'\n    },\n    {\n      name: 'Postavke profila',\n      href: '/dashboard/influencer/profile',\n      icon: Settings,\n      description: 'Javni profil i cijene'\n    },\n    {\n      name: 'Zarada',\n      href: '/dashboard/influencer/earnings',\n      icon: DollarSign,\n      description: 'Pregled zarade i isplate'\n    }\n  ];\n\n  // Ostali elementi za hamburger meni - biznis\n  const businessMenuNav: MenuNavItem[] = [\n    {\n      name: 'Moj račun',\n      href: '/dashboard/biznis/account',\n      icon: User,\n      description: 'Podaci o firmi i sigurnost'\n    },\n    {\n      name: 'Postavke profila',\n      href: '/dashboard/biznis/profile',\n      icon: Settings,\n      description: 'Javni profil firme'\n    },\n    {\n      name: 'Influenceri',\n      href: '/marketplace/influencers',\n      icon: User,\n      description: 'Pronađi influencere'\n    }\n  ];\n\n  const mainNavigation = userType === 'influencer' ? influencerMainNav : businessMainNav;\n  const menuNavigation = userType === 'influencer' ? influencerMenuNav : businessMenuNav;\n\n  const handleSignOut = async () => {\n    try {\n      await signOut();\n      router.push('/');\n      setIsMenuOpen(false);\n    } catch (error) {\n      console.error('Error signing out:', error);\n    }\n  };\n\n  const isActive = (href: string) => {\n    return pathname === href || pathname.startsWith(href + '/');\n  };\n\n  return (\n    <>\n      {/* Mobile Bottom Navigation */}\n      <div className=\"fixed bottom-0 left-0 right-0 z-50 bg-card border-t border-border md:hidden\">\n        <div className=\"flex items-center justify-around py-2\">\n          {/* Glavne 4 ikonice */}\n          {mainNavigation.map((item) => (\n            <Link key={item.name} href={item.href}>\n              <div className={cn(\n                \"flex flex-col items-center justify-center p-2 min-w-[60px] rounded-lg transition-colors\",\n                isActive(item.href)\n                  ? \"text-primary bg-primary/10\"\n                  : \"text-muted-foreground hover:text-foreground hover:bg-accent\"\n              )}>\n                <item.icon className=\"h-5 w-5 mb-1\" />\n                <span className=\"text-xs font-medium\">{item.label}</span>\n              </div>\n            </Link>\n          ))}\n\n          {/* Hamburger Menu */}\n          <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>\n            <SheetTrigger asChild>\n              <div className=\"flex flex-col items-center justify-center p-2 min-w-[60px] rounded-lg transition-colors text-muted-foreground hover:text-foreground hover:bg-accent cursor-pointer\">\n                <Menu className=\"h-5 w-5 mb-1\" />\n                <span className=\"text-xs font-medium\">Više</span>\n              </div>\n            </SheetTrigger>\n            <SheetContent side=\"bottom\" className=\"h-auto max-h-[80vh]\">\n              <SheetHeader>\n                <SheetTitle>Meni</SheetTitle>\n              </SheetHeader>\n              \n              <div className=\"grid gap-4 py-4\">\n                {menuNavigation.map((item) => (\n                  <Link \n                    key={item.name} \n                    href={item.href}\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    <div className={cn(\n                      \"flex items-center space-x-3 p-3 rounded-lg transition-colors\",\n                      isActive(item.href)\n                        ? \"bg-primary text-primary-foreground\"\n                        : \"hover:bg-accent\"\n                    )}>\n                      <item.icon className=\"h-5 w-5\" />\n                      <div className=\"flex-1\">\n                        <div className=\"font-medium\">{item.name}</div>\n                        {item.description && (\n                          <div className=\"text-sm opacity-75 mt-0.5\">\n                            {item.description}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </Link>\n                ))}\n                \n                {/* Odjava */}\n                <Button\n                  variant=\"ghost\"\n                  onClick={handleSignOut}\n                  className=\"flex items-center justify-start space-x-3 p-3 w-full text-destructive hover:text-destructive hover:bg-destructive/10\"\n                >\n                  <LogOut className=\"h-5 w-5\" />\n                  <span className=\"font-medium\">Odjava</span>\n                </Button>\n              </div>\n            </SheetContent>\n          </Sheet>\n        </div>\n      </div>\n\n      {/* Spacer za bottom navigation */}\n      <div className=\"h-16 md:hidden\" />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AAEA;AAOA;AA1BA;;;;;;;;;;;AA8CO,SAAS,uBAAuB,EAAE,QAAQ,EAA+B;IAC9E,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,kCAAkC;IAClC,MAAM,oBAA+B;QACnC;YACE,MAAM;YACN,MAAM;YACN,MAAM,4NAAA,CAAA,kBAAe;YACrB,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,8MAAA,CAAA,WAAQ;YACd,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;QACT;KACD;IAED,uCAAuC;IACvC,MAAM,kBAA6B;QACjC;YACE,MAAM;YACN,MAAM;YACN,MAAM,4NAAA,CAAA,kBAAe;YACrB,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,8MAAA,CAAA,WAAQ;YACd,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;QACT;KACD;IAED,kDAAkD;IAClD,MAAM,oBAAmC;QACvC;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kNAAA,CAAA,aAAU;YAChB,aAAa;QACf;KACD;IAED,6CAA6C;IAC7C,MAAM,kBAAiC;QACrC;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,aAAa;QACf;KACD;IAED,MAAM,iBAAiB,aAAa,eAAe,oBAAoB;IACvE,MAAM,iBAAiB,aAAa,eAAe,oBAAoB;IAEvE,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;YACZ,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,OAAO,aAAa,QAAQ,SAAS,UAAU,CAAC,OAAO;IACzD;IAEA,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBAEZ,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,4JAAA,CAAA,UAAI;gCAAiB,MAAM,KAAK,IAAI;0CACnC,cAAA,8OAAC;oCAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,2FACA,SAAS,KAAK,IAAI,IACd,+BACA;;sDAEJ,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,8OAAC;4CAAK,WAAU;sDAAuB,KAAK,KAAK;;;;;;;;;;;;+BAR1C,KAAK,IAAI;;;;;sCActB,8OAAC,iIAAA,CAAA,QAAK;4BAAC,MAAM;4BAAY,cAAc;;8CACrC,8OAAC,iIAAA,CAAA,eAAY;oCAAC,OAAO;8CACnB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;;;;;;8CAG1C,8OAAC,iIAAA,CAAA,eAAY;oCAAC,MAAK;oCAAS,WAAU;;sDACpC,8OAAC,iIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC,iIAAA,CAAA,aAAU;0DAAC;;;;;;;;;;;sDAGd,8OAAC;4CAAI,WAAU;;gDACZ,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,4JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,SAAS,IAAM,cAAc;kEAE7B,cAAA,8OAAC;4DAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,gEACA,SAAS,KAAK,IAAI,IACd,uCACA;;8EAEJ,8OAAC,KAAK,IAAI;oEAAC,WAAU;;;;;;8EACrB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAAe,KAAK,IAAI;;;;;;wEACtC,KAAK,WAAW,kBACf,8OAAC;4EAAI,WAAU;sFACZ,KAAK,WAAW;;;;;;;;;;;;;;;;;;uDAfpB,KAAK,IAAI;;;;;8DAwBlB,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS;oDACT,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS1C,8OAAC;gBAAI,WAAU;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 2568, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/navigation/ResponsiveNavigation.tsx"], "sourcesContent": ["'use client';\n\nimport { DesktopNavigation } from './DesktopNavigation';\nimport { MobileBottomNavigation } from './MobileBottomNavigation';\n\ninterface ResponsiveNavigationProps {\n  userType: 'influencer' | 'business';\n}\n\nexport function ResponsiveNavigation({ userType }: ResponsiveNavigationProps) {\n  return (\n    <>\n      {/* Desktop Navigation - prikazuje se samo na desktop uređajima */}\n      <DesktopNavigation userType={userType} />\n      \n      {/* Mobile Bottom Navigation - prikazuje se samo na mobile uređajima */}\n      <MobileBottomNavigation userType={userType} />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASO,SAAS,qBAAqB,EAAE,QAAQ,EAA6B;IAC1E,qBACE;;0BAEE,8OAAC,qJAAA,CAAA,oBAAiB;gBAAC,UAAU;;;;;;0BAG7B,8OAAC,0JAAA,CAAA,yBAAsB;gBAAC,UAAU;;;;;;;;AAGxC", "debugId": null}}, {"offset": {"line": 2602, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction ScrollArea({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\n  return (\n    <ScrollAreaPrimitive.Root\n      data-slot=\"scroll-area\"\n      className={cn(\"relative\", className)}\n      {...props}\n    >\n      <ScrollAreaPrimitive.Viewport\n        data-slot=\"scroll-area-viewport\"\n        className=\"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1\"\n      >\n        {children}\n      </ScrollAreaPrimitive.Viewport>\n      <ScrollBar />\n      <ScrollAreaPrimitive.Corner />\n    </ScrollAreaPrimitive.Root>\n  )\n}\n\nfunction ScrollBar({\n  className,\n  orientation = \"vertical\",\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\n  return (\n    <ScrollAreaPrimitive.ScrollAreaScrollbar\n      data-slot=\"scroll-area-scrollbar\"\n      orientation={orientation}\n      className={cn(\n        \"flex touch-none p-px transition-colors select-none\",\n        orientation === \"vertical\" &&\n          \"h-full w-2.5 border-l border-l-transparent\",\n        orientation === \"horizontal\" &&\n          \"h-2.5 flex-col border-t border-t-transparent\",\n        className\n      )}\n      {...props}\n    >\n      <ScrollAreaPrimitive.ScrollAreaThumb\n        data-slot=\"scroll-area-thumb\"\n        className=\"bg-border relative flex-1 rounded-full\"\n      />\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>\n  )\n}\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAC3B,aAAU;gBACV,WAAU;0BAET;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAGjC;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,UAAU,EACxB,GAAG,OACkE;IACrE,qBACE,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,aAAU;QACV,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,gBAAgB,cACd,8CACF,gBAAgB,gBACd,gDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAClC,aAAU;YACV,WAAU;;;;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 2670, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2930, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/notifications.ts"], "sourcesContent": ["import { supabase } from './supabase';\n\nexport interface Notification {\n  id: string;\n  user_id: string;\n  type: string;\n  title: string;\n  message: string;\n  data: Record<string, any>;\n  read: boolean;\n  created_at: string;\n  updated_at: string;\n}\n\nexport type NotificationType = \n  | 'offer_received'\n  | 'offer_accepted'\n  | 'offer_rejected'\n  | 'campaign_application'\n  | 'campaign_accepted'\n  | 'campaign_rejected'\n  | 'message_received'\n  | 'payment_received';\n\n// Create a new notification\nexport async function createNotification(\n  userId: string,\n  type: NotificationType,\n  title: string,\n  message: string,\n  data: Record<string, any> = {}\n) {\n  const { data: notification, error } = await supabase.rpc('create_notification', {\n    p_user_id: userId,\n    p_type: type,\n    p_title: title,\n    p_message: message,\n    p_data: data\n  });\n\n  if (error) {\n    console.error('Error creating notification:', error);\n    return { data: null, error };\n  }\n\n  return { data: notification, error: null };\n}\n\n// Get user notifications\nexport async function getUserNotifications(userId?: string, limit = 50) {\n  let query = supabase\n    .from('notifications')\n    .select('*')\n    .order('created_at', { ascending: false })\n    .limit(limit);\n\n  if (userId) {\n    query = query.eq('user_id', userId);\n  }\n\n  const { data, error } = await query;\n\n  if (error) {\n    console.error('Error fetching notifications:', error);\n    return { data: null, error };\n  }\n\n  return { data, error: null };\n}\n\n// Mark notification as read\nexport async function markNotificationAsRead(notificationId: string) {\n  const { data, error } = await supabase\n    .from('notifications')\n    .update({ read: true })\n    .eq('id', notificationId)\n    .select()\n    .single();\n\n  if (error) {\n    console.error('Error marking notification as read:', error);\n    return { data: null, error };\n  }\n\n  return { data, error: null };\n}\n\n// Mark all notifications as read for user\nexport async function markAllNotificationsAsRead(userId: string) {\n  const { data, error } = await supabase\n    .from('notifications')\n    .update({ read: true })\n    .eq('user_id', userId)\n    .eq('read', false);\n\n  if (error) {\n    console.error('Error marking all notifications as read:', error);\n    return { data: null, error };\n  }\n\n  return { data, error: null };\n}\n\n// Get unread notification count\nexport async function getUnreadNotificationCount(userId: string) {\n  const { count, error } = await supabase\n    .from('notifications')\n    .select('*', { count: 'exact', head: true })\n    .eq('user_id', userId)\n    .eq('read', false);\n\n  if (error) {\n    console.error('Error getting unread count:', error);\n    return { count: 0, error };\n  }\n\n  return { count: count || 0, error: null };\n}\n\n// Delete notification\nexport async function deleteNotification(notificationId: string) {\n  const { error } = await supabase\n    .from('notifications')\n    .delete()\n    .eq('id', notificationId);\n\n  if (error) {\n    console.error('Error deleting notification:', error);\n    return { error };\n  }\n\n  return { error: null };\n}\n\n// Helper functions for specific notification types\n\nexport async function notifyOfferReceived(\n  influencerId: string,\n  businessName: string,\n  offerTitle: string,\n  offerId: string\n) {\n  return createNotification(\n    influencerId,\n    'offer_received',\n    'Nova direktna ponuda',\n    `${businessName} vam je poslao ponudu: \"${offerTitle}\"`,\n    { offer_id: offerId, business_name: businessName }\n  );\n}\n\nexport async function notifyOfferAccepted(\n  businessId: string,\n  influencerName: string,\n  offerTitle: string,\n  offerId: string\n) {\n  return createNotification(\n    businessId,\n    'offer_accepted',\n    'Ponuda prihvaćena',\n    `${influencerName} je prihvatio vašu ponudu: \"${offerTitle}\"`,\n    { offer_id: offerId, influencer_name: influencerName }\n  );\n}\n\nexport async function notifyOfferRejected(\n  businessId: string,\n  influencerName: string,\n  offerTitle: string,\n  offerId: string\n) {\n  return createNotification(\n    businessId,\n    'offer_rejected',\n    'Ponuda odbijena',\n    `${influencerName} je odbio vašu ponudu: \"${offerTitle}\"`,\n    { offer_id: offerId, influencer_name: influencerName }\n  );\n}\n\nexport async function notifyCampaignApplication(\n  businessId: string,\n  influencerName: string,\n  campaignTitle: string,\n  applicationId: string\n) {\n  return createNotification(\n    businessId,\n    'campaign_application',\n    'Nova aplikacija na kampanju',\n    `${influencerName} se prijavio na kampanju: \"${campaignTitle}\"`,\n    { application_id: applicationId, influencer_name: influencerName }\n  );\n}\n\nexport async function notifyCampaignAccepted(\n  influencerId: string,\n  businessName: string,\n  campaignTitle: string,\n  applicationId: string\n) {\n  return createNotification(\n    influencerId,\n    'campaign_accepted',\n    'Aplikacija prihvaćena',\n    `${businessName} je prihvatio vašu aplikaciju za kampanju: \"${campaignTitle}\"`,\n    { application_id: applicationId, business_name: businessName }\n  );\n}\n\nexport async function notifyCampaignRejected(\n  influencerId: string,\n  businessName: string,\n  campaignTitle: string,\n  applicationId: string\n) {\n  return createNotification(\n    influencerId,\n    'campaign_rejected',\n    'Aplikacija odbijena',\n    `${businessName} je odbio vašu aplikaciju za kampanju: \"${campaignTitle}\"`,\n    { application_id: applicationId, business_name: businessName }\n  );\n}\n\nexport async function notifyMessageReceived(\n  userId: string,\n  senderName: string,\n  conversationId: string\n) {\n  return createNotification(\n    userId,\n    'message_received',\n    'Nova poruka',\n    `${senderName} vam je poslao novu poruku`,\n    { conversation_id: conversationId, sender_name: senderName }\n  );\n}\n\nexport async function notifyPaymentReceived(\n  influencerId: string,\n  amount: number,\n  currency: string,\n  campaignTitle: string\n) {\n  return createNotification(\n    influencerId,\n    'payment_received',\n    'Plaćanje primljeno',\n    `Primili ste plaćanje od ${amount} ${currency} za kampanju: \"${campaignTitle}\"`,\n    { amount, currency, campaign_title: campaignTitle }\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;;AAyBO,eAAe,mBACpB,MAAc,EACd,IAAsB,EACtB,KAAa,EACb,OAAe,EACf,OAA4B,CAAC,CAAC;IAE9B,MAAM,EAAE,MAAM,YAAY,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,uBAAuB;QAC9E,WAAW;QACX,QAAQ;QACR,SAAS;QACT,WAAW;QACX,QAAQ;IACV;IAEA,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,OAAO;QAAE,MAAM;QAAc,OAAO;IAAK;AAC3C;AAGO,eAAe,qBAAqB,MAAe,EAAE,QAAQ,EAAE;IACpE,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM,GACvC,KAAK,CAAC;IAET,IAAI,QAAQ;QACV,QAAQ,MAAM,EAAE,CAAC,WAAW;IAC9B;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAE9B,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,OAAO;QAAE;QAAM,OAAO;IAAK;AAC7B;AAGO,eAAe,uBAAuB,cAAsB;IACjE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC;QAAE,MAAM;IAAK,GACpB,EAAE,CAAC,MAAM,gBACT,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,OAAO;QAAE;QAAM,OAAO;IAAK;AAC7B;AAGO,eAAe,2BAA2B,MAAc;IAC7D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC;QAAE,MAAM;IAAK,GACpB,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,QAAQ;IAEd,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,OAAO;QAAE;QAAM,OAAO;IAAK;AAC7B;AAGO,eAAe,2BAA2B,MAAc;IAC7D,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACpC,IAAI,CAAC,iBACL,MAAM,CAAC,KAAK;QAAE,OAAO;QAAS,MAAM;IAAK,GACzC,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,QAAQ;IAEd,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YAAE,OAAO;YAAG;QAAM;IAC3B;IAEA,OAAO;QAAE,OAAO,SAAS;QAAG,OAAO;IAAK;AAC1C;AAGO,eAAe,mBAAmB,cAAsB;IAC7D,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,iBACL,MAAM,GACN,EAAE,CAAC,MAAM;IAEZ,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YAAE;QAAM;IACjB;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB;AAIO,eAAe,oBACpB,YAAoB,EACpB,YAAoB,EACpB,UAAkB,EAClB,OAAe;IAEf,OAAO,mBACL,cACA,kBACA,wBACA,GAAG,aAAa,wBAAwB,EAAE,WAAW,CAAC,CAAC,EACvD;QAAE,UAAU;QAAS,eAAe;IAAa;AAErD;AAEO,eAAe,oBACpB,UAAkB,EAClB,cAAsB,EACtB,UAAkB,EAClB,OAAe;IAEf,OAAO,mBACL,YACA,kBACA,qBACA,GAAG,eAAe,4BAA4B,EAAE,WAAW,CAAC,CAAC,EAC7D;QAAE,UAAU;QAAS,iBAAiB;IAAe;AAEzD;AAEO,eAAe,oBACpB,UAAkB,EAClB,cAAsB,EACtB,UAAkB,EAClB,OAAe;IAEf,OAAO,mBACL,YACA,kBACA,mBACA,GAAG,eAAe,wBAAwB,EAAE,WAAW,CAAC,CAAC,EACzD;QAAE,UAAU;QAAS,iBAAiB;IAAe;AAEzD;AAEO,eAAe,0BACpB,UAAkB,EAClB,cAAsB,EACtB,aAAqB,EACrB,aAAqB;IAErB,OAAO,mBACL,YACA,wBACA,+BACA,GAAG,eAAe,2BAA2B,EAAE,cAAc,CAAC,CAAC,EAC/D;QAAE,gBAAgB;QAAe,iBAAiB;IAAe;AAErE;AAEO,eAAe,uBACpB,YAAoB,EACpB,YAAoB,EACpB,aAAqB,EACrB,aAAqB;IAErB,OAAO,mBACL,cACA,qBACA,yBACA,GAAG,aAAa,4CAA4C,EAAE,cAAc,CAAC,CAAC,EAC9E;QAAE,gBAAgB;QAAe,eAAe;IAAa;AAEjE;AAEO,eAAe,uBACpB,YAAoB,EACpB,YAAoB,EACpB,aAAqB,EACrB,aAAqB;IAErB,OAAO,mBACL,cACA,qBACA,uBACA,GAAG,aAAa,wCAAwC,EAAE,cAAc,CAAC,CAAC,EAC1E;QAAE,gBAAgB;QAAe,eAAe;IAAa;AAEjE;AAEO,eAAe,sBACpB,MAAc,EACd,UAAkB,EAClB,cAAsB;IAEtB,OAAO,mBACL,QACA,oBACA,eACA,GAAG,WAAW,0BAA0B,CAAC,EACzC;QAAE,iBAAiB;QAAgB,aAAa;IAAW;AAE/D;AAEO,eAAe,sBACpB,YAAoB,EACpB,MAAc,EACd,QAAgB,EAChB,aAAqB;IAErB,OAAO,mBACL,cACA,oBACA,sBACA,CAAC,wBAAwB,EAAE,OAAO,CAAC,EAAE,SAAS,eAAe,EAAE,cAAc,CAAC,CAAC,EAC/E;QAAE;QAAQ;QAAU,gBAAgB;IAAc;AAEtD", "debugId": null}}, {"offset": {"line": 3102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/notifications/NotificationDropdown.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuLabel,\n  DropdownMenuTrigger,\n  DropdownMenuSeparator,\n  DropdownMenuItem,\n} from '@/components/ui/dropdown-menu';\nimport { \n  Bell, \n  Check, \n  CheckCheck,\n  Inbox,\n  MessageCircle,\n  DollarSign,\n  FileText,\n  Building2,\n  User\n} from 'lucide-react';\nimport { \n  getUserNotifications, \n  markNotificationAsRead, \n  markAllNotificationsAsRead,\n  getUnreadNotificationCount,\n  type Notification \n} from '@/lib/notifications';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { formatDistanceToNow } from 'date-fns';\nimport { hr } from 'date-fns/locale';\nimport { toast } from 'sonner';\nimport Link from 'next/link';\n\nexport function NotificationDropdown() {\n  const { user } = useAuth();\n  const [notifications, setNotifications] = useState<Notification[]>([]);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isOpen, setIsOpen] = useState(false);\n\n  useEffect(() => {\n    if (user) {\n      loadNotifications();\n      loadUnreadCount();\n    }\n  }, [user]);\n\n  const loadNotifications = async () => {\n    if (!user) return;\n    \n    setIsLoading(true);\n    try {\n      const { data, error } = await getUserNotifications(user.id, 20);\n      if (error) {\n        console.error('Error loading notifications:', error);\n      } else {\n        setNotifications(data || []);\n      }\n    } catch (error) {\n      console.error('Error loading notifications:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loadUnreadCount = async () => {\n    if (!user) return;\n    \n    try {\n      const { count, error } = await getUnreadNotificationCount(user.id);\n      if (error) {\n        console.error('Error loading unread count:', error);\n      } else {\n        setUnreadCount(count);\n      }\n    } catch (error) {\n      console.error('Error loading unread count:', error);\n    }\n  };\n\n  const handleMarkAsRead = async (notificationId: string) => {\n    try {\n      const { error } = await markNotificationAsRead(notificationId);\n      if (error) {\n        toast.error('Greška pri označavanju notifikacije');\n      } else {\n        setNotifications(prev => \n          prev.map(n => n.id === notificationId ? { ...n, read: true } : n)\n        );\n        setUnreadCount(prev => Math.max(0, prev - 1));\n      }\n    } catch (error) {\n      console.error('Error marking notification as read:', error);\n      toast.error('Greška pri označavanju notifikacije');\n    }\n  };\n\n  const handleMarkAllAsRead = async () => {\n    if (!user) return;\n    \n    try {\n      const { error } = await markAllNotificationsAsRead(user.id);\n      if (error) {\n        toast.error('Greška pri označavanju notifikacija');\n      } else {\n        setNotifications(prev => prev.map(n => ({ ...n, read: true })));\n        setUnreadCount(0);\n        toast.success('Sve notifikacije su označene kao pročitane');\n      }\n    } catch (error) {\n      console.error('Error marking all notifications as read:', error);\n      toast.error('Greška pri označavanju notifikacija');\n    }\n  };\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'offer_received':\n      case 'offer_accepted':\n      case 'offer_rejected':\n        return <Inbox className=\"h-4 w-4\" />;\n      case 'campaign_application':\n      case 'campaign_accepted':\n      case 'campaign_rejected':\n        return <FileText className=\"h-4 w-4\" />;\n      case 'message_received':\n        return <MessageCircle className=\"h-4 w-4\" />;\n      case 'payment_received':\n        return <DollarSign className=\"h-4 w-4\" />;\n      default:\n        return <Bell className=\"h-4 w-4\" />;\n    }\n  };\n\n  const getNotificationLink = (notification: Notification) => {\n    switch (notification.type) {\n      case 'offer_received':\n      case 'offer_accepted':\n      case 'offer_rejected':\n        if (notification.data.offer_id) {\n          return `/dashboard/influencer/offers/${notification.data.offer_id}`;\n        }\n        return '/dashboard/influencer/offers';\n      case 'campaign_application':\n      case 'campaign_accepted':\n      case 'campaign_rejected':\n        return '/dashboard/campaigns';\n      case 'message_received':\n        return '/dashboard/messages';\n      default:\n        return '/dashboard';\n    }\n  };\n\n  if (!user) return null;\n\n  return (\n    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n          <Bell className=\"h-5 w-5\" />\n          {unreadCount > 0 && (\n            <Badge \n              variant=\"destructive\" \n              className=\"absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n            >\n              {unreadCount > 99 ? '99+' : unreadCount}\n            </Badge>\n          )}\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\" className=\"w-80\">\n        <DropdownMenuLabel className=\"flex items-center justify-between p-4\">\n          <h3 className=\"font-semibold\">Notifikacije</h3>\n          {unreadCount > 0 && (\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={handleMarkAllAsRead}\n              className=\"text-xs\"\n            >\n              <CheckCheck className=\"h-3 w-3 mr-1\" />\n              Označi sve\n            </Button>\n          )}\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        \n        <ScrollArea className=\"h-96\">\n          {isLoading ? (\n            <div className=\"flex items-center justify-center p-8\">\n              <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-primary\"></div>\n            </div>\n          ) : notifications.length === 0 ? (\n            <div className=\"flex flex-col items-center justify-center p-8 text-center\">\n              <Bell className=\"h-8 w-8 text-muted-foreground mb-2\" />\n              <p className=\"text-sm text-muted-foreground\">Nema novih notifikacija</p>\n            </div>\n          ) : (\n            <div className=\"space-y-1\">\n              {notifications.map((notification) => (\n                <div\n                  key={notification.id}\n                  className={`p-3 hover:bg-muted/50 transition-colors border-l-2 ${\n                    notification.read ? 'border-transparent' : 'border-primary'\n                  }`}\n                >\n                  <Link \n                    href={getNotificationLink(notification)}\n                    onClick={() => {\n                      if (!notification.read) {\n                        handleMarkAsRead(notification.id);\n                      }\n                      setIsOpen(false);\n                    }}\n                    className=\"block\"\n                  >\n                    <div className=\"flex items-start gap-3\">\n                      <div className=\"flex-shrink-0 mt-0.5\">\n                        {getNotificationIcon(notification.type)}\n                      </div>\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-start justify-between\">\n                          <h4 className={`text-sm font-medium ${\n                            notification.read ? 'text-muted-foreground' : 'text-foreground'\n                          }`}>\n                            {notification.title}\n                          </h4>\n                          {!notification.read && (\n                            <Button\n                              variant=\"ghost\"\n                              size=\"sm\"\n                              onClick={(e) => {\n                                e.preventDefault();\n                                e.stopPropagation();\n                                handleMarkAsRead(notification.id);\n                              }}\n                              className=\"h-6 w-6 p-0 ml-2\"\n                            >\n                              <Check className=\"h-3 w-3\" />\n                            </Button>\n                          )}\n                        </div>\n                        <p className={`text-xs mt-1 ${\n                          notification.read ? 'text-muted-foreground' : 'text-muted-foreground'\n                        }`}>\n                          {notification.message}\n                        </p>\n                        <p className=\"text-xs text-muted-foreground mt-1\">\n                          {formatDistanceToNow(new Date(notification.created_at), { \n                            addSuffix: true, \n                            locale: hr \n                          })}\n                        </p>\n                      </div>\n                    </div>\n                  </Link>\n                </div>\n              ))}\n            </div>\n          )}\n        </ScrollArea>\n        \n        {notifications.length > 0 && (\n          <>\n            <DropdownMenuSeparator />\n            <div className=\"p-2\">\n              <Button variant=\"ghost\" size=\"sm\" className=\"w-full\" asChild>\n                <Link href=\"/dashboard/notifications\">\n                  Pogledaj sve notifikacije\n                </Link>\n              </Button>\n            </div>\n          </>\n        )}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAOA;AACA;AACA;AACA;AACA;AApCA;;;;;;;;;;;;;;AAsCO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;YACA;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,oBAAoB;QACxB,IAAI,CAAC,MAAM;QAEX,aAAa;QACb,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,2HAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,EAAE,EAAE;YAC5D,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,gCAAgC;YAChD,OAAO;gBACL,iBAAiB,QAAQ,EAAE;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,2HAAA,CAAA,6BAA0B,AAAD,EAAE,KAAK,EAAE;YACjE,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,+BAA+B;YAC/C,OAAO;gBACL,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,2HAAA,CAAA,yBAAsB,AAAD,EAAE;YAC/C,IAAI,OAAO;gBACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,iBAAiB;4BAAE,GAAG,CAAC;4BAAE,MAAM;wBAAK,IAAI;gBAEjE,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,2HAAA,CAAA,6BAA0B,AAAD,EAAE,KAAK,EAAE;YAC1D,IAAI,OAAO;gBACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,iBAAiB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC;4BAAE,GAAG,CAAC;4BAAE,MAAM;wBAAK,CAAC;gBAC5D,eAAe;gBACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,kNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ,aAAa,IAAI;YACvB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,IAAI,aAAa,IAAI,CAAC,QAAQ,EAAE;oBAC9B,OAAO,CAAC,6BAA6B,EAAE,aAAa,IAAI,CAAC,QAAQ,EAAE;gBACrE;gBACA,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC,4IAAA,CAAA,eAAY;QAAC,MAAM;QAAQ,cAAc;;0BACxC,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAK,WAAU;;sCAC1C,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACf,cAAc,mBACb,8OAAC,iIAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,WAAU;sCAET,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAKpC,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAM;gBAAM,WAAU;;kCACzC,8OAAC,4IAAA,CAAA,oBAAiB;wBAAC,WAAU;;0CAC3B,8OAAC;gCAAG,WAAU;0CAAgB;;;;;;4BAC7B,cAAc,mBACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAK7C,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCAEtB,8OAAC,0IAAA,CAAA,aAAU;wBAAC,WAAU;kCACnB,0BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;mCAEf,cAAc,MAAM,KAAK,kBAC3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;iDAG/C,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,8OAAC;oCAEC,WAAW,CAAC,mDAAmD,EAC7D,aAAa,IAAI,GAAG,uBAAuB,kBAC3C;8CAEF,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,oBAAoB;wCAC1B,SAAS;4CACP,IAAI,CAAC,aAAa,IAAI,EAAE;gDACtB,iBAAiB,aAAa,EAAE;4CAClC;4CACA,UAAU;wCACZ;wCACA,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,oBAAoB,aAAa,IAAI;;;;;;8DAExC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAW,CAAC,oBAAoB,EAClC,aAAa,IAAI,GAAG,0BAA0B,mBAC9C;8EACC,aAAa,KAAK;;;;;;gEAEpB,CAAC,aAAa,IAAI,kBACjB,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,CAAC;wEACR,EAAE,cAAc;wEAChB,EAAE,eAAe;wEACjB,iBAAiB,aAAa,EAAE;oEAClC;oEACA,WAAU;8EAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAIvB,8OAAC;4DAAE,WAAW,CAAC,aAAa,EAC1B,aAAa,IAAI,GAAG,0BAA0B,yBAC9C;sEACC,aAAa,OAAO;;;;;;sEAEvB,8OAAC;4DAAE,WAAU;sEACV,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,aAAa,UAAU,GAAG;gEACtD,WAAW;gEACX,QAAQ,2IAAA,CAAA,KAAE;4DACZ;;;;;;;;;;;;;;;;;;;;;;;mCAlDH,aAAa,EAAE;;;;;;;;;;;;;;;oBA6D7B,cAAc,MAAM,GAAG,mBACtB;;0CACE,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0CACtB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAS,OAAO;8CAC1D,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtD", "debugId": null}}, {"offset": {"line": 3574, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/dashboard/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { getProfile } from '@/lib/profiles';\nimport { ResponsiveNavigation } from '@/components/navigation/ResponsiveNavigation';\nimport { NotificationDropdown } from '@/components/notifications/NotificationDropdown';\nimport { Loader2 } from 'lucide-react';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n  requiredUserType?: 'influencer' | 'business';\n}\n\nexport function DashboardLayout({ children, requiredUserType }: DashboardLayoutProps) {\n  const { user, loading: authLoading } = useAuth();\n  const router = useRouter();\n  const [profile, setProfile] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (authLoading) return;\n\n    if (!user) {\n      router.push('/prijava');\n      return;\n    }\n\n    loadProfile();\n  }, [user, authLoading, router]);\n\n  const loadProfile = async () => {\n    try {\n      setLoading(true);\n      const { data, error } = await getProfile(user!.id);\n\n      if (error) {\n        console.error('Profile loading error:', error);\n        if (error.message && error.message.includes('No rows')) {\n          router.push('/profil/kreiranje');\n          return;\n        }\n        setError('Greška pri učitavanju profila');\n        return;\n      }\n\n      if (!data) {\n        router.push('/profil/kreiranje');\n        return;\n      }\n\n      setProfile(data);\n\n      // Provjeri da li korisnik ima pravo pristupa ovoj stranici\n      if (requiredUserType && data.user_type !== requiredUserType) {\n        // Preusmjeri na odgovarajući dashboard\n        if (data.user_type === 'influencer') {\n          router.push('/dashboard/influencer');\n        } else if (data.user_type === 'business') {\n          router.push('/dashboard/biznis');\n        }\n        return;\n      }\n    } catch (err) {\n      console.error('Unexpected error in loadProfile:', err);\n      setError('Neočekivana greška');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Loading state\n  if (authLoading || loading) {\n    return (\n      <div className=\"min-h-screen bg-background flex items-center justify-center\">\n        <div className=\"flex flex-col items-center space-y-4\">\n          <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\n          <p className=\"text-muted-foreground\">Učitavanje...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-background flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-foreground mb-2\">Greška</h2>\n          <p className=\"text-muted-foreground mb-4\">{error}</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90\"\n          >\n            Pokušaj ponovo\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // No profile state\n  if (!profile) {\n    return null; // Router redirect will handle this\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Responsive Navigation */}\n      <ResponsiveNavigation userType={profile.user_type} />\n\n      {/* Main Content - full width without sidebar */}\n      <div className=\"flex flex-col min-h-screen\">\n        {/* Header */}\n        <header className=\"bg-card border-b border-border px-6 py-4 md:pl-20\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-foreground\">\n                {profile.user_type === 'influencer' ? 'Influencer Dashboard' : 'Biznis Dashboard'}\n              </h1>\n              <p className=\"text-muted-foreground\">\n                Dobrodošli, {profile.full_name || profile.username}\n              </p>\n            </div>\n\n            {/* User Info */}\n            <div className=\"flex items-center space-x-3\">\n              <NotificationDropdown />\n              {profile.avatar_url && (\n                <img\n                  src={profile.avatar_url}\n                  alt={profile.username}\n                  className=\"w-8 h-8 rounded-full object-cover\"\n                />\n              )}\n              <div className=\"text-right hidden sm:block\">\n                <p className=\"text-sm font-medium text-foreground\">\n                  {profile.full_name || profile.username}\n                </p>\n                <p className=\"text-xs text-muted-foreground\">\n                  @{profile.username}\n                </p>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        {/* Page Content */}\n        <main className=\"flex-1 overflow-auto pb-16 md:pb-0\">\n          <div className=\"p-6\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAeO,SAAS,gBAAgB,EAAE,QAAQ,EAAE,gBAAgB,EAAwB;IAClF,MAAM,EAAE,IAAI,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC5C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;QAEjB,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,CAAC;YACZ;QACF;QAEA;IACF,GAAG;QAAC;QAAM;QAAa;KAAO;IAE9B,MAAM,cAAc;QAClB,IAAI;YACF,WAAW;YACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAM,EAAE;YAEjD,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;oBACtD,OAAO,IAAI,CAAC;oBACZ;gBACF;gBACA,SAAS;gBACT;YACF;YAEA,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,WAAW;YAEX,2DAA2D;YAC3D,IAAI,oBAAoB,KAAK,SAAS,KAAK,kBAAkB;gBAC3D,uCAAuC;gBACvC,IAAI,KAAK,SAAS,KAAK,cAAc;oBACnC,OAAO,IAAI,CAAC;gBACd,OAAO,IAAI,KAAK,SAAS,KAAK,YAAY;oBACxC,OAAO,IAAI,CAAC;gBACd;gBACA;YACF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oCAAoC;YAClD,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,gBAAgB;IAChB,IAAI,eAAe,SAAS;QAC1B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0C;;;;;;kCACxD,8OAAC;wBAAE,WAAU;kCAA8B;;;;;;kCAC3C,8OAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,mBAAmB;IACnB,IAAI,CAAC,SAAS;QACZ,OAAO,MAAM,mCAAmC;IAClD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,wJAAA,CAAA,uBAAoB;gBAAC,UAAU,QAAQ,SAAS;;;;;;0BAGjD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,QAAQ,SAAS,KAAK,eAAe,yBAAyB;;;;;;sDAEjE,8OAAC;4CAAE,WAAU;;gDAAwB;gDACtB,QAAQ,SAAS,IAAI,QAAQ,QAAQ;;;;;;;;;;;;;8CAKtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,2JAAA,CAAA,uBAAoB;;;;;wCACpB,QAAQ,UAAU,kBACjB,8OAAC;4CACC,KAAK,QAAQ,UAAU;4CACvB,KAAK,QAAQ,QAAQ;4CACrB,WAAU;;;;;;sDAGd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,QAAQ,SAAS,IAAI,QAAQ,QAAQ;;;;;;8DAExC,8OAAC;oDAAE,WAAU;;wDAAgC;wDACzC,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ5B,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 3868, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/app/dashboard/campaigns/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { \n  Plus, \n  Edit, \n  Eye, \n  Calendar, \n  DollarSign, \n  Users, \n  Loader2,\n  CheckCircle,\n  Clock,\n  AlertCircle\n} from 'lucide-react';\nimport Link from 'next/link';\n\nimport { getBusinessCampaigns, updateCampaignStatus } from '@/lib/campaigns';\nimport { DashboardLayout } from '@/components/dashboard/DashboardLayout';\n\ninterface Campaign {\n  id: string;\n  title: string;\n  description: string;\n  budget: number | null;\n  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';\n  created_at: string;\n  application_count?: number;\n  content_types: string[];\n}\n\nexport default function BusinessCampaignsPage() {\n  console.log('BusinessCampaignsPage component rendered');\n\n  const { user, loading: authLoading } = useAuth();\n  const router = useRouter();\n  const [campaigns, setCampaigns] = useState<Campaign[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [activatingCampaign, setActivatingCampaign] = useState<string | null>(null);\n\n  console.log('BusinessCampaignsPage - current state:', {\n    user: user?.id,\n    userObject: user,\n    authLoading,\n    campaignsCount: campaigns.length,\n    loading\n  });\n\n  useEffect(() => {\n    if (!authLoading && !user) {\n      router.push('/prijava');\n      return;\n    }\n\n    if (user) {\n      loadCampaigns();\n    }\n  }, [user, authLoading, router]);\n\n  const loadCampaigns = async () => {\n    try {\n      setLoading(true);\n\n      if (!user?.id) {\n        return;\n      }\n\n      const result = await getBusinessCampaigns(user.id);\n      setCampaigns(result?.data || []);\n    } catch (error) {\n      console.error('Error loading campaigns:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n\n\n  const activateCampaign = async (campaignId: string) => {\n    try {\n      setActivatingCampaign(campaignId);\n      console.log('Activating campaign:', campaignId, 'for user:', user?.id);\n\n      const { error } = await updateCampaignStatus(campaignId, 'active');\n\n      if (error) {\n        console.error('Error activating campaign:', error);\n        return;\n      }\n\n      console.log('Campaign activated successfully');\n      // Refresh campaigns\n      await loadCampaigns();\n    } catch (error) {\n      console.error('Error activating campaign:', error);\n    } finally {\n      setActivatingCampaign(null);\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'draft':\n        return <Clock className=\"h-4 w-4\" />;\n      case 'active':\n        return <CheckCircle className=\"h-4 w-4\" />;\n      case 'paused':\n        return <AlertCircle className=\"h-4 w-4\" />;\n      case 'completed':\n        return <CheckCircle className=\"h-4 w-4\" />;\n      default:\n        return <Clock className=\"h-4 w-4\" />;\n    }\n  };\n\n  const getStatusBadge = (status: string) => {\n    const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {\n      draft: 'outline',\n      active: 'default',\n      paused: 'secondary',\n      completed: 'secondary'\n    };\n\n    const labels: Record<string, string> = {\n      draft: 'Draft',\n      active: 'Aktivna',\n      paused: 'Pauzirana',\n      completed: 'Završena'\n    };\n\n    return (\n      <Badge variant={variants[status] || 'outline'} className=\"flex items-center gap-1\">\n        {getStatusIcon(status)}\n        {labels[status] || status}\n      </Badge>\n    );\n  };\n\n  const draftCampaigns = campaigns.filter(c => c.status === 'draft');\n  const activeCampaigns = campaigns.filter(c => c.status === 'active');\n  const otherCampaigns = campaigns.filter(c => !['draft', 'active'].includes(c.status));\n\n  if (authLoading || loading) {\n    return (\n      <DashboardLayout requiredUserType=\"business\">\n        <div className=\"flex items-center justify-center min-h-[400px]\">\n          <div className=\"flex items-center gap-2\">\n            <Loader2 className=\"h-6 w-6 animate-spin\" />\n            <span>Loading campaigns...</span>\n          </div>\n        </div>\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout requiredUserType=\"business\">\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4\">\n          <div>\n            <h1 className=\"text-2xl md:text-3xl font-bold\">Moje Kampanje</h1>\n            <p className=\"text-muted-foreground mt-1\">\n              Upravljajte svojim kampanjama i pratite performanse\n            </p>\n          </div>\n\n          <Link href=\"/campaigns/create\">\n            <Button className=\"w-full sm:w-auto\">\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Nova Kampanja\n            </Button>\n          </Link>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-8\">\n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center gap-2\">\n                <Clock className=\"h-4 w-4 text-muted-foreground\" />\n                <div>\n                  <p className=\"text-sm text-muted-foreground\">Draft</p>\n                  <p className=\"text-2xl font-bold\">{draftCampaigns.length}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center gap-2\">\n                <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                <div>\n                  <p className=\"text-sm text-muted-foreground\">Aktivne</p>\n                  <p className=\"text-2xl font-bold\">{activeCampaigns.length}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center gap-2\">\n                <DollarSign className=\"h-4 w-4 text-muted-foreground\" />\n                <div>\n                  <p className=\"text-sm text-muted-foreground\">Ukupan Budžet</p>\n                  <p className=\"text-2xl font-bold\">\n                    {campaigns.reduce((sum, c) => sum + (c.budget || 0), 0).toLocaleString()} KM\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center gap-2\">\n                <Users className=\"h-4 w-4 text-muted-foreground\" />\n                <div>\n                  <p className=\"text-sm text-muted-foreground\">Ukupno</p>\n                  <p className=\"text-2xl font-bold\">{campaigns.length}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Campaigns Tabs */}\n        <Tabs defaultValue=\"draft\" className=\"space-y-6\">\n          <TabsList>\n            <TabsTrigger value=\"draft\">Draft ({draftCampaigns.length})</TabsTrigger>\n            <TabsTrigger value=\"active\">Aktivne ({activeCampaigns.length})</TabsTrigger>\n            <TabsTrigger value=\"other\">Ostale ({otherCampaigns.length})</TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"draft\" className=\"space-y-4\">\n            {draftCampaigns.length === 0 ? (\n              <Card>\n                <CardContent className=\"p-8 text-center\">\n                  <p className=\"text-muted-foreground mb-4\">Nemate draft kampanja</p>\n                  <Link href=\"/campaigns/create\">\n                    <Button>\n                      <Plus className=\"h-4 w-4 mr-2\" />\n                      Kreiraj prvu kampanju\n                    </Button>\n                  </Link>\n                </CardContent>\n              </Card>\n            ) : (\n              draftCampaigns.map((campaign) => (\n                <CampaignCard \n                  key={campaign.id} \n                  campaign={campaign} \n                  onActivate={activateCampaign}\n                  isActivating={activatingCampaign === campaign.id}\n                />\n              ))\n            )}\n          </TabsContent>\n\n          <TabsContent value=\"active\" className=\"space-y-4\">\n            {activeCampaigns.length === 0 ? (\n              <Card>\n                <CardContent className=\"p-8 text-center\">\n                  <p className=\"text-muted-foreground\">Nemate aktivnih kampanja</p>\n                </CardContent>\n              </Card>\n            ) : (\n              activeCampaigns.map((campaign) => (\n                <CampaignCard \n                  key={campaign.id} \n                  campaign={campaign} \n                  onActivate={activateCampaign}\n                  isActivating={false}\n                />\n              ))\n            )}\n          </TabsContent>\n\n          <TabsContent value=\"other\" className=\"space-y-4\">\n            {otherCampaigns.length === 0 ? (\n              <Card>\n                <CardContent className=\"p-8 text-center\">\n                  <p className=\"text-muted-foreground\">Nemate drugih kampanja</p>\n                </CardContent>\n              </Card>\n            ) : (\n              otherCampaigns.map((campaign) => (\n                <CampaignCard \n                  key={campaign.id} \n                  campaign={campaign} \n                  onActivate={activateCampaign}\n                  isActivating={false}\n                />\n              ))\n            )}\n          </TabsContent>\n        </Tabs>\n      </div>\n    </DashboardLayout>\n  );\n}\n\ninterface CampaignCardProps {\n  campaign: Campaign;\n  onActivate: (id: string) => void;\n  isActivating: boolean;\n}\n\nfunction CampaignCard({ campaign, onActivate, isActivating }: CampaignCardProps) {\n  const getStatusBadge = (status: string) => {\n    const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {\n      draft: 'outline',\n      active: 'default',\n      paused: 'secondary',\n      completed: 'secondary'\n    };\n\n    const labels: Record<string, string> = {\n      draft: 'Draft',\n      active: 'Aktivna',\n      paused: 'Pauzirana',\n      completed: 'Završena'\n    };\n\n    return (\n      <Badge variant={variants[status] || 'outline'}>\n        {labels[status] || status}\n      </Badge>\n    );\n  };\n\n  return (\n    <Card>\n      <CardContent className=\"p-6\">\n        <div className=\"flex justify-between items-start\">\n          <div className=\"flex-1\">\n            <div className=\"flex items-center gap-3 mb-2\">\n              <h3 className=\"text-lg font-semibold\">{campaign.title}</h3>\n              {getStatusBadge(campaign.status)}\n            </div>\n            \n            <p className=\"text-muted-foreground mb-4 line-clamp-2\">\n              {campaign.description}\n            </p>\n            \n            <div className=\"flex items-center gap-4 text-sm text-muted-foreground\">\n              <div className=\"flex items-center gap-1\">\n                <DollarSign className=\"h-4 w-4\" />\n                {campaign.budget?.toLocaleString() || 'N/A'} KM\n              </div>\n              <div className=\"flex items-center gap-1\">\n                <Calendar className=\"h-4 w-4\" />\n                {new Date(campaign.created_at).toLocaleDateString()}\n              </div>\n              {campaign.content_types && (\n                <div>\n                  {campaign.content_types.slice(0, 2).join(', ')}\n                  {campaign.content_types.length > 2 && ` +${campaign.content_types.length - 2}`}\n                </div>\n              )}\n            </div>\n          </div>\n          \n          <div className=\"flex items-center gap-2 ml-4\">\n            <Link href={`/campaigns/${campaign.id}`}>\n              <Button variant=\"outline\" size=\"sm\">\n                <Eye className=\"h-4 w-4 mr-1\" />\n                View\n              </Button>\n            </Link>\n            \n            {campaign.status === 'draft' && (\n              <>\n                <Link href={`/campaigns/${campaign.id}/edit`}>\n                  <Button variant=\"outline\" size=\"sm\">\n                    <Edit className=\"h-4 w-4 mr-1\" />\n                    Edit\n                  </Button>\n                </Link>\n                \n                <Button \n                  size=\"sm\" \n                  onClick={() => onActivate(campaign.id)}\n                  disabled={isActivating}\n                >\n                  {isActivating && <Loader2 className=\"h-4 w-4 mr-1 animate-spin\" />}\n                  Aktiviraj\n                </Button>\n              </>\n            )}\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAEA;AACA;AAxBA;;;;;;;;;;;;;AAqCe,SAAS;IACtB,QAAQ,GAAG,CAAC;IAEZ,MAAM,EAAE,IAAI,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5E,QAAQ,GAAG,CAAC,0CAA0C;QACpD,MAAM,MAAM;QACZ,YAAY;QACZ;QACA,gBAAgB,UAAU,MAAM;QAChC;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe,CAAC,MAAM;YACzB,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;QAAM;QAAa;KAAO;IAE9B,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YAEX,IAAI,CAAC,MAAM,IAAI;gBACb;YACF;YAEA,MAAM,SAAS,MAAM,CAAA,GAAA,uHAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,EAAE;YACjD,aAAa,QAAQ,QAAQ,EAAE;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAIA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,sBAAsB;YACtB,QAAQ,GAAG,CAAC,wBAAwB,YAAY,aAAa,MAAM;YAEnE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,uHAAA,CAAA,uBAAoB,AAAD,EAAE,YAAY;YAEzD,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C;YACF;YAEA,QAAQ,GAAG,CAAC;YACZ,oBAAoB;YACpB,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,WAAgF;YACpF,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,WAAW;QACb;QAEA,MAAM,SAAiC;YACrC,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,WAAW;QACb;QAEA,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,SAAS,QAAQ,CAAC,OAAO,IAAI;YAAW,WAAU;;gBACtD,cAAc;gBACd,MAAM,CAAC,OAAO,IAAI;;;;;;;IAGzB;IAEA,MAAM,iBAAiB,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;IAC1D,MAAM,kBAAkB,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;IAC3D,MAAM,iBAAiB,UAAU,MAAM,CAAC,CAAA,IAAK,CAAC;YAAC;YAAS;SAAS,CAAC,QAAQ,CAAC,EAAE,MAAM;IAEnF,IAAI,eAAe,SAAS;QAC1B,qBACE,8OAAC,kJAAA,CAAA,kBAAe;YAAC,kBAAiB;sBAChC,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,8OAAC;sCAAK;;;;;;;;;;;;;;;;;;;;;;IAKhB;IAEA,qBACE,8OAAC,kJAAA,CAAA,kBAAe;QAAC,kBAAiB;kBAChC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAK5C,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,WAAU;;kDAChB,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;8BAOvC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;8DAC7C,8OAAC;oDAAE,WAAU;8DAAsB,eAAe,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMhE,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;8DAC7C,8OAAC;oDAAE,WAAU;8DAAsB,gBAAgB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMjE,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;8DAC7C,8OAAC;oDAAE,WAAU;;wDACV,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,MAAM,IAAI,CAAC,GAAG,GAAG,cAAc;wDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOnF,8OAAC,gIAAA,CAAA,OAAI;sCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;8DAC7C,8OAAC;oDAAE,WAAU;8DAAsB,UAAU,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ7D,8OAAC,gIAAA,CAAA,OAAI;oBAAC,cAAa;oBAAQ,WAAU;;sCACnC,8OAAC,gIAAA,CAAA,WAAQ;;8CACP,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;;wCAAQ;wCAAQ,eAAe,MAAM;wCAAC;;;;;;;8CACzD,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;;wCAAS;wCAAU,gBAAgB,MAAM;wCAAC;;;;;;;8CAC7D,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;;wCAAQ;wCAAS,eAAe,MAAM;wCAAC;;;;;;;;;;;;;sCAG5D,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAQ,WAAU;sCAClC,eAAe,MAAM,KAAK,kBACzB,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;;kEACL,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;uCAOzC,eAAe,GAAG,CAAC,CAAC,yBAClB,8OAAC;oCAEC,UAAU;oCACV,YAAY;oCACZ,cAAc,uBAAuB,SAAS,EAAE;mCAH3C,SAAS,EAAE;;;;;;;;;;sCASxB,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAS,WAAU;sCACnC,gBAAgB,MAAM,KAAK,kBAC1B,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;uCAIzC,gBAAgB,GAAG,CAAC,CAAC,yBACnB,8OAAC;oCAEC,UAAU;oCACV,YAAY;oCACZ,cAAc;mCAHT,SAAS,EAAE;;;;;;;;;;sCASxB,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAQ,WAAU;sCAClC,eAAe,MAAM,KAAK,kBACzB,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;uCAIzC,eAAe,GAAG,CAAC,CAAC,yBAClB,8OAAC;oCAEC,UAAU;oCACV,YAAY;oCACZ,cAAc;mCAHT,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYlC;AAQA,SAAS,aAAa,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAqB;IAC7E,MAAM,iBAAiB,CAAC;QACtB,MAAM,WAAgF;YACpF,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,WAAW;QACb;QAEA,MAAM,SAAiC;YACrC,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,WAAW;QACb;QAEA,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,SAAS,QAAQ,CAAC,OAAO,IAAI;sBACjC,MAAM,CAAC,OAAO,IAAI;;;;;;IAGzB;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;kBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyB,SAAS,KAAK;;;;;;oCACpD,eAAe,SAAS,MAAM;;;;;;;0CAGjC,8OAAC;gCAAE,WAAU;0CACV,SAAS,WAAW;;;;;;0CAGvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CACrB,SAAS,MAAM,EAAE,oBAAoB;4CAAM;;;;;;;kDAE9C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CACnB,IAAI,KAAK,SAAS,UAAU,EAAE,kBAAkB;;;;;;;oCAElD,SAAS,aAAa,kBACrB,8OAAC;;4CACE,SAAS,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;4CACxC,SAAS,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,EAAE,SAAS,aAAa,CAAC,MAAM,GAAG,GAAG;;;;;;;;;;;;;;;;;;;kCAMtF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE;0CACrC,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;4BAKnC,SAAS,MAAM,KAAK,yBACnB;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,WAAW,EAAE,SAAS,EAAE,CAAC,KAAK,CAAC;kDAC1C,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC7B,8OAAC,2MAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAKrC,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAS,IAAM,WAAW,SAAS,EAAE;wCACrC,UAAU;;4CAET,8BAAgB,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrF", "debugId": null}}]}