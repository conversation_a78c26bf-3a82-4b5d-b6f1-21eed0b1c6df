{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/chat-permissions.ts"], "sourcesContent": ["import { supabase } from '@/lib/supabase';\n\nexport interface ChatPermission {\n  id: string;\n  business_id: string;\n  influencer_id: string;\n  offer_id: string | null;\n  campaign_application_id: string | null;\n  business_approved: boolean | null;\n  influencer_approved: boolean | null;\n  chat_enabled: boolean | null;\n  created_at: string | null;\n  updated_at: string | null;\n}\n\n/**\n * Create or update chat permission for direct offer\n */\nexport async function upsertOfferChatPermission(\n  businessId: string,\n  influencerId: string,\n  offerId: string,\n  businessApproved: boolean = false,\n  influencerApproved: boolean = false\n) {\n  const { data, error } = await supabase.rpc('upsert_chat_permission', {\n    p_business_id: businessId,\n    p_influencer_id: influencerId,\n    p_offer_id: offerId,\n    p_business_approved: businessApproved,\n    p_influencer_approved: influencerApproved\n  });\n\n  if (error) {\n    console.error('Error upserting offer chat permission:', error);\n    throw error;\n  }\n\n  return data;\n}\n\n/**\n * Create or update chat permission for campaign application\n */\nexport async function upsertApplicationChatPermission(\n  businessId: string,\n  influencerId: string,\n  applicationId: string,\n  businessApproved: boolean = false,\n  influencerApproved: boolean = false\n) {\n  const { data, error } = await supabase.rpc('upsert_chat_permission', {\n    p_business_id: businessId,\n    p_influencer_id: influencerId,\n    p_campaign_application_id: applicationId,\n    p_business_approved: businessApproved,\n    p_influencer_approved: influencerApproved\n  });\n\n  if (error) {\n    console.error('Error upserting application chat permission:', error);\n    throw error;\n  }\n\n  return data;\n}\n\n/**\n * Check if chat is enabled between business and influencer for specific offer/application\n */\nexport async function isChatEnabled(\n  businessId: string,\n  influencerId: string,\n  offerId?: string,\n  applicationId?: string\n): Promise<boolean> {\n  let query = supabase\n    .from('chat_permissions')\n    .select('chat_enabled')\n    .eq('business_id', businessId)\n    .eq('influencer_id', influencerId);\n\n  if (offerId) {\n    query = query.eq('offer_id', offerId);\n  } else if (applicationId) {\n    query = query.eq('campaign_application_id', applicationId);\n  } else {\n    throw new Error('Either offerId or applicationId must be provided');\n  }\n\n  const { data, error } = await query.single();\n\n  if (error) {\n    if (error.code === 'PGRST116') {\n      // No permission record found, chat not enabled\n      return false;\n    }\n    console.error('Error checking chat permission:', error);\n    throw error;\n  }\n\n  return data?.chat_enabled || false;\n}\n\n/**\n * Get chat permission details\n */\nexport async function getChatPermission(\n  businessId: string,\n  influencerId: string,\n  offerId?: string,\n  applicationId?: string\n): Promise<ChatPermission | null> {\n  let query = supabase\n    .from('chat_permissions')\n    .select('*')\n    .eq('business_id', businessId)\n    .eq('influencer_id', influencerId);\n\n  if (offerId) {\n    query = query.eq('offer_id', offerId);\n  } else if (applicationId) {\n    query = query.eq('campaign_application_id', applicationId);\n  } else {\n    throw new Error('Either offerId or applicationId must be provided');\n  }\n\n  const { data, error } = await query.single();\n\n  if (error) {\n    if (error.code === 'PGRST116') {\n      // No permission record found\n      return null;\n    }\n    console.error('Error getting chat permission:', error);\n    throw error;\n  }\n\n  return data;\n}\n\n/**\n * Approve chat from business side\n */\nexport async function approveBusinessChat(\n  businessId: string,\n  influencerId: string,\n  offerId?: string,\n  applicationId?: string\n) {\n  let query = supabase\n    .from('chat_permissions')\n    .update({ business_approved: true })\n    .eq('business_id', businessId)\n    .eq('influencer_id', influencerId);\n\n  if (offerId) {\n    query = query.eq('offer_id', offerId);\n  } else if (applicationId) {\n    query = query.eq('campaign_application_id', applicationId);\n  } else {\n    throw new Error('Either offerId or applicationId must be provided');\n  }\n\n  const { error } = await query;\n\n  if (error) {\n    console.error('Error approving business chat:', error);\n    throw error;\n  }\n}\n\n/**\n * Approve chat from influencer side\n */\nexport async function approveInfluencerChat(\n  businessId: string,\n  influencerId: string,\n  offerId?: string,\n  applicationId?: string\n) {\n  let query = supabase\n    .from('chat_permissions')\n    .update({ influencer_approved: true })\n    .eq('business_id', businessId)\n    .eq('influencer_id', influencerId);\n\n  if (offerId) {\n    query = query.eq('offer_id', offerId);\n  } else if (applicationId) {\n    query = query.eq('campaign_application_id', applicationId);\n  } else {\n    throw new Error('Either offerId or applicationId must be provided');\n  }\n\n  const { error } = await query;\n\n  if (error) {\n    console.error('Error approving influencer chat:', error);\n    throw error;\n  }\n}\n\n/**\n * Get all chat permissions for a user (business or influencer)\n */\nexport async function getUserChatPermissions(userId: string): Promise<ChatPermission[]> {\n  const { data, error } = await supabase\n    .from('chat_permissions')\n    .select('*')\n    .or(`business_id.eq.${userId},influencer_id.eq.${userId}`)\n    .order('created_at', { ascending: false });\n\n  if (error) {\n    console.error('Error getting user chat permissions:', error);\n    throw error;\n  }\n\n  return data || [];\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAkBO,eAAe,0BACpB,UAAkB,EAClB,YAAoB,EACpB,OAAe,EACf,mBAA4B,KAAK,EACjC,qBAA8B,KAAK;IAEnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,0BAA0B;QACnE,eAAe;QACf,iBAAiB;QACjB,YAAY;QACZ,qBAAqB;QACrB,uBAAuB;IACzB;IAEA,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,0CAA0C;QACxD,MAAM;IACR;IAEA,OAAO;AACT;AAKO,eAAe,gCACpB,UAAkB,EAClB,YAAoB,EACpB,aAAqB,EACrB,mBAA4B,KAAK,EACjC,qBAA8B,KAAK;IAEnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,0BAA0B;QACnE,eAAe;QACf,iBAAiB;QACjB,2BAA2B;QAC3B,qBAAqB;QACrB,uBAAuB;IACzB;IAEA,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,MAAM;IACR;IAEA,OAAO;AACT;AAKO,eAAe,cACpB,UAAkB,EAClB,YAAoB,EACpB,OAAgB,EAChB,aAAsB;IAEtB,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,oBACL,MAAM,CAAC,gBACP,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,iBAAiB;IAEvB,IAAI,SAAS;QACX,QAAQ,MAAM,EAAE,CAAC,YAAY;IAC/B,OAAO,IAAI,eAAe;QACxB,QAAQ,MAAM,EAAE,CAAC,2BAA2B;IAC9C,OAAO;QACL,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,MAAM;IAE1C,IAAI,OAAO;QACT,IAAI,MAAM,IAAI,KAAK,YAAY;YAC7B,+CAA+C;YAC/C,OAAO;QACT;QACA,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;IAEA,OAAO,MAAM,gBAAgB;AAC/B;AAKO,eAAe,kBACpB,UAAkB,EAClB,YAAoB,EACpB,OAAgB,EAChB,aAAsB;IAEtB,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,iBAAiB;IAEvB,IAAI,SAAS;QACX,QAAQ,MAAM,EAAE,CAAC,YAAY;IAC/B,OAAO,IAAI,eAAe;QACxB,QAAQ,MAAM,EAAE,CAAC,2BAA2B;IAC9C,OAAO;QACL,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,MAAM;IAE1C,IAAI,OAAO;QACT,IAAI,MAAM,IAAI,KAAK,YAAY;YAC7B,6BAA6B;YAC7B,OAAO;QACT;QACA,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;IAEA,OAAO;AACT;AAKO,eAAe,oBACpB,UAAkB,EAClB,YAAoB,EACpB,OAAgB,EAChB,aAAsB;IAEtB,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,oBACL,MAAM,CAAC;QAAE,mBAAmB;IAAK,GACjC,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,iBAAiB;IAEvB,IAAI,SAAS;QACX,QAAQ,MAAM,EAAE,CAAC,YAAY;IAC/B,OAAO,IAAI,eAAe;QACxB,QAAQ,MAAM,EAAE,CAAC,2BAA2B;IAC9C,OAAO;QACL,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM;IAExB,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAKO,eAAe,sBACpB,UAAkB,EAClB,YAAoB,EACpB,OAAgB,EAChB,aAAsB;IAEtB,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,oBACL,MAAM,CAAC;QAAE,qBAAqB;IAAK,GACnC,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,iBAAiB;IAEvB,IAAI,SAAS;QACX,QAAQ,MAAM,EAAE,CAAC,YAAY;IAC/B,OAAO,IAAI,eAAe;QACxB,QAAQ,MAAM,EAAE,CAAC,2BAA2B;IAC9C,OAAO;QACL,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM;IAExB,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAKO,eAAe,uBAAuB,MAAc;IACzD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,EAAE,CAAC,CAAC,eAAe,EAAE,OAAO,kBAAkB,EAAE,QAAQ,EACxD,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,wCAAwC;QACtD,MAAM;IACR;IAEA,OAAO,QAAQ,EAAE;AACnB", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/campaigns.ts"], "sourcesContent": ["import { supabase } from './supabase';\nimport { upsertApplicationChatPermission } from './chat-permissions';\nimport { Database } from './database.types';\n\ntype Campaign = Database['public']['Tables']['campaigns']['Row'];\ntype CampaignInsert = Database['public']['Tables']['campaigns']['Insert'];\ntype CampaignUpdate = Database['public']['Tables']['campaigns']['Update'];\ntype CampaignApplication = Database['public']['Tables']['campaign_applications']['Row'];\ntype CampaignApplicationInsert = Database['public']['Tables']['campaign_applications']['Insert'];\n\n// Campaign CRUD operations\nexport async function createCampaign(campaign: CampaignInsert) {\n  const { data, error } = await supabase\n    .from('campaigns')\n    .insert(campaign)\n    .select()\n    .single();\n\n  return { data, error };\n}\n\nexport async function getCampaign(id: string) {\n  const { data, error } = await supabase\n    .from('campaigns')\n    .select(`\n      *,\n      businesses!inner(\n        company_name,\n        industry,\n        profiles!inner(\n          username,\n          avatar_url\n        )\n      )\n    `)\n    .eq('id', id)\n    .single();\n\n  return { data, error };\n}\n\nexport async function getCampaignWithDetails(id: string) {\n  const { data, error } = await supabase\n    .from('campaigns_with_details')\n    .select('*')\n    .eq('id', id)\n    .single();\n\n  return { data, error };\n}\n\n\n\nexport async function deleteCampaign(id: string) {\n  const { data, error } = await supabase\n    .from('campaigns')\n    .delete()\n    .eq('id', id);\n\n  return { data, error };\n}\n\n// Business campaigns\nexport async function getBusinessCampaigns(businessId: string, status?: string) {\n  let query = supabase\n    .from('campaigns')\n    .select(`\n      *,\n      campaign_applications(count)\n    `)\n    .eq('business_id', businessId)\n    .order('created_at', { ascending: false });\n\n  if (status) {\n    query = query.eq('status', status);\n  }\n\n  const { data, error } = await query;\n  return { data, error };\n}\n\n// Campaign search and filtering for influencers\nexport interface CampaignFilters {\n  search?: string;\n  categories?: number[];\n  platforms?: number[];\n  minBudget?: number;\n  maxBudget?: number;\n  location?: string;\n  minFollowers?: number;\n  maxFollowers?: number;\n  gender?: string;\n  deadlineBefore?: string;\n  sortBy?: 'created_at' | 'budget' | 'application_deadline' | 'applications_count';\n  sortOrder?: 'asc' | 'desc';\n  limit?: number;\n  offset?: number;\n}\n\nexport async function searchCampaigns(filters: CampaignFilters = {}) {\n  const {\n    search,\n    categories,\n    platforms,\n    minBudget,\n    maxBudget,\n    location,\n    minFollowers,\n    maxFollowers,\n    gender,\n    deadlineBefore,\n    sortBy = 'created_at',\n    sortOrder = 'desc',\n    limit = 20,\n    offset = 0\n  } = filters;\n\n  // Get current user for debugging\n  const { data: { user } } = await supabase.auth.getUser();\n\n  // Use RPC function to bypass RLS issues\n  const { data: allCampaigns, error: rpcError } = await supabase\n    .rpc('get_active_campaigns_for_influencers');\n\n  if (rpcError) {\n    console.error('RPC error:', rpcError);\n    return { data: [], error: rpcError };\n  }\n\n\n\n  // Apply client-side filtering\n  let filteredCampaigns = allCampaigns || [];\n\n  // Basic text search in title and description\n  if (search) {\n    const searchLower = search.toLowerCase();\n    filteredCampaigns = filteredCampaigns.filter(campaign =>\n      campaign.title.toLowerCase().includes(searchLower) ||\n      campaign.description.toLowerCase().includes(searchLower)\n    );\n  }\n\n  // Budget range\n  if (minBudget !== undefined) {\n    filteredCampaigns = filteredCampaigns.filter(campaign => campaign.budget >= minBudget);\n  }\n  if (maxBudget !== undefined) {\n    filteredCampaigns = filteredCampaigns.filter(campaign => campaign.budget <= maxBudget);\n  }\n\n  // Skip advanced filters for now since RPC returns basic fields only\n  // TODO: Add these filters back when we have all fields in RPC\n\n  // Sorting\n  filteredCampaigns.sort((a, b) => {\n    let aValue, bValue;\n\n    switch (sortBy) {\n      case 'budget':\n        aValue = a.budget;\n        bValue = b.budget;\n        break;\n      default: // 'created_at'\n        aValue = new Date(a.created_at).getTime();\n        bValue = new Date(b.created_at).getTime();\n    }\n\n    return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;\n  });\n\n  // Pagination\n  const startIndex = offset;\n  const endIndex = offset + limit;\n  const paginatedCampaigns = filteredCampaigns.slice(startIndex, endIndex);\n\n\n\n  return { data: paginatedCampaigns, error: null };\n}\n\n// Get campaign for editing (only for business owners and draft campaigns)\nexport async function getCampaignForEdit(campaignId: string) {\n  const { data: { user } } = await supabase.auth.getUser();\n\n  if (!user) {\n    return { data: null, error: { message: 'User not authenticated' } };\n  }\n\n  // Get campaign with all related data\n  const { data: campaign, error } = await supabase\n    .from('campaigns')\n    .select(`\n      *,\n      campaign_platforms (\n        platform_id,\n        platforms (name)\n      ),\n      campaign_categories (\n        category_id,\n        categories (name)\n      )\n    `)\n    .eq('id', campaignId)\n    .eq('business_id', user.id)\n    .eq('status', 'draft')\n    .single();\n\n  if (error) {\n    return { data: null, error };\n  }\n\n  if (!campaign) {\n    return { data: null, error: { message: 'Campaign not found or not editable' } };\n  }\n\n  return { data: campaign, error: null };\n}\n\n// Update campaign (only for business owners and draft campaigns)\nexport async function updateCampaign(campaignId: string, campaignData: any) {\n  const { data: { user } } = await supabase.auth.getUser();\n\n  if (!user) {\n    return { data: null, error: { message: 'User not authenticated' } };\n  }\n\n  // First check if campaign exists and is editable\n  const { data: existingCampaign, error: checkError } = await supabase\n    .from('campaigns')\n    .select('id, status, business_id')\n    .eq('id', campaignId)\n    .eq('business_id', user.id)\n    .eq('status', 'draft')\n    .single();\n\n  if (checkError || !existingCampaign) {\n    return { data: null, error: { message: 'Campaign not found or not editable' } };\n  }\n\n  // Update campaign\n  const { data: updatedCampaign, error: updateError } = await supabase\n    .from('campaigns')\n    .update({\n      title: campaignData.title,\n      description: campaignData.description,\n      budget: campaignData.budget,\n      content_types: campaignData.content_types,\n      min_followers: campaignData.min_followers,\n      max_followers: campaignData.max_followers,\n      age_range_min: campaignData.age_range_min,\n      age_range_max: campaignData.age_range_max,\n      gender: campaignData.gender,\n      updated_at: new Date().toISOString()\n    })\n    .eq('id', campaignId)\n    .select()\n    .single();\n\n  if (updateError) {\n    return { data: null, error: updateError };\n  }\n\n  // Update platforms\n  if (campaignData.platforms && campaignData.platforms.length > 0) {\n    // Delete existing platforms\n    await supabase\n      .from('campaign_platforms')\n      .delete()\n      .eq('campaign_id', campaignId);\n\n    // Insert new platforms\n    const platformInserts = campaignData.platforms.map((platformId: string) => ({\n      campaign_id: campaignId,\n      platform_id: platformId\n    }));\n\n    await supabase\n      .from('campaign_platforms')\n      .insert(platformInserts);\n  }\n\n  // Update categories\n  if (campaignData.categories && campaignData.categories.length > 0) {\n    // Delete existing categories\n    await supabase\n      .from('campaign_categories')\n      .delete()\n      .eq('campaign_id', campaignId);\n\n    // Insert new categories\n    const categoryInserts = campaignData.categories.map((categoryId: string) => ({\n      campaign_id: campaignId,\n      category_id: categoryId\n    }));\n\n    await supabase\n      .from('campaign_categories')\n      .insert(categoryInserts);\n  }\n\n  return { data: updatedCampaign, error: null };\n}\n\n// Update campaign status (with validation)\nexport async function updateCampaignStatus(campaignId: string, newStatus: 'draft' | 'active' | 'paused' | 'completed') {\n  const { data: { user } } = await supabase.auth.getUser();\n\n  if (!user) {\n    return { data: null, error: { message: 'User not authenticated' } };\n  }\n\n  // First check if campaign exists and user owns it\n  const { data: existingCampaign, error: checkError } = await supabase\n    .from('campaigns')\n    .select('id, status, business_id')\n    .eq('id', campaignId)\n    .eq('business_id', user.id)\n    .single();\n\n  if (checkError || !existingCampaign) {\n    return { data: null, error: { message: 'Campaign not found or access denied' } };\n  }\n\n  // Validation rules\n  if (existingCampaign.status === 'active' && newStatus === 'draft') {\n    return { data: null, error: { message: 'Cannot change active campaign back to draft' } };\n  }\n\n  if (existingCampaign.status === 'completed') {\n    return { data: null, error: { message: 'Cannot change status of completed campaign' } };\n  }\n\n  // Update status\n  const { data: updatedCampaign, error: updateError } = await supabase\n    .from('campaigns')\n    .update({\n      status: newStatus,\n      updated_at: new Date().toISOString()\n    })\n    .eq('id', campaignId)\n    .select()\n    .single();\n\n  if (updateError) {\n    return { data: null, error: updateError };\n  }\n\n  return { data: updatedCampaign, error: null };\n}\n\n// Get business campaigns for dashboard with counts\nexport async function getBusinessCampaignsForDashboard() {\n  const { data: { user } } = await supabase.auth.getUser();\n\n  if (!user) {\n    console.log('User not authenticated');\n    return { data: [], error: { message: 'User not authenticated' } };\n  }\n\n  console.log('Loading campaigns for business:', user.id);\n\n  const { data: campaigns, error } = await supabase\n    .from('campaigns')\n    .select(`\n      id,\n      title,\n      description,\n      budget,\n      status,\n      created_at,\n      content_types,\n      campaign_applications(count)\n    `)\n    .eq('business_id', user.id)\n    .order('created_at', { ascending: false });\n\n  console.log('Campaigns query result:', { campaigns, error });\n\n  if (error) {\n    console.error('Error loading campaigns:', error);\n    return { data: [], error };\n  }\n\n  return { data: campaigns || [], error: null };\n}\n\n// Featured campaigns - for now just return latest active campaigns\nexport async function getFeaturedCampaigns(limit: number = 6) {\n  const { data, error } = await supabase\n    .from('campaigns')\n    .select('*')\n    .eq('status', 'active')\n    .order('created_at', { ascending: false })\n    .limit(limit);\n\n  if (error) {\n    console.error('Featured campaigns error:', error);\n  }\n\n  return { data, error };\n}\n\n// Campaign applications\nexport async function createCampaignApplication(application: CampaignApplicationInsert) {\n  const { data, error } = await supabase\n    .from('campaign_applications')\n    .insert(application)\n    .select()\n    .single();\n\n  return { data, error };\n}\n\n// Check if influencer already applied to campaign\nexport async function hasInfluencerApplied(campaignId: string, influencerId: string) {\n  const { data, error } = await supabase\n    .from('campaign_applications')\n    .select('id, status, applied_at')\n    .eq('campaign_id', campaignId)\n    .eq('influencer_id', influencerId)\n    .single();\n\n  return { data, error };\n}\n\nexport async function getCampaignApplications(campaignId: string, status?: string) {\n  let query = supabase\n    .from('campaign_applications')\n    .select(`\n      *,\n      influencers!inner(\n        *,\n        profiles!inner(\n          username,\n          full_name,\n          avatar_url,\n          bio,\n          location\n        )\n      )\n    `)\n    .eq('campaign_id', campaignId)\n    .order('applied_at', { ascending: false });\n\n  if (status) {\n    query = query.eq('status', status);\n  }\n\n  const { data, error } = await query;\n  return { data, error };\n}\n\nexport async function getInfluencerApplications(influencerId: string, status?: string) {\n  try {\n    console.log('Fetching applications for influencer:', influencerId);\n\n    let query = supabase\n      .from('campaign_applications')\n      .select(`\n        id,\n        campaign_id,\n        influencer_id,\n        status,\n        proposed_rate,\n        proposal_text,\n        delivery_timeframe,\n        portfolio_links,\n        additional_services,\n        available_start_date,\n        experience_relevant,\n        audience_insights,\n        applied_at,\n        campaigns!inner(\n          id,\n          title,\n          description,\n          budget,\n          status,\n          business_id\n        )\n      `)\n      .eq('influencer_id', influencerId)\n      .order('applied_at', { ascending: false });\n\n    if (status) {\n      query = query.eq('status', status);\n    }\n\n    const { data: applicationsData, error: applicationsError } = await query;\n\n    if (applicationsError) {\n      console.error('Error fetching applications:', applicationsError);\n      throw applicationsError;\n    }\n\n    console.log('Applications data:', applicationsData);\n\n    if (!applicationsData || applicationsData.length === 0) {\n      return { data: [], error: null };\n    }\n\n    // Get business data separately\n    const businessIds = applicationsData.map(app => app.campaigns.business_id);\n    const { data: businessesData, error: businessesError } = await supabase\n      .from('businesses')\n      .select(`\n        id,\n        company_name\n      `)\n      .in('id', businessIds);\n\n    if (businessesError) {\n      console.error('Error fetching businesses:', businessesError);\n      throw businessesError;\n    }\n\n    // Get business profiles\n    const { data: profilesData, error: profilesError } = await supabase\n      .from('profiles')\n      .select('id, username, avatar_url')\n      .in('id', businessIds);\n\n    if (profilesError) {\n      console.error('Error fetching business profiles:', profilesError);\n    }\n\n    console.log('Businesses data:', businessesData);\n    console.log('Profiles data:', profilesData);\n\n    // Transform data to match expected interface\n    const transformedData = applicationsData.map(app => {\n      const business = businessesData?.find(b => b.id === app.campaigns.business_id);\n      const profile = profilesData?.find(p => p.id === app.campaigns.business_id);\n\n      return {\n        id: app.id,\n        campaign_id: app.campaign_id,\n        status: app.status,\n        proposed_rate: app.proposed_rate,\n        proposal_text: app.proposal_text,\n        portfolio_links: app.portfolio_links,\n        delivery_timeframe: app.delivery_timeframe,\n        additional_services: app.additional_services,\n        applied_at: app.applied_at,\n        campaigns: {\n          title: app.campaigns.title,\n          description: app.campaigns.description,\n          budget: app.campaigns.budget,\n          status: app.campaigns.status,\n          businesses: {\n            company_name: business?.company_name || 'Unknown Company',\n            profiles: {\n              username: profile?.username || 'Unknown',\n              avatar_url: profile?.avatar_url || null\n            }\n          }\n        }\n      };\n    });\n\n    return { data: transformedData, error: null };\n  } catch (error: any) {\n    console.error('Error in getInfluencerApplications:', error);\n    return { data: null, error: error.message };\n  }\n}\n\n\n\n// Campaign platforms and categories\nexport async function addCampaignPlatforms(campaignId: string, platforms: Array<{\n  platform_id: number;\n  content_type_ids: number[];\n  posts_required?: number;\n  budget_per_post?: number;\n}>) {\n  const platformData = platforms.map(platform => ({\n    campaign_id: campaignId,\n    ...platform\n  }));\n\n  const { data, error } = await supabase\n    .from('campaign_platforms')\n    .insert(platformData)\n    .select();\n\n  return { data, error };\n}\n\nexport async function addCampaignCategories(campaignId: string, categoryIds: number[]) {\n  const categoryData = categoryIds.map(categoryId => ({\n    campaign_id: campaignId,\n    category_id: categoryId\n  }));\n\n  const { data, error } = await supabase\n    .from('campaign_categories')\n    .insert(categoryData)\n    .select();\n\n  return { data, error };\n}\n\nexport async function removeCampaignPlatforms(campaignId: string) {\n  const { data, error } = await supabase\n    .from('campaign_platforms')\n    .delete()\n    .eq('campaign_id', campaignId);\n\n  return { data, error };\n}\n\nexport async function removeCampaignCategories(campaignId: string) {\n  const { data, error } = await supabase\n    .from('campaign_categories')\n    .delete()\n    .eq('campaign_id', campaignId);\n\n  return { data, error };\n}\n\n// Utility functions\n\nexport async function refreshCampaignsSearchView() {\n  const { data, error } = await supabase\n    .rpc('refresh_campaigns_search_view');\n\n  return { data, error };\n}\n\n// Get campaign statistics for business dashboard\nexport async function getCampaignStats(businessId: string) {\n  const { data: campaigns, error: campaignsError } = await supabase\n    .from('campaigns')\n    .select('status')\n    .eq('business_id', businessId);\n\n  if (campaignsError) return { data: null, error: campaignsError };\n\n  const { data: applications, error: applicationsError } = await supabase\n    .from('campaign_applications')\n    .select('status, campaign_id')\n    .in('campaign_id', campaigns?.map(c => c.id) || []);\n\n  if (applicationsError) return { data: null, error: applicationsError };\n\n  const stats = {\n    totalCampaigns: campaigns?.length || 0,\n    activeCampaigns: campaigns?.filter(c => c.status === 'active').length || 0,\n    completedCampaigns: campaigns?.filter(c => c.status === 'completed').length || 0,\n    totalApplications: applications?.length || 0,\n    pendingApplications: applications?.filter(a => a.status === 'pending').length || 0,\n    acceptedApplications: applications?.filter(a => a.status === 'accepted').length || 0,\n  };\n\n  return { data: stats, error: null };\n}\n\n\n\n// Get all applications for business campaigns\nexport async function getBusinessCampaignApplications(businessId: string, status?: string) {\n  try {\n    console.log('Fetching applications for business:', businessId);\n\n    // Use the correct field names from the actual database schema\n    let query = supabase\n      .from('campaign_applications')\n      .select(`\n        id,\n        campaign_id,\n        influencer_id,\n        status,\n        proposed_rate,\n        proposal_text,\n        delivery_timeframe,\n        portfolio_links,\n        additional_services,\n        available_start_date,\n        experience_relevant,\n        audience_insights,\n        applied_at,\n        campaigns!inner(\n          id,\n          title,\n          budget,\n          business_id\n        )\n      `)\n      .eq('campaigns.business_id', businessId)\n      .order('applied_at', { ascending: false });\n\n    if (status) {\n      query = query.eq('status', status);\n    }\n\n    const { data: applicationsData, error: applicationsError } = await query;\n\n    if (applicationsError) {\n      console.error('Error fetching applications:', applicationsError);\n      throw applicationsError;\n    }\n\n    console.log('Applications data:', applicationsData);\n\n    if (!applicationsData || applicationsData.length === 0) {\n      return { data: [], error: null };\n    }\n\n    // Get influencer data with profiles joined\n    const influencerIds = applicationsData.map(app => app.influencer_id);\n    const { data: influencersData, error: influencersError } = await supabase\n      .from('influencers')\n      .select(`\n        id,\n        profiles!inner(\n          id,\n          username,\n          full_name,\n          avatar_url\n        )\n      `)\n      .in('id', influencerIds);\n\n    if (influencersError) {\n      console.error('Error fetching influencers:', influencersError);\n      throw influencersError;\n    }\n\n    console.log('Influencers data:', influencersData);\n\n    // Transform data to match expected interface\n    const transformedData = applicationsData?.map(app => {\n      const influencer = influencersData?.find(inf => inf.id === app.influencer_id);\n      return {\n        id: app.id,\n        campaign_id: app.campaign_id,\n        influencer_id: app.influencer_id,\n        status: app.status,\n        proposed_rate: app.proposed_rate,\n        proposal_text: app.proposal_text,\n        delivery_timeframe: app.delivery_timeframe || 'Nije specificirano',\n        portfolio_links: app.portfolio_links || [],\n        experience_relevant: app.experience_relevant || app.proposal_text,\n        audience_insights: app.audience_insights || '',\n        additional_services: app.additional_services || '',\n        available_start_date: app.available_start_date,\n        applied_at: app.applied_at,\n        campaigns: {\n          id: app.campaigns.id,\n          title: app.campaigns.title,\n          budget: app.campaigns.budget,\n          business_id: app.campaigns.business_id\n        },\n        profiles: {\n          id: influencer?.profiles?.id || app.influencer_id,\n          username: influencer?.profiles?.username || 'Unknown',\n          full_name: influencer?.profiles?.full_name || 'Unknown User',\n          avatar_url: influencer?.profiles?.avatar_url || null\n        }\n      };\n    });\n\n    return { data: transformedData, error: null };\n  } catch (error: any) {\n    console.error('Error fetching campaign applications:', error);\n    return { data: null, error: error.message };\n  }\n}\n\n// Get single application with detailed info\nexport async function getCampaignApplication(applicationId: string) {\n  try {\n    const { data, error } = await supabase\n      .from('campaign_applications')\n      .select(`\n        id,\n        campaign_id,\n        influencer_id,\n        status,\n        proposed_price,\n        message,\n        portfolio_urls,\n        estimated_delivery_days,\n        additional_services,\n        applied_at,\n        campaigns!inner(\n          id,\n          title,\n          description,\n          budget,\n          requirements,\n          deliverables\n        ),\n        influencers!inner(\n          id,\n          full_name,\n          username,\n          avatar_url,\n          bio,\n          followers_count\n        )\n      `)\n      .eq('id', applicationId)\n      .single();\n\n    if (error) throw error;\n\n    // Get influencer categories and platforms\n    const { data: categoriesData } = await supabase\n      .from('influencer_categories')\n      .select('categories(name)')\n      .eq('influencer_id', data.influencer_id);\n\n    const { data: platformsData } = await supabase\n      .from('influencer_platforms')\n      .select(`\n        handle,\n        followers_count,\n        platforms(name)\n      `)\n      .eq('influencer_id', data.influencer_id);\n\n    // Transform data\n    const transformedData = {\n      id: data.id,\n      campaign_id: data.campaign_id,\n      influencer_id: data.influencer_id,\n      status: data.status,\n      proposed_price: data.proposed_price,\n      delivery_timeframe: `${data.estimated_delivery_days} dana`,\n      portfolio_links: data.portfolio_urls || [],\n      relevant_experience: data.message,\n      audience_insights: data.additional_services || '',\n      additional_services: [],\n      created_at: data.applied_at,\n      campaign: {\n        id: data.campaigns.id,\n        title: data.campaigns.title,\n        description: data.campaigns.description,\n        budget: data.campaigns.budget,\n        requirements: data.campaigns.requirements,\n        deliverables: data.campaigns.deliverables,\n      },\n      influencer: {\n        id: data.influencers.id,\n        full_name: data.influencers.full_name,\n        username: data.influencers.username,\n        avatar_url: data.influencers.avatar_url,\n        bio: data.influencers.bio,\n        followers_count: data.influencers.followers_count,\n        categories: categoriesData?.map(c => c.categories?.name).filter(Boolean) || [],\n        platforms: platformsData?.map(p => ({\n          platform_name: p.platforms?.name || '',\n          handle: p.handle,\n          followers_count: p.followers_count,\n        })) || [],\n      },\n    };\n\n    return { data: transformedData, error: null };\n  } catch (error: any) {\n    console.error('Error fetching campaign application:', error);\n    return { data: null, error: error.message };\n  }\n}\n\n// Update application status\nexport async function updateApplicationStatus(\n  applicationId: string,\n  status: 'accepted' | 'rejected',\n  rejectionReason?: string\n) {\n  try {\n    const updateData: any = { status };\n\n    if (status === 'rejected' && rejectionReason) {\n      updateData.rejection_reason = rejectionReason;\n    }\n\n    const { data, error } = await supabase\n      .from('campaign_applications')\n      .update(updateData)\n      .eq('id', applicationId)\n      .select(`\n        *,\n        campaigns!inner(business_id)\n      `)\n      .single();\n\n    if (error) throw error;\n\n    // Kreiraj chat dozvolu kada se aplikacija prihvati\n    if (status === 'accepted' && data) {\n      try {\n        // Business je odobrio prihvatanjem aplikacije\n        await upsertApplicationChatPermission(\n          data.campaigns.business_id,\n          data.influencer_id,\n          data.id,\n          true,  // business_approved - business je odobrio prihvatanjem\n          false  // influencer_approved - čeka da influencer odobri\n        );\n      } catch (chatError) {\n        console.error('Error creating chat permission:', chatError);\n        // Ne prekidamo proces ako chat dozvola ne uspije\n      }\n    }\n\n    return { data, error: null };\n  } catch (error: any) {\n    console.error('Error updating application status:', error);\n    return { data: null, error: error.message };\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAUO,eAAe,eAAe,QAAwB;IAC3D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,UACP,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,YAAY,EAAU;IAC1C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,CAAC;;;;;;;;;;IAUT,CAAC,EACA,EAAE,CAAC,MAAM,IACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,uBAAuB,EAAU;IACrD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,0BACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAIO,eAAe,eAAe,EAAU;IAC7C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,GACN,EAAE,CAAC,MAAM;IAEZ,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,eAAe,qBAAqB,UAAkB,EAAE,MAAe;IAC5E,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,aACL,MAAM,CAAC,CAAC;;;IAGT,CAAC,EACA,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,IAAI,QAAQ;QACV,QAAQ,MAAM,EAAE,CAAC,UAAU;IAC7B;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAC9B,OAAO;QAAE;QAAM;IAAM;AACvB;AAoBO,eAAe,gBAAgB,UAA2B,CAAC,CAAC;IACjE,MAAM,EACJ,MAAM,EACN,UAAU,EACV,SAAS,EACT,SAAS,EACT,SAAS,EACT,QAAQ,EACR,YAAY,EACZ,YAAY,EACZ,MAAM,EACN,cAAc,EACd,SAAS,YAAY,EACrB,YAAY,MAAM,EAClB,QAAQ,EAAE,EACV,SAAS,CAAC,EACX,GAAG;IAEJ,iCAAiC;IACjC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IAEtD,wCAAwC;IACxC,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,QAAQ,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC3D,GAAG,CAAC;IAEP,IAAI,UAAU;QACZ,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO;YAAE,MAAM,EAAE;YAAE,OAAO;QAAS;IACrC;IAIA,8BAA8B;IAC9B,IAAI,oBAAoB,gBAAgB,EAAE;IAE1C,6CAA6C;IAC7C,IAAI,QAAQ;QACV,MAAM,cAAc,OAAO,WAAW;QACtC,oBAAoB,kBAAkB,MAAM,CAAC,CAAA,WAC3C,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACtC,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC;IAEhD;IAEA,eAAe;IACf,IAAI,cAAc,WAAW;QAC3B,oBAAoB,kBAAkB,MAAM,CAAC,CAAA,WAAY,SAAS,MAAM,IAAI;IAC9E;IACA,IAAI,cAAc,WAAW;QAC3B,oBAAoB,kBAAkB,MAAM,CAAC,CAAA,WAAY,SAAS,MAAM,IAAI;IAC9E;IAEA,oEAAoE;IACpE,8DAA8D;IAE9D,UAAU;IACV,kBAAkB,IAAI,CAAC,CAAC,GAAG;QACzB,IAAI,QAAQ;QAEZ,OAAQ;YACN,KAAK;gBACH,SAAS,EAAE,MAAM;gBACjB,SAAS,EAAE,MAAM;gBACjB;YACF;gBACE,SAAS,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;gBACvC,SAAS,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;QAC3C;QAEA,OAAO,cAAc,QAAQ,SAAS,SAAS,SAAS;IAC1D;IAEA,aAAa;IACb,MAAM,aAAa;IACnB,MAAM,WAAW,SAAS;IAC1B,MAAM,qBAAqB,kBAAkB,KAAK,CAAC,YAAY;IAI/D,OAAO;QAAE,MAAM;QAAoB,OAAO;IAAK;AACjD;AAGO,eAAe,mBAAmB,UAAkB;IACzD,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IAEtD,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,MAAM;YAAM,OAAO;gBAAE,SAAS;YAAyB;QAAE;IACpE;IAEA,qCAAqC;IACrC,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7C,IAAI,CAAC,aACL,MAAM,CAAC,CAAC;;;;;;;;;;IAUT,CAAC,EACA,EAAE,CAAC,MAAM,YACT,EAAE,CAAC,eAAe,KAAK,EAAE,EACzB,EAAE,CAAC,UAAU,SACb,MAAM;IAET,IAAI,OAAO;QACT,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,IAAI,CAAC,UAAU;QACb,OAAO;YAAE,MAAM;YAAM,OAAO;gBAAE,SAAS;YAAqC;QAAE;IAChF;IAEA,OAAO;QAAE,MAAM;QAAU,OAAO;IAAK;AACvC;AAGO,eAAe,eAAe,UAAkB,EAAE,YAAiB;IACxE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IAEtD,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,MAAM;YAAM,OAAO;gBAAE,SAAS;YAAyB;QAAE;IACpE;IAEA,iDAAiD;IACjD,MAAM,EAAE,MAAM,gBAAgB,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACjE,IAAI,CAAC,aACL,MAAM,CAAC,2BACP,EAAE,CAAC,MAAM,YACT,EAAE,CAAC,eAAe,KAAK,EAAE,EACzB,EAAE,CAAC,UAAU,SACb,MAAM;IAET,IAAI,cAAc,CAAC,kBAAkB;QACnC,OAAO;YAAE,MAAM;YAAM,OAAO;gBAAE,SAAS;YAAqC;QAAE;IAChF;IAEA,kBAAkB;IAClB,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACjE,IAAI,CAAC,aACL,MAAM,CAAC;QACN,OAAO,aAAa,KAAK;QACzB,aAAa,aAAa,WAAW;QACrC,QAAQ,aAAa,MAAM;QAC3B,eAAe,aAAa,aAAa;QACzC,eAAe,aAAa,aAAa;QACzC,eAAe,aAAa,aAAa;QACzC,eAAe,aAAa,aAAa;QACzC,eAAe,aAAa,aAAa;QACzC,QAAQ,aAAa,MAAM;QAC3B,YAAY,IAAI,OAAO,WAAW;IACpC,GACC,EAAE,CAAC,MAAM,YACT,MAAM,GACN,MAAM;IAET,IAAI,aAAa;QACf,OAAO;YAAE,MAAM;YAAM,OAAO;QAAY;IAC1C;IAEA,mBAAmB;IACnB,IAAI,aAAa,SAAS,IAAI,aAAa,SAAS,CAAC,MAAM,GAAG,GAAG;QAC/D,4BAA4B;QAC5B,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,sBACL,MAAM,GACN,EAAE,CAAC,eAAe;QAErB,uBAAuB;QACvB,MAAM,kBAAkB,aAAa,SAAS,CAAC,GAAG,CAAC,CAAC,aAAuB,CAAC;gBAC1E,aAAa;gBACb,aAAa;YACf,CAAC;QAED,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,sBACL,MAAM,CAAC;IACZ;IAEA,oBAAoB;IACpB,IAAI,aAAa,UAAU,IAAI,aAAa,UAAU,CAAC,MAAM,GAAG,GAAG;QACjE,6BAA6B;QAC7B,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,uBACL,MAAM,GACN,EAAE,CAAC,eAAe;QAErB,wBAAwB;QACxB,MAAM,kBAAkB,aAAa,UAAU,CAAC,GAAG,CAAC,CAAC,aAAuB,CAAC;gBAC3E,aAAa;gBACb,aAAa;YACf,CAAC;QAED,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,uBACL,MAAM,CAAC;IACZ;IAEA,OAAO;QAAE,MAAM;QAAiB,OAAO;IAAK;AAC9C;AAGO,eAAe,qBAAqB,UAAkB,EAAE,SAAsD;IACnH,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IAEtD,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,MAAM;YAAM,OAAO;gBAAE,SAAS;YAAyB;QAAE;IACpE;IAEA,kDAAkD;IAClD,MAAM,EAAE,MAAM,gBAAgB,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACjE,IAAI,CAAC,aACL,MAAM,CAAC,2BACP,EAAE,CAAC,MAAM,YACT,EAAE,CAAC,eAAe,KAAK,EAAE,EACzB,MAAM;IAET,IAAI,cAAc,CAAC,kBAAkB;QACnC,OAAO;YAAE,MAAM;YAAM,OAAO;gBAAE,SAAS;YAAsC;QAAE;IACjF;IAEA,mBAAmB;IACnB,IAAI,iBAAiB,MAAM,KAAK,YAAY,cAAc,SAAS;QACjE,OAAO;YAAE,MAAM;YAAM,OAAO;gBAAE,SAAS;YAA8C;QAAE;IACzF;IAEA,IAAI,iBAAiB,MAAM,KAAK,aAAa;QAC3C,OAAO;YAAE,MAAM;YAAM,OAAO;gBAAE,SAAS;YAA6C;QAAE;IACxF;IAEA,gBAAgB;IAChB,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACjE,IAAI,CAAC,aACL,MAAM,CAAC;QACN,QAAQ;QACR,YAAY,IAAI,OAAO,WAAW;IACpC,GACC,EAAE,CAAC,MAAM,YACT,MAAM,GACN,MAAM;IAET,IAAI,aAAa;QACf,OAAO;YAAE,MAAM;YAAM,OAAO;QAAY;IAC1C;IAEA,OAAO;QAAE,MAAM;QAAiB,OAAO;IAAK;AAC9C;AAGO,eAAe;IACpB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IAEtD,IAAI,CAAC,MAAM;QACT,QAAQ,GAAG,CAAC;QACZ,OAAO;YAAE,MAAM,EAAE;YAAE,OAAO;gBAAE,SAAS;YAAyB;QAAE;IAClE;IAEA,QAAQ,GAAG,CAAC,mCAAmC,KAAK,EAAE;IAEtD,MAAM,EAAE,MAAM,SAAS,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC9C,IAAI,CAAC,aACL,MAAM,CAAC,CAAC;;;;;;;;;IAST,CAAC,EACA,EAAE,CAAC,eAAe,KAAK,EAAE,EACzB,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,QAAQ,GAAG,CAAC,2BAA2B;QAAE;QAAW;IAAM;IAE1D,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;YAAE,MAAM,EAAE;YAAE;QAAM;IAC3B;IAEA,OAAO;QAAE,MAAM,aAAa,EAAE;QAAE,OAAO;IAAK;AAC9C;AAGO,eAAe,qBAAqB,QAAgB,CAAC;IAC1D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,KACP,EAAE,CAAC,UAAU,UACb,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM,GACvC,KAAK,CAAC;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,6BAA6B;IAC7C;IAEA,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,eAAe,0BAA0B,WAAsC;IACpF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yBACL,MAAM,CAAC,aACP,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,eAAe,qBAAqB,UAAkB,EAAE,YAAoB;IACjF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yBACL,MAAM,CAAC,0BACP,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,iBAAiB,cACpB,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,wBAAwB,UAAkB,EAAE,MAAe;IAC/E,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,yBACL,MAAM,CAAC,CAAC;;;;;;;;;;;;IAYT,CAAC,EACA,EAAE,CAAC,eAAe,YAClB,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,IAAI,QAAQ;QACV,QAAQ,MAAM,EAAE,CAAC,UAAU;IAC7B;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAC9B,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,0BAA0B,YAAoB,EAAE,MAAe;IACnF,IAAI;QACF,QAAQ,GAAG,CAAC,yCAAyC;QAErD,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,yBACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;MAsBT,CAAC,EACA,EAAE,CAAC,iBAAiB,cACpB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,QAAQ;YACV,QAAQ,MAAM,EAAE,CAAC,UAAU;QAC7B;QAEA,MAAM,EAAE,MAAM,gBAAgB,EAAE,OAAO,iBAAiB,EAAE,GAAG,MAAM;QAEnE,IAAI,mBAAmB;YACrB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;QAEA,QAAQ,GAAG,CAAC,sBAAsB;QAElC,IAAI,CAAC,oBAAoB,iBAAiB,MAAM,KAAK,GAAG;YACtD,OAAO;gBAAE,MAAM,EAAE;gBAAE,OAAO;YAAK;QACjC;QAEA,+BAA+B;QAC/B,MAAM,cAAc,iBAAiB,GAAG,CAAC,CAAA,MAAO,IAAI,SAAS,CAAC,WAAW;QACzE,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACpE,IAAI,CAAC,cACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,EAAE,CAAC,MAAM;QAEZ,IAAI,iBAAiB;YACnB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;QAEA,wBAAwB;QACxB,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAChE,IAAI,CAAC,YACL,MAAM,CAAC,4BACP,EAAE,CAAC,MAAM;QAEZ,IAAI,eAAe;YACjB,QAAQ,KAAK,CAAC,qCAAqC;QACrD;QAEA,QAAQ,GAAG,CAAC,oBAAoB;QAChC,QAAQ,GAAG,CAAC,kBAAkB;QAE9B,6CAA6C;QAC7C,MAAM,kBAAkB,iBAAiB,GAAG,CAAC,CAAA;YAC3C,MAAM,WAAW,gBAAgB,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,IAAI,SAAS,CAAC,WAAW;YAC7E,MAAM,UAAU,cAAc,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,IAAI,SAAS,CAAC,WAAW;YAE1E,OAAO;gBACL,IAAI,IAAI,EAAE;gBACV,aAAa,IAAI,WAAW;gBAC5B,QAAQ,IAAI,MAAM;gBAClB,eAAe,IAAI,aAAa;gBAChC,eAAe,IAAI,aAAa;gBAChC,iBAAiB,IAAI,eAAe;gBACpC,oBAAoB,IAAI,kBAAkB;gBAC1C,qBAAqB,IAAI,mBAAmB;gBAC5C,YAAY,IAAI,UAAU;gBAC1B,WAAW;oBACT,OAAO,IAAI,SAAS,CAAC,KAAK;oBAC1B,aAAa,IAAI,SAAS,CAAC,WAAW;oBACtC,QAAQ,IAAI,SAAS,CAAC,MAAM;oBAC5B,QAAQ,IAAI,SAAS,CAAC,MAAM;oBAC5B,YAAY;wBACV,cAAc,UAAU,gBAAgB;wBACxC,UAAU;4BACR,UAAU,SAAS,YAAY;4BAC/B,YAAY,SAAS,cAAc;wBACrC;oBACF;gBACF;YACF;QACF;QAEA,OAAO;YAAE,MAAM;YAAiB,OAAO;QAAK;IAC9C,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;YAAE,MAAM;YAAM,OAAO,MAAM,OAAO;QAAC;IAC5C;AACF;AAKO,eAAe,qBAAqB,UAAkB,EAAE,SAK7D;IACA,MAAM,eAAe,UAAU,GAAG,CAAC,CAAA,WAAY,CAAC;YAC9C,aAAa;YACb,GAAG,QAAQ;QACb,CAAC;IAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,sBACL,MAAM,CAAC,cACP,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,sBAAsB,UAAkB,EAAE,WAAqB;IACnF,MAAM,eAAe,YAAY,GAAG,CAAC,CAAA,aAAc,CAAC;YAClD,aAAa;YACb,aAAa;QACf,CAAC;IAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,uBACL,MAAM,CAAC,cACP,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,wBAAwB,UAAkB;IAC9D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,sBACL,MAAM,GACN,EAAE,CAAC,eAAe;IAErB,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,eAAe,yBAAyB,UAAkB;IAC/D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,uBACL,MAAM,GACN,EAAE,CAAC,eAAe;IAErB,OAAO;QAAE;QAAM;IAAM;AACvB;AAIO,eAAe;IACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,GAAG,CAAC;IAEP,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,eAAe,iBAAiB,UAAkB;IACvD,MAAM,EAAE,MAAM,SAAS,EAAE,OAAO,cAAc,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC9D,IAAI,CAAC,aACL,MAAM,CAAC,UACP,EAAE,CAAC,eAAe;IAErB,IAAI,gBAAgB,OAAO;QAAE,MAAM;QAAM,OAAO;IAAe;IAE/D,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,iBAAiB,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACpE,IAAI,CAAC,yBACL,MAAM,CAAC,uBACP,EAAE,CAAC,eAAe,WAAW,IAAI,CAAA,IAAK,EAAE,EAAE,KAAK,EAAE;IAEpD,IAAI,mBAAmB,OAAO;QAAE,MAAM;QAAM,OAAO;IAAkB;IAErE,MAAM,QAAQ;QACZ,gBAAgB,WAAW,UAAU;QACrC,iBAAiB,WAAW,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,UAAU;QACzE,oBAAoB,WAAW,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,UAAU;QAC/E,mBAAmB,cAAc,UAAU;QAC3C,qBAAqB,cAAc,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,UAAU;QACjF,sBAAsB,cAAc,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,UAAU;IACrF;IAEA,OAAO;QAAE,MAAM;QAAO,OAAO;IAAK;AACpC;AAKO,eAAe,gCAAgC,UAAkB,EAAE,MAAe;IACvF,IAAI;QACF,QAAQ,GAAG,CAAC,uCAAuC;QAEnD,8DAA8D;QAC9D,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,yBACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;MAoBT,CAAC,EACA,EAAE,CAAC,yBAAyB,YAC5B,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,QAAQ;YACV,QAAQ,MAAM,EAAE,CAAC,UAAU;QAC7B;QAEA,MAAM,EAAE,MAAM,gBAAgB,EAAE,OAAO,iBAAiB,EAAE,GAAG,MAAM;QAEnE,IAAI,mBAAmB;YACrB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;QAEA,QAAQ,GAAG,CAAC,sBAAsB;QAElC,IAAI,CAAC,oBAAoB,iBAAiB,MAAM,KAAK,GAAG;YACtD,OAAO;gBAAE,MAAM,EAAE;gBAAE,OAAO;YAAK;QACjC;QAEA,2CAA2C;QAC3C,MAAM,gBAAgB,iBAAiB,GAAG,CAAC,CAAA,MAAO,IAAI,aAAa;QACnE,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,gBAAgB,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACtE,IAAI,CAAC,eACL,MAAM,CAAC,CAAC;;;;;;;;MAQT,CAAC,EACA,EAAE,CAAC,MAAM;QAEZ,IAAI,kBAAkB;YACpB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;QAEA,QAAQ,GAAG,CAAC,qBAAqB;QAEjC,6CAA6C;QAC7C,MAAM,kBAAkB,kBAAkB,IAAI,CAAA;YAC5C,MAAM,aAAa,iBAAiB,KAAK,CAAA,MAAO,IAAI,EAAE,KAAK,IAAI,aAAa;YAC5E,OAAO;gBACL,IAAI,IAAI,EAAE;gBACV,aAAa,IAAI,WAAW;gBAC5B,eAAe,IAAI,aAAa;gBAChC,QAAQ,IAAI,MAAM;gBAClB,eAAe,IAAI,aAAa;gBAChC,eAAe,IAAI,aAAa;gBAChC,oBAAoB,IAAI,kBAAkB,IAAI;gBAC9C,iBAAiB,IAAI,eAAe,IAAI,EAAE;gBAC1C,qBAAqB,IAAI,mBAAmB,IAAI,IAAI,aAAa;gBACjE,mBAAmB,IAAI,iBAAiB,IAAI;gBAC5C,qBAAqB,IAAI,mBAAmB,IAAI;gBAChD,sBAAsB,IAAI,oBAAoB;gBAC9C,YAAY,IAAI,UAAU;gBAC1B,WAAW;oBACT,IAAI,IAAI,SAAS,CAAC,EAAE;oBACpB,OAAO,IAAI,SAAS,CAAC,KAAK;oBAC1B,QAAQ,IAAI,SAAS,CAAC,MAAM;oBAC5B,aAAa,IAAI,SAAS,CAAC,WAAW;gBACxC;gBACA,UAAU;oBACR,IAAI,YAAY,UAAU,MAAM,IAAI,aAAa;oBACjD,UAAU,YAAY,UAAU,YAAY;oBAC5C,WAAW,YAAY,UAAU,aAAa;oBAC9C,YAAY,YAAY,UAAU,cAAc;gBAClD;YACF;QACF;QAEA,OAAO;YAAE,MAAM;YAAiB,OAAO;QAAK;IAC9C,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,yCAAyC;QACvD,OAAO;YAAE,MAAM;YAAM,OAAO,MAAM,OAAO;QAAC;IAC5C;AACF;AAGO,eAAe,uBAAuB,aAAqB;IAChE,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yBACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;MA2BT,CAAC,EACA,EAAE,CAAC,MAAM,eACT,MAAM;QAET,IAAI,OAAO,MAAM;QAEjB,0CAA0C;QAC1C,MAAM,EAAE,MAAM,cAAc,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC5C,IAAI,CAAC,yBACL,MAAM,CAAC,oBACP,EAAE,CAAC,iBAAiB,KAAK,aAAa;QAEzC,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC3C,IAAI,CAAC,wBACL,MAAM,CAAC,CAAC;;;;MAIT,CAAC,EACA,EAAE,CAAC,iBAAiB,KAAK,aAAa;QAEzC,iBAAiB;QACjB,MAAM,kBAAkB;YACtB,IAAI,KAAK,EAAE;YACX,aAAa,KAAK,WAAW;YAC7B,eAAe,KAAK,aAAa;YACjC,QAAQ,KAAK,MAAM;YACnB,gBAAgB,KAAK,cAAc;YACnC,oBAAoB,GAAG,KAAK,uBAAuB,CAAC,KAAK,CAAC;YAC1D,iBAAiB,KAAK,cAAc,IAAI,EAAE;YAC1C,qBAAqB,KAAK,OAAO;YACjC,mBAAmB,KAAK,mBAAmB,IAAI;YAC/C,qBAAqB,EAAE;YACvB,YAAY,KAAK,UAAU;YAC3B,UAAU;gBACR,IAAI,KAAK,SAAS,CAAC,EAAE;gBACrB,OAAO,KAAK,SAAS,CAAC,KAAK;gBAC3B,aAAa,KAAK,SAAS,CAAC,WAAW;gBACvC,QAAQ,KAAK,SAAS,CAAC,MAAM;gBAC7B,cAAc,KAAK,SAAS,CAAC,YAAY;gBACzC,cAAc,KAAK,SAAS,CAAC,YAAY;YAC3C;YACA,YAAY;gBACV,IAAI,KAAK,WAAW,CAAC,EAAE;gBACvB,WAAW,KAAK,WAAW,CAAC,SAAS;gBACrC,UAAU,KAAK,WAAW,CAAC,QAAQ;gBACnC,YAAY,KAAK,WAAW,CAAC,UAAU;gBACvC,KAAK,KAAK,WAAW,CAAC,GAAG;gBACzB,iBAAiB,KAAK,WAAW,CAAC,eAAe;gBACjD,YAAY,gBAAgB,IAAI,CAAA,IAAK,EAAE,UAAU,EAAE,MAAM,OAAO,YAAY,EAAE;gBAC9E,WAAW,eAAe,IAAI,CAAA,IAAK,CAAC;wBAClC,eAAe,EAAE,SAAS,EAAE,QAAQ;wBACpC,QAAQ,EAAE,MAAM;wBAChB,iBAAiB,EAAE,eAAe;oBACpC,CAAC,MAAM,EAAE;YACX;QACF;QAEA,OAAO;YAAE,MAAM;YAAiB,OAAO;QAAK;IAC9C,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO;YAAE,MAAM;YAAM,OAAO,MAAM,OAAO;QAAC;IAC5C;AACF;AAGO,eAAe,wBACpB,aAAqB,EACrB,MAA+B,EAC/B,eAAwB;IAExB,IAAI;QACF,MAAM,aAAkB;YAAE;QAAO;QAEjC,IAAI,WAAW,cAAc,iBAAiB;YAC5C,WAAW,gBAAgB,GAAG;QAChC;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,eACT,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,MAAM;QAET,IAAI,OAAO,MAAM;QAEjB,mDAAmD;QACnD,IAAI,WAAW,cAAc,MAAM;YACjC,IAAI;gBACF,8CAA8C;gBAC9C,MAAM,CAAA,GAAA,iIAAA,CAAA,kCAA+B,AAAD,EAClC,KAAK,SAAS,CAAC,WAAW,EAC1B,KAAK,aAAa,EAClB,KAAK,EAAE,EACP,MACA,MAAO,kDAAkD;;YAE7D,EAAE,OAAO,WAAW;gBAClB,QAAQ,KAAK,CAAC,mCAAmC;YACjD,iDAAiD;YACnD;QACF;QAEA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;YAAE,MAAM;YAAM,OAAO,MAAM,OAAO;QAAC;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 938, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 952, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1007, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAG7B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,qMAAA,CAAA,aAAgB,CAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,kKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1219, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,qMAAA,CAAA,aAAgB,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,qMAAA,CAAA,aAAgB,CAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,qMAAA,CAAA,aAAgB,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,qMAAA,CAAA,aAAgB,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1458, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { Check } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\n      className\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      <Check className=\"h-4 w-4\" />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,yBAAW,qMAAA,CAAA,aAAgB,CAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kTACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1501, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1673, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/campaigns/campaign-application-form.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport * as z from 'zod';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Checkbox } from '@/components/ui/checkbox';\nimport { Badge } from '@/components/ui/badge';\nimport { Loader2, Send, DollarSign, Calendar, FileText, Link as LinkIcon } from 'lucide-react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { createCampaignApplication } from '@/lib/campaigns';\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\n\n// Schema za validaciju\nconst applicationSchema = z.object({\n  proposedRate: z.number().min(10, 'Minimalna cijena je 10 KM').max(50000, 'Maksimalna cijena je 50,000 KM'),\n  proposalText: z.string().min(50, 'Prijedlog mora imati najmanje 50 karaktera').max(2000, 'Prijedlog je predugačak'),\n  deliveryTimeframe: z.string().min(1, 'Morate specificirati vremenski okvir'),\n  portfolioLinks: z.string().optional(),\n  additionalServices: z.string().optional(),\n  availableStartDate: z.string().optional(),\n  experienceRelevant: z.string().optional(),\n  audienceInsights: z.string().optional(),\n  agreeToTerms: z.boolean().refine(val => val === true, 'Morate se složiti sa uslovima'),\n});\n\ntype ApplicationForm = z.infer<typeof applicationSchema>;\n\ninterface Campaign {\n  id: string;\n  title: string;\n  budget: number;\n  description: string;\n  company_name: string;\n  platforms: Array<{\n    platform_name: string;\n    platform_icon: string;\n    posts_required: number;\n    budget_per_post: number;\n  }>;\n}\n\ninterface CampaignApplicationFormProps {\n  campaign: Campaign;\n  onSuccess: () => void;\n  onCancel: () => void;\n}\n\nexport function CampaignApplicationForm({ campaign, onSuccess, onCancel }: CampaignApplicationFormProps) {\n  const { user } = useAuth();\n  const [isLoading, setIsLoading] = useState(false);\n  const [showPreview, setShowPreview] = useState(false);\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    setValue,\n    watch,\n    setError,\n  } = useForm<ApplicationForm>({\n    resolver: zodResolver(applicationSchema),\n    defaultValues: {\n      proposedRate: campaign.budget,\n      deliveryTimeframe: '7-14 dana',\n      agreeToTerms: false,\n    },\n  });\n\n  const watchedValues = watch();\n\n  const onSubmit = async (data: ApplicationForm) => {\n    if (!user) return;\n\n    setIsLoading(true);\n\n    try {\n      const applicationData = {\n        campaign_id: campaign.id,\n        influencer_id: user.id,\n        proposed_rate: data.proposedRate,\n        proposal_text: data.proposalText,\n        delivery_timeframe: data.deliveryTimeframe,\n        portfolio_links: data.portfolioLinks ? data.portfolioLinks.split('\\n').filter(link => link.trim()) : null,\n        additional_services: data.additionalServices || null,\n        available_start_date: data.availableStartDate || null,\n        experience_relevant: data.experienceRelevant || null,\n        audience_insights: data.audienceInsights || null,\n        status: 'pending' as const,\n      };\n\n      const { error } = await createCampaignApplication(applicationData);\n\n      if (error) {\n        setError('root', { message: 'Greška pri slanju aplikacije' });\n        return;\n      }\n\n      onSuccess();\n    } catch (error) {\n      console.error('Error submitting application:', error);\n      setError('root', { message: 'Neočekivana greška' });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Campaign Summary */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <FileText className=\"h-5 w-5\" />\n            Aplikacija za kampanju\n          </CardTitle>\n          <CardDescription>\n            Pošaljite vašu ponudu za kampanju \"{campaign.title}\"\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg\">\n            <div>\n              <Label className=\"text-sm text-muted-foreground\">Biznis</Label>\n              <p className=\"font-medium\">{campaign.company_name}</p>\n            </div>\n            <div>\n              <Label className=\"text-sm text-muted-foreground\">Budžet</Label>\n              <p className=\"font-medium flex items-center gap-1\">\n                <DollarSign className=\"h-4 w-4\" />\n                {campaign.budget.toLocaleString()} KM\n              </p>\n            </div>\n            <div>\n              <Label className=\"text-sm text-muted-foreground\">Platforme</Label>\n              <div className=\"flex flex-wrap gap-1 mt-1\">\n                {campaign.platforms.map((platform, index) => (\n                  <Badge key={index} variant=\"outline\" className=\"text-xs\">\n                    <span className=\"mr-1\">{platform.platform_icon}</span>\n                    {platform.platform_name}\n                  </Badge>\n                ))}\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Application Form */}\n      <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n        <Card>\n          <CardHeader>\n            <CardTitle>Vaša ponuda</CardTitle>\n            <CardDescription>\n              Unesite detalje vaše ponude za ovu kampanju\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            {/* Proposed Rate */}\n            <div>\n              <Label htmlFor=\"proposedRate\">Predložena cijena (KM) *</Label>\n              <Input\n                id=\"proposedRate\"\n                type=\"number\"\n                {...register('proposedRate', { valueAsNumber: true })}\n                placeholder={campaign.budget.toString()}\n              />\n              {errors.proposedRate && (\n                <p className=\"text-sm text-destructive mt-1\">{errors.proposedRate.message}</p>\n              )}\n              <p className=\"text-xs text-muted-foreground mt-1\">\n                Budžet kampanje: {campaign.budget.toLocaleString()} KM\n              </p>\n            </div>\n\n            {/* Proposal Text */}\n            <div>\n              <Label htmlFor=\"proposalText\">Vaš prijedlog *</Label>\n              <Textarea\n                id=\"proposalText\"\n                {...register('proposalText')}\n                placeholder=\"Objasnite zašto ste savršen izbor za ovu kampanju, kako planirate pristupiti projektu, i šta možete ponuditi...\"\n                rows={6}\n              />\n              {errors.proposalText && (\n                <p className=\"text-sm text-destructive mt-1\">{errors.proposalText.message}</p>\n              )}\n              <p className=\"text-xs text-muted-foreground mt-1\">\n                {watchedValues.proposalText?.length || 0}/2000 karaktera\n              </p>\n            </div>\n\n            {/* Delivery Timeframe */}\n            <div>\n              <Label htmlFor=\"deliveryTimeframe\">Vremenski okvir izvršavanja *</Label>\n              <Select onValueChange={(value) => setValue('deliveryTimeframe', value)}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Izaberite vremenski okvir\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"1-3 dana\">1-3 dana</SelectItem>\n                  <SelectItem value=\"3-7 dana\">3-7 dana</SelectItem>\n                  <SelectItem value=\"7-14 dana\">7-14 dana</SelectItem>\n                  <SelectItem value=\"14-30 dana\">14-30 dana</SelectItem>\n                  <SelectItem value=\"30+ dana\">30+ dana</SelectItem>\n                  <SelectItem value=\"custom\">Prilagođeno (objasniti u prijedlogu)</SelectItem>\n                </SelectContent>\n              </Select>\n              {errors.deliveryTimeframe && (\n                <p className=\"text-sm text-destructive mt-1\">{errors.deliveryTimeframe.message}</p>\n              )}\n            </div>\n\n            {/* Available Start Date */}\n            <div>\n              <Label htmlFor=\"availableStartDate\">Dostupan od datuma</Label>\n              <Input\n                id=\"availableStartDate\"\n                type=\"date\"\n                {...register('availableStartDate')}\n              />\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Additional Information */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Dodatne informacije</CardTitle>\n            <CardDescription>\n              Pošaljite dodatne detalje koji mogu pomoći vašoj aplikaciji\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            {/* Portfolio Links */}\n            <div>\n              <Label htmlFor=\"portfolioLinks\">Portfolio linkovi</Label>\n              <Textarea\n                id=\"portfolioLinks\"\n                {...register('portfolioLinks')}\n                placeholder=\"https://instagram.com/post1&#10;https://tiktok.com/@username/video1&#10;https://youtube.com/watch?v=...\"\n                rows={4}\n              />\n              <p className=\"text-xs text-muted-foreground mt-1\">\n                Unesite jedan link po liniji. Pokažite vaš najbolji rad koji je relevantan za ovu kampanju.\n              </p>\n            </div>\n\n            {/* Relevant Experience */}\n            <div>\n              <Label htmlFor=\"experienceRelevant\">Relevantno iskustvo</Label>\n              <Textarea\n                id=\"experienceRelevant\"\n                {...register('experienceRelevant')}\n                placeholder=\"Opišite vaše prethodno iskustvo sa sličnim kampanjama ili brendovima...\"\n                rows={3}\n              />\n            </div>\n\n            {/* Audience Insights */}\n            <div>\n              <Label htmlFor=\"audienceInsights\">Informacije o publici</Label>\n              <Textarea\n                id=\"audienceInsights\"\n                {...register('audienceInsights')}\n                placeholder=\"Opišite vašu publiku - demografija, interesovanja, engagement rate...\"\n                rows={3}\n              />\n            </div>\n\n            {/* Additional Services */}\n            <div>\n              <Label htmlFor=\"additionalServices\">Dodatne usluge</Label>\n              <Textarea\n                id=\"additionalServices\"\n                {...register('additionalServices')}\n                placeholder=\"Dodatne usluge koje možete ponuditi (npr. dodatne objave, stories, reels...)\"\n                rows={3}\n              />\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Terms Agreement */}\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-start space-x-2\">\n              <Checkbox\n                id=\"agreeToTerms\"\n                checked={watchedValues.agreeToTerms}\n                onCheckedChange={(checked) => setValue('agreeToTerms', !!checked)}\n              />\n              <div className=\"grid gap-1.5 leading-none\">\n                <Label \n                  htmlFor=\"agreeToTerms\" \n                  className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n                >\n                  Slažem se sa uslovima korišćenja\n                </Label>\n                <p className=\"text-xs text-muted-foreground\">\n                  Slanjem aplikacije se slažete da će vaše informacije biti podijeljene sa biznisom koji je kreirao kampanju.\n                </p>\n              </div>\n            </div>\n            {errors.agreeToTerms && (\n              <p className=\"text-sm text-destructive mt-2\">{errors.agreeToTerms.message}</p>\n            )}\n          </CardContent>\n        </Card>\n\n        {/* Error message */}\n        {errors.root && (\n          <div className=\"p-3 bg-destructive/10 border border-destructive/20 rounded-lg\">\n            <p className=\"text-sm text-destructive\">{errors.root.message}</p>\n          </div>\n        )}\n\n        {/* Action buttons */}\n        <div className=\"flex justify-between\">\n          <Button type=\"button\" variant=\"outline\" onClick={onCancel}>\n            Otkaži\n          </Button>\n          <div className=\"space-x-2\">\n            <Dialog open={showPreview} onOpenChange={setShowPreview}>\n              <DialogTrigger asChild>\n                <Button type=\"button\" variant=\"outline\">\n                  Pregled\n                </Button>\n              </DialogTrigger>\n              <DialogContent className=\"max-w-2xl max-h-[80vh] overflow-y-auto\">\n                <DialogHeader>\n                  <DialogTitle>Pregled aplikacije</DialogTitle>\n                  <DialogDescription>\n                    Pregledajte vašu aplikaciju prije slanja\n                  </DialogDescription>\n                </DialogHeader>\n                <div className=\"space-y-4\">\n                  <div>\n                    <Label className=\"font-medium\">Kampanja:</Label>\n                    <p className=\"text-sm\">{campaign.title}</p>\n                  </div>\n                  <div>\n                    <Label className=\"font-medium\">Predložena cijena:</Label>\n                    <p className=\"text-sm\">{watchedValues.proposedRate?.toLocaleString()} KM</p>\n                  </div>\n                  <div>\n                    <Label className=\"font-medium\">Vremenski okvir:</Label>\n                    <p className=\"text-sm\">{watchedValues.deliveryTimeframe}</p>\n                  </div>\n                  <div>\n                    <Label className=\"font-medium\">Prijedlog:</Label>\n                    <p className=\"text-sm whitespace-pre-wrap\">{watchedValues.proposalText}</p>\n                  </div>\n                  {watchedValues.portfolioLinks && (\n                    <div>\n                      <Label className=\"font-medium\">Portfolio linkovi:</Label>\n                      <div className=\"text-sm space-y-1\">\n                        {watchedValues.portfolioLinks.split('\\n').filter(link => link.trim()).map((link, index) => (\n                          <div key={index} className=\"flex items-center gap-1\">\n                            <LinkIcon className=\"h-3 w-3\" />\n                            <span className=\"break-all\">{link.trim()}</span>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </DialogContent>\n            </Dialog>\n            <Button type=\"submit\" disabled={isLoading}>\n              {isLoading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n              <Send className=\"mr-2 h-4 w-4\" />\n              Pošalji aplikaciju\n            </Button>\n          </div>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAjBA;;;;;;;;;;;;;;;;;;AAmBA,uBAAuB;AACvB,MAAM,oBAAoB,+IAAA,CAAA,SAAQ,CAAC;IACjC,cAAc,+IAAA,CAAA,SAAQ,GAAG,GAAG,CAAC,IAAI,6BAA6B,GAAG,CAAC,OAAO;IACzE,cAAc,+IAAA,CAAA,SAAQ,GAAG,GAAG,CAAC,IAAI,8CAA8C,GAAG,CAAC,MAAM;IACzF,mBAAmB,+IAAA,CAAA,SAAQ,GAAG,GAAG,CAAC,GAAG;IACrC,gBAAgB,+IAAA,CAAA,SAAQ,GAAG,QAAQ;IACnC,oBAAoB,+IAAA,CAAA,SAAQ,GAAG,QAAQ;IACvC,oBAAoB,+IAAA,CAAA,SAAQ,GAAG,QAAQ;IACvC,oBAAoB,+IAAA,CAAA,SAAQ,GAAG,QAAQ;IACvC,kBAAkB,+IAAA,CAAA,SAAQ,GAAG,QAAQ;IACrC,cAAc,+IAAA,CAAA,UAAS,GAAG,MAAM,CAAC,CAAA,MAAO,QAAQ,MAAM;AACxD;AAwBO,SAAS,wBAAwB,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAgC;IACrG,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,QAAQ,EACR,KAAK,EACL,QAAQ,EACT,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,cAAc,SAAS,MAAM;YAC7B,mBAAmB;YACnB,cAAc;QAChB;IACF;IAEA,MAAM,gBAAgB;IAEtB,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,MAAM;QAEX,aAAa;QAEb,IAAI;YACF,MAAM,kBAAkB;gBACtB,aAAa,SAAS,EAAE;gBACxB,eAAe,KAAK,EAAE;gBACtB,eAAe,KAAK,YAAY;gBAChC,eAAe,KAAK,YAAY;gBAChC,oBAAoB,KAAK,iBAAiB;gBAC1C,iBAAiB,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,MAAM;gBACrG,qBAAqB,KAAK,kBAAkB,IAAI;gBAChD,sBAAsB,KAAK,kBAAkB,IAAI;gBACjD,qBAAqB,KAAK,kBAAkB,IAAI;gBAChD,mBAAmB,KAAK,gBAAgB,IAAI;gBAC5C,QAAQ;YACV;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,uHAAA,CAAA,4BAAyB,AAAD,EAAE;YAElD,IAAI,OAAO;gBACT,SAAS,QAAQ;oBAAE,SAAS;gBAA+B;gBAC3D;YACF;YAEA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,SAAS,QAAQ;gBAAE,SAAS;YAAqB;QACnD,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGlC,8OAAC,gIAAA,CAAA,kBAAe;;oCAAC;oCACqB,SAAS,KAAK;oCAAC;;;;;;;;;;;;;kCAGvD,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAgC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAe,SAAS,YAAY;;;;;;;;;;;;8CAEnD,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAgC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;;8DACX,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDACrB,SAAS,MAAM,CAAC,cAAc;gDAAG;;;;;;;;;;;;;8CAGtC,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAgC;;;;;;sDACjD,8OAAC;4CAAI,WAAU;sDACZ,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,sBACjC,8OAAC,iIAAA,CAAA,QAAK;oDAAa,SAAQ;oDAAU,WAAU;;sEAC7C,8OAAC;4DAAK,WAAU;sEAAQ,SAAS,aAAa;;;;;;wDAC7C,SAAS,aAAa;;mDAFb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYxB,8OAAC;gBAAK,UAAU,aAAa;gBAAW,WAAU;;kCAChD,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDAErB,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAe;;;;;;0DAC9B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACJ,GAAG,SAAS,gBAAgB;oDAAE,eAAe;gDAAK,EAAE;gDACrD,aAAa,SAAS,MAAM,CAAC,QAAQ;;;;;;4CAEtC,OAAO,YAAY,kBAClB,8OAAC;gDAAE,WAAU;0DAAiC,OAAO,YAAY,CAAC,OAAO;;;;;;0DAE3E,8OAAC;gDAAE,WAAU;;oDAAqC;oDAC9B,SAAS,MAAM,CAAC,cAAc;oDAAG;;;;;;;;;;;;;kDAKvD,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAe;;;;;;0DAC9B,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAG;gDACF,GAAG,SAAS,eAAe;gDAC5B,aAAY;gDACZ,MAAM;;;;;;4CAEP,OAAO,YAAY,kBAClB,8OAAC;gDAAE,WAAU;0DAAiC,OAAO,YAAY,CAAC,OAAO;;;;;;0DAE3E,8OAAC;gDAAE,WAAU;;oDACV,cAAc,YAAY,EAAE,UAAU;oDAAE;;;;;;;;;;;;;kDAK7C,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAoB;;;;;;0DACnC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,eAAe,CAAC,QAAU,SAAS,qBAAqB;;kEAC9D,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;0EAC7B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;0EAC7B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAY;;;;;;0EAC9B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAa;;;;;;0EAC/B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;0EAC7B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAS;;;;;;;;;;;;;;;;;;4CAG9B,OAAO,iBAAiB,kBACvB,8OAAC;gDAAE,WAAU;0DAAiC,OAAO,iBAAiB,CAAC,OAAO;;;;;;;;;;;;kDAKlF,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAqB;;;;;;0DACpC,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACJ,GAAG,SAAS,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;kCAO1C,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDAErB,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAiB;;;;;;0DAChC,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAG;gDACF,GAAG,SAAS,iBAAiB;gDAC9B,aAAY;gDACZ,MAAM;;;;;;0DAER,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;;;;;;;kDAMpD,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAqB;;;;;;0DACpC,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAG;gDACF,GAAG,SAAS,qBAAqB;gDAClC,aAAY;gDACZ,MAAM;;;;;;;;;;;;kDAKV,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAmB;;;;;;0DAClC,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAG;gDACF,GAAG,SAAS,mBAAmB;gDAChC,aAAY;gDACZ,MAAM;;;;;;;;;;;;kDAKV,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAqB;;;;;;0DACpC,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAG;gDACF,GAAG,SAAS,qBAAqB;gDAClC,aAAY;gDACZ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;kCAOd,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,SAAS,cAAc,YAAY;4CACnC,iBAAiB,CAAC,UAAY,SAAS,gBAAgB,CAAC,CAAC;;;;;;sDAE3D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDACJ,SAAQ;oDACR,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;gCAKhD,OAAO,YAAY,kBAClB,8OAAC;oCAAE,WAAU;8CAAiC,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;;;;;;oBAM9E,OAAO,IAAI,kBACV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAA4B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;kCAKhE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,SAAQ;gCAAU,SAAS;0CAAU;;;;;;0CAG3D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAM;wCAAa,cAAc;;0DACvC,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,OAAO;0DACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAS,SAAQ;8DAAU;;;;;;;;;;;0DAI1C,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;;kEACvB,8OAAC,kIAAA,CAAA,eAAY;;0EACX,8OAAC,kIAAA,CAAA,cAAW;0EAAC;;;;;;0EACb,8OAAC,kIAAA,CAAA,oBAAiB;0EAAC;;;;;;;;;;;;kEAIrB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;wEAAC,WAAU;kFAAc;;;;;;kFAC/B,8OAAC;wEAAE,WAAU;kFAAW,SAAS,KAAK;;;;;;;;;;;;0EAExC,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;wEAAC,WAAU;kFAAc;;;;;;kFAC/B,8OAAC;wEAAE,WAAU;;4EAAW,cAAc,YAAY,EAAE;4EAAiB;;;;;;;;;;;;;0EAEvE,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;wEAAC,WAAU;kFAAc;;;;;;kFAC/B,8OAAC;wEAAE,WAAU;kFAAW,cAAc,iBAAiB;;;;;;;;;;;;0EAEzD,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;wEAAC,WAAU;kFAAc;;;;;;kFAC/B,8OAAC;wEAAE,WAAU;kFAA+B,cAAc,YAAY;;;;;;;;;;;;4DAEvE,cAAc,cAAc,kBAC3B,8OAAC;;kFACC,8OAAC,iIAAA,CAAA,QAAK;wEAAC,WAAU;kFAAc;;;;;;kFAC/B,8OAAC;wEAAI,WAAU;kFACZ,cAAc,cAAc,CAAC,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC,MAAM,sBAC/E,8OAAC;gFAAgB,WAAU;;kGACzB,8OAAC,kMAAA,CAAA,OAAQ;wFAAC,WAAU;;;;;;kGACpB,8OAAC;wFAAK,WAAU;kGAAa,KAAK,IAAI;;;;;;;+EAF9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAWxB,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,UAAU;;4CAC7B,2BAAa,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACjC,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C", "debugId": null}}, {"offset": {"line": 2714, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/app/campaigns/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { usePara<PERSON>, useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { getCampaignWithDetails, hasInfluencerApplied } from '@/lib/campaigns';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Separator } from '@/components/ui/separator';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport {\n  ArrowLeft,\n  Loader2,\n  MapPin,\n  Calendar,\n  DollarSign,\n  Users,\n  Eye,\n  MessageCircle,\n  Share2,\n  Clock,\n  Target,\n  CheckCircle,\n  AlertCircle,\n  Send,\n  Edit\n} from 'lucide-react';\nimport Link from 'next/link';\nimport { formatDistanceToNow } from 'date-fns';\nimport { bs } from 'date-fns/locale';\nimport { CampaignApplicationForm } from '@/components/campaigns/campaign-application-form';\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\n\ninterface CampaignDetails {\n  id: string;\n  title: string;\n  description: string;\n  budget: number;\n  status: string;\n  location: string | null;\n  application_deadline: string | null;\n  min_followers: number | null;\n  max_followers: number | null;\n  age_range_min: number | null;\n  age_range_max: number | null;\n  gender: string | null;\n  requirements: string | null;\n  deliverables: string | null;\n  collaboration_type: string;\n  payment_terms: string | null;\n  usage_rights: string | null;\n  exclusivity_period: number | null;\n  revisions_included: number;\n  views_count: number;\n  applications_count: number;\n  created_at: string;\n  company_name: string;\n  industry: string;\n  business_username: string;\n  business_avatar: string | null;\n  platforms: Array<{\n    platform_id: number;\n    platform_name: string;\n    platform_icon: string;\n    content_type_ids: number[];\n    posts_required: number;\n    budget_per_post: number;\n  }>;\n  categories: Array<{\n    category_id: number;\n    category_name: string;\n    category_icon: string;\n  }>;\n}\n\nexport default function CampaignDetailsPage() {\n  const params = useParams();\n  const router = useRouter();\n  const { user, loading: authLoading } = useAuth();\n  const [campaign, setCampaign] = useState<CampaignDetails | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [showApplicationForm, setShowApplicationForm] = useState(false);\n  const [isOwner, setIsOwner] = useState(false);\n  const [applicationStatus, setApplicationStatus] = useState<{\n    hasApplied: boolean;\n    status?: string;\n    appliedAt?: string;\n  }>({ hasApplied: false });\n\n  const campaignId = params.id as string;\n\n  useEffect(() => {\n    if (!authLoading && !user) {\n      router.push('/prijava');\n      return;\n    }\n\n    if (user && campaignId) {\n      loadCampaign();\n    }\n  }, [user, authLoading, campaignId, router]);\n\n  const loadCampaign = async () => {\n    try {\n      setLoading(true);\n\n      const { data, error } = await getCampaignWithDetails(campaignId);\n      \n      if (error) {\n        setError('Greška pri učitavanju kampanje');\n        return;\n      }\n\n      if (!data) {\n        setError('Kampanja nije pronađena');\n        return;\n      }\n\n      setCampaign(data);\n\n      // Check if current user is the owner of the campaign\n      setIsOwner(data.business_id === user?.id);\n\n      // Check if influencer already applied (only for non-owners)\n      if (data.business_id !== user?.id && user?.id) {\n        const { data: applicationData } = await hasInfluencerApplied(campaignId, user.id);\n        if (applicationData) {\n          setApplicationStatus({\n            hasApplied: true,\n            status: applicationData.status,\n            appliedAt: applicationData.applied_at\n          });\n        }\n      }\n    } catch (err) {\n      setError('Neočekivana greška');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (authLoading || loading) {\n    return (\n      <div className=\"min-h-screen bg-background flex items-center justify-center\">\n        <Loader2 className=\"h-8 w-8 animate-spin\" />\n      </div>\n    );\n  }\n\n  if (error || !campaign) {\n    return (\n      <div className=\"min-h-screen bg-background flex items-center justify-center\">\n        <Card className=\"w-full max-w-md\">\n          <CardHeader className=\"text-center\">\n            <AlertCircle className=\"h-12 w-12 text-destructive mx-auto mb-4\" />\n            <CardTitle>Greška</CardTitle>\n            <CardDescription>{error || 'Kampanja nije pronađena'}</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <Button \n              variant=\"outline\" \n              className=\"w-full\"\n              onClick={() => router.back()}\n            >\n              Nazad\n            </Button>\n          </CardContent>\n        </Card>\n      </div>\n    );\n  }\n\n  const getStatusBadge = (status: string) => {\n    const statusMap = {\n      draft: { label: 'Nacrt', variant: 'secondary' as const },\n      active: { label: 'Aktivna', variant: 'default' as const },\n      paused: { label: 'Pauzirana', variant: 'outline' as const },\n      completed: { label: 'Završena', variant: 'secondary' as const },\n      cancelled: { label: 'Otkazana', variant: 'destructive' as const },\n    };\n    \n    const statusInfo = statusMap[status as keyof typeof statusMap] || statusMap.draft;\n    return <Badge variant={statusInfo.variant}>{statusInfo.label}</Badge>;\n  };\n\n  const getCollaborationTypeLabel = (type: string) => {\n    const typeMap = {\n      paid: 'Plaćena saradnja',\n      barter: 'Barter (razmena)',\n      hybrid: 'Hibridna saradnja'\n    };\n    return typeMap[type as keyof typeof typeMap] || type;\n  };\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Breadcrumb */}\n      <div className=\"border-b bg-muted/30\">\n        <div className=\"container mx-auto px-4 py-3\">\n          <nav className=\"flex items-center gap-2 text-sm text-muted-foreground\">\n            <Link href=\"/dashboard/biznis\" className=\"hover:text-foreground transition-colors\">\n              Dashboard\n            </Link>\n            <span>/</span>\n            <Link href=\"/dashboard/campaigns\" className=\"hover:text-foreground transition-colors\">\n              Kampanje\n            </Link>\n            <span>/</span>\n            <span className=\"text-foreground font-medium\">{campaign.title}</span>\n          </nav>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Main Content */}\n          <div className=\"lg:col-span-2 space-y-6\">\n            {/* Header */}\n            <div>\n              <div className=\"flex items-start justify-between mb-4\">\n                <div>\n                  <h1 className=\"text-3xl font-bold text-foreground mb-2\">\n                    {campaign.title}\n                  </h1>\n                  <div className=\"flex items-center gap-4 text-sm text-muted-foreground\">\n                    <span className=\"flex items-center gap-1\">\n                      <Eye className=\"h-4 w-4\" />\n                      {campaign.views_count} pregleda\n                    </span>\n                    <span className=\"flex items-center gap-1\">\n                      <MessageCircle className=\"h-4 w-4\" />\n                      {campaign.applications_count} aplikacija\n                    </span>\n                    <span className=\"flex items-center gap-1\">\n                      <Clock className=\"h-4 w-4\" />\n                      {formatDistanceToNow(new Date(campaign.created_at), { \n                        addSuffix: true, \n                        locale: bs \n                      })}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  {getStatusBadge(campaign.status)}\n                  <Button variant=\"outline\" size=\"sm\">\n                    <Share2 className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n              </div>\n            </div>\n\n            {/* Description */}\n            <Card>\n              <CardHeader>\n                <CardTitle>Opis kampanje</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <p className=\"text-muted-foreground leading-relaxed whitespace-pre-wrap\">\n                  {campaign.description}\n                </p>\n              </CardContent>\n            </Card>\n\n            {/* Platforms and Categories */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>Platforme</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-3\">\n                    {campaign.platforms.map((platform) => (\n                      <div key={platform.platform_id} className=\"flex items-center justify-between p-3 bg-muted/50 rounded-lg\">\n                        <div className=\"flex items-center gap-2\">\n                          <span className=\"text-lg\">{platform.platform_icon}</span>\n                          <span className=\"font-medium\">{platform.platform_name}</span>\n                        </div>\n                        <div className=\"text-sm text-muted-foreground\">\n                          {platform.posts_required} objava\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader>\n                  <CardTitle>Kategorije</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {campaign.categories.map((category) => (\n                      <Badge key={category.category_id} variant=\"secondary\" className=\"flex items-center gap-1\">\n                        <span>{category.category_icon}</span>\n                        {category.category_name}\n                      </Badge>\n                    ))}\n                  </div>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader>\n                  <CardTitle>Tipovi sadržaja</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {campaign.content_types?.map((contentType) => (\n                      <Badge key={contentType} variant=\"outline\" className=\"capitalize\">\n                        {contentType}\n                      </Badge>\n                    ))}\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n\n            {/* Requirements and Deliverables */}\n            {(campaign.requirements || campaign.deliverables) && (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {campaign.requirements && (\n                  <Card>\n                    <CardHeader>\n                      <CardTitle>Zahtevi</CardTitle>\n                    </CardHeader>\n                    <CardContent>\n                      <p className=\"text-muted-foreground whitespace-pre-wrap\">\n                        {campaign.requirements}\n                      </p>\n                    </CardContent>\n                  </Card>\n                )}\n\n                {campaign.deliverables && (\n                  <Card>\n                    <CardHeader>\n                      <CardTitle>Očekivani rezultati</CardTitle>\n                    </CardHeader>\n                    <CardContent>\n                      <p className=\"text-muted-foreground whitespace-pre-wrap\">\n                        {campaign.deliverables}\n                      </p>\n                    </CardContent>\n                  </Card>\n                )}\n              </div>\n            )}\n          </div>\n\n          {/* Sidebar */}\n          <div className=\"space-y-6\">\n            {/* Business Info */}\n            <Card>\n              <CardHeader>\n                <CardTitle>Biznis</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"flex items-center gap-3 mb-4\">\n                  <Avatar>\n                    <AvatarImage src={campaign.business_avatar || undefined} />\n                    <AvatarFallback>\n                      {campaign.company_name.charAt(0).toUpperCase()}\n                    </AvatarFallback>\n                  </Avatar>\n                  <div>\n                    <h3 className=\"font-medium\">{campaign.company_name}</h3>\n                    <p className=\"text-sm text-muted-foreground\">@{campaign.business_username}</p>\n                  </div>\n                </div>\n                {campaign.industry && (\n                  <p className=\"text-sm text-muted-foreground\">\n                    Industrija: {campaign.industry}\n                  </p>\n                )}\n              </CardContent>\n            </Card>\n\n            {/* Campaign Details */}\n            <Card>\n              <CardHeader>\n                <CardTitle>Detalji kampanje</CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-muted-foreground\">Budžet</span>\n                  <span className=\"font-medium flex items-center gap-1\">\n                    <DollarSign className=\"h-4 w-4\" />\n                    {campaign.budget.toLocaleString()} KM\n                  </span>\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-muted-foreground\">Tip saradnje</span>\n                  <span className=\"font-medium\">\n                    {getCollaborationTypeLabel(campaign.collaboration_type)}\n                  </span>\n                </div>\n\n                {campaign.location && (\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-muted-foreground\">Lokacija</span>\n                    <span className=\"font-medium flex items-center gap-1\">\n                      <MapPin className=\"h-4 w-4\" />\n                      {campaign.location}\n                    </span>\n                  </div>\n                )}\n\n                {campaign.application_deadline && (\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-muted-foreground\">Rok za prijave</span>\n                    <span className=\"font-medium flex items-center gap-1\">\n                      <Calendar className=\"h-4 w-4\" />\n                      {new Date(campaign.application_deadline).toLocaleDateString('bs-BA')}\n                    </span>\n                  </div>\n                )}\n\n                {(campaign.min_followers || campaign.max_followers) && (\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-muted-foreground\">Broj pratilaca</span>\n                    <span className=\"font-medium flex items-center gap-1\">\n                      <Users className=\"h-4 w-4\" />\n                      {campaign.min_followers?.toLocaleString() || '0'} - {campaign.max_followers?.toLocaleString() || '∞'}\n                    </span>\n                  </div>\n                )}\n\n                {(campaign.age_range_min || campaign.age_range_max) && (\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-muted-foreground\">Uzrast ciljne grupe</span>\n                    <span className=\"font-medium\">\n                      {campaign.age_range_min || '13'} - {campaign.age_range_max || '65'} godina\n                    </span>\n                  </div>\n                )}\n\n                {campaign.gender && campaign.gender !== 'all' && (\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-muted-foreground\">Pol ciljne grupe</span>\n                    <span className=\"font-medium\">\n                      {campaign.gender === 'male' ? 'Muški' : 'Ženski'}\n                    </span>\n                  </div>\n                )}\n\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-muted-foreground\">Revizije uključene</span>\n                  <span className=\"font-medium\">{campaign.revisions_included}</span>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Action Buttons - Different for Owner vs Influencer */}\n            <div className=\"space-y-3\">\n              {isOwner ? (\n                // Owner (Business) View\n                <>\n                  <Button\n                    className=\"w-full\"\n                    size=\"lg\"\n                    onClick={() => router.push(`/dashboard/biznis/applications?campaign=${campaign.id}`)}\n                  >\n                    <Users className=\"mr-2 h-5 w-5\" />\n                    Pregled aplikacija ({campaign.applications_count})\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    className=\"w-full\"\n                    size=\"lg\"\n                    onClick={() => router.push(`/campaigns/${campaign.id}/edit`)}\n                  >\n                    <Edit className=\"mr-2 h-5 w-5\" />\n                    Uredi kampanju\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    className=\"w-full\"\n                    size=\"lg\"\n                    onClick={() => router.push('/dashboard/biznis')}\n                  >\n                    <ArrowLeft className=\"mr-2 h-5 w-5\" />\n                    Nazad na dashboard\n                  </Button>\n                </>\n              ) : (\n                // Influencer View\n                <>\n                  {applicationStatus.hasApplied ? (\n                    // Show application status\n                    <Card className=\"w-full\">\n                      <CardContent className=\"pt-6\">\n                        <div className=\"flex items-center gap-3 mb-4\">\n                          <CheckCircle className=\"h-6 w-6 text-green-500\" />\n                          <div>\n                            <h3 className=\"font-semibold\">Aplikacija poslana</h3>\n                            <p className=\"text-sm text-muted-foreground\">\n                              Prijavili ste se na ovu kampanju {applicationStatus.appliedAt && formatDistanceToNow(new Date(applicationStatus.appliedAt), { addSuffix: true, locale: bs })}\n                            </p>\n                          </div>\n                        </div>\n                        <div className=\"flex items-center gap-2\">\n                          <Badge variant={\n                            applicationStatus.status === 'accepted' ? 'default' :\n                            applicationStatus.status === 'rejected' ? 'destructive' : 'secondary'\n                          }>\n                            {applicationStatus.status === 'pending' && 'Čeka se odgovor'}\n                            {applicationStatus.status === 'accepted' && 'Prihvaćeno'}\n                            {applicationStatus.status === 'rejected' && 'Odbačeno'}\n                          </Badge>\n                        </div>\n                        {applicationStatus.status === 'pending' && (\n                          <p className=\"text-sm text-muted-foreground mt-2\">\n                            Biznis će uskoro pregledati vašu aplikaciju i odgovoriti.\n                          </p>\n                        )}\n                      </CardContent>\n                    </Card>\n                  ) : (\n                    // Show application form\n                    <Dialog open={showApplicationForm} onOpenChange={setShowApplicationForm}>\n                      <DialogTrigger asChild>\n                        <Button className=\"w-full\" size=\"lg\">\n                          <Send className=\"mr-2 h-5 w-5\" />\n                          Pošaljite ponudu\n                        </Button>\n                      </DialogTrigger>\n                      <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-y-auto\">\n                        <DialogHeader>\n                          <DialogTitle>Aplikacija za kampanju</DialogTitle>\n                          <DialogDescription>\n                            Pošaljite vašu ponudu za kampanju \"{campaign.title}\"\n                          </DialogDescription>\n                        </DialogHeader>\n                        <CampaignApplicationForm\n                          campaign={{\n                            id: campaign.id,\n                            title: campaign.title,\n                            budget: campaign.budget,\n                            description: campaign.description,\n                            company_name: campaign.company_name,\n                            platforms: campaign.platforms\n                          }}\n                          onSuccess={() => {\n                            setShowApplicationForm(false);\n                            // Reload to show application status\n                            loadCampaign();\n                          }}\n                          onCancel={() => setShowApplicationForm(false)}\n                        />\n                      </DialogContent>\n                    </Dialog>\n                  )}\n                  <Button variant=\"outline\" className=\"w-full\" size=\"lg\">\n                    <MessageCircle className=\"mr-2 h-5 w-5\" />\n                    Kontaktiraj biznis\n                  </Button>\n                </>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AACA;AACA;AACA;AACA;AAhCA;;;;;;;;;;;;;;;;AA4Ee,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAItD;QAAE,YAAY;IAAM;IAEvB,MAAM,aAAa,OAAO,EAAE;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe,CAAC,MAAM;YACzB,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI,QAAQ,YAAY;YACtB;QACF;IACF,GAAG;QAAC;QAAM;QAAa;QAAY;KAAO;IAE1C,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YAEX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,uHAAA,CAAA,yBAAsB,AAAD,EAAE;YAErD,IAAI,OAAO;gBACT,SAAS;gBACT;YACF;YAEA,IAAI,CAAC,MAAM;gBACT,SAAS;gBACT;YACF;YAEA,YAAY;YAEZ,qDAAqD;YACrD,WAAW,KAAK,WAAW,KAAK,MAAM;YAEtC,4DAA4D;YAC5D,IAAI,KAAK,WAAW,KAAK,MAAM,MAAM,MAAM,IAAI;gBAC7C,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,CAAA,GAAA,uHAAA,CAAA,uBAAoB,AAAD,EAAE,YAAY,KAAK,EAAE;gBAChF,IAAI,iBAAiB;oBACnB,qBAAqB;wBACnB,YAAY;wBACZ,QAAQ,gBAAgB,MAAM;wBAC9B,WAAW,gBAAgB,UAAU;oBACvC;gBACF;YACF;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,eAAe,SAAS;QAC1B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,iNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;;;;;;IAGzB;IAEA,IAAI,SAAS,CAAC,UAAU;QACtB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAE,SAAS;;;;;;;;;;;;kCAE7B,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS,IAAM,OAAO,IAAI;sCAC3B;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,YAAY;YAChB,OAAO;gBAAE,OAAO;gBAAS,SAAS;YAAqB;YACvD,QAAQ;gBAAE,OAAO;gBAAW,SAAS;YAAmB;YACxD,QAAQ;gBAAE,OAAO;gBAAa,SAAS;YAAmB;YAC1D,WAAW;gBAAE,OAAO;gBAAY,SAAS;YAAqB;YAC9D,WAAW;gBAAE,OAAO;gBAAY,SAAS;YAAuB;QAClE;QAEA,MAAM,aAAa,SAAS,CAAC,OAAiC,IAAI,UAAU,KAAK;QACjF,qBAAO,8OAAC,iIAAA,CAAA,QAAK;YAAC,SAAS,WAAW,OAAO;sBAAG,WAAW,KAAK;;;;;;IAC9D;IAEA,MAAM,4BAA4B,CAAC;QACjC,MAAM,UAAU;YACd,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QACA,OAAO,OAAO,CAAC,KAA6B,IAAI;IAClD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAoB,WAAU;0CAA0C;;;;;;0CAGnF,8OAAC;0CAAK;;;;;;0CACN,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAuB,WAAU;0CAA0C;;;;;;0CAGtF,8OAAC;0CAAK;;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAA+B,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;0BAMnE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;8CACC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEACX,SAAS,KAAK;;;;;;kEAEjB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;;kFACd,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEACd,SAAS,WAAW;oEAAC;;;;;;;0EAExB,8OAAC;gEAAK,WAAU;;kFACd,8OAAC,wNAAA,CAAA,gBAAa;wEAAC,WAAU;;;;;;oEACxB,SAAS,kBAAkB;oEAAC;;;;;;;0EAE/B,8OAAC;gEAAK,WAAU;;kFACd,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAChB,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,SAAS,UAAU,GAAG;wEAClD,WAAW;wEACX,QAAQ,2IAAA,CAAA,KAAE;oEACZ;;;;;;;;;;;;;;;;;;;0DAIN,8OAAC;gDAAI,WAAU;;oDACZ,eAAe,SAAS,MAAM;kEAC/B,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,MAAK;kEAC7B,cAAA,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO1B,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAE,WAAU;0DACV,SAAS,WAAW;;;;;;;;;;;;;;;;;8CAM3B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;8DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;8DAEb,8OAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC;wDAAI,WAAU;kEACZ,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC,yBACvB,8OAAC;gEAA+B,WAAU;;kFACxC,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAW,SAAS,aAAa;;;;;;0FACjD,8OAAC;gFAAK,WAAU;0FAAe,SAAS,aAAa;;;;;;;;;;;;kFAEvD,8OAAC;wEAAI,WAAU;;4EACZ,SAAS,cAAc;4EAAC;;;;;;;;+DANnB,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;sDActC,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;8DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;8DAEb,8OAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC;wDAAI,WAAU;kEACZ,SAAS,UAAU,CAAC,GAAG,CAAC,CAAC,yBACxB,8OAAC,iIAAA,CAAA,QAAK;gEAA4B,SAAQ;gEAAY,WAAU;;kFAC9D,8OAAC;kFAAM,SAAS,aAAa;;;;;;oEAC5B,SAAS,aAAa;;+DAFb,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;sDASxC,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;8DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;8DAEb,8OAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC;wDAAI,WAAU;kEACZ,SAAS,aAAa,EAAE,IAAI,CAAC,4BAC5B,8OAAC,iIAAA,CAAA,QAAK;gEAAmB,SAAQ;gEAAU,WAAU;0EAClD;+DADS;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAUrB,CAAC,SAAS,YAAY,IAAI,SAAS,YAAY,mBAC9C,8OAAC;oCAAI,WAAU;;wCACZ,SAAS,YAAY,kBACpB,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;8DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;8DAEb,8OAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC;wDAAE,WAAU;kEACV,SAAS,YAAY;;;;;;;;;;;;;;;;;wCAM7B,SAAS,YAAY,kBACpB,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;8DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;8DAEb,8OAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC;wDAAE,WAAU;kEACV,SAAS,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUpC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,8OAAC,gIAAA,CAAA,cAAW;;8DACV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;;8EACL,8OAAC,kIAAA,CAAA,cAAW;oEAAC,KAAK,SAAS,eAAe,IAAI;;;;;;8EAC9C,8OAAC,kIAAA,CAAA,iBAAc;8EACZ,SAAS,YAAY,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;sEAGhD,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAe,SAAS,YAAY;;;;;;8EAClD,8OAAC;oEAAE,WAAU;;wEAAgC;wEAAE,SAAS,iBAAiB;;;;;;;;;;;;;;;;;;;gDAG5E,SAAS,QAAQ,kBAChB,8OAAC;oDAAE,WAAU;;wDAAgC;wDAC9B,SAAS,QAAQ;;;;;;;;;;;;;;;;;;;8CAOtC,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgC;;;;;;sEAChD,8OAAC;4DAAK,WAAU;;8EACd,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEACrB,SAAS,MAAM,CAAC,cAAc;gEAAG;;;;;;;;;;;;;8DAItC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgC;;;;;;sEAChD,8OAAC;4DAAK,WAAU;sEACb,0BAA0B,SAAS,kBAAkB;;;;;;;;;;;;gDAIzD,SAAS,QAAQ,kBAChB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgC;;;;;;sEAChD,8OAAC;4DAAK,WAAU;;8EACd,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEACjB,SAAS,QAAQ;;;;;;;;;;;;;gDAKvB,SAAS,oBAAoB,kBAC5B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgC;;;;;;sEAChD,8OAAC;4DAAK,WAAU;;8EACd,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACnB,IAAI,KAAK,SAAS,oBAAoB,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;gDAKjE,CAAC,SAAS,aAAa,IAAI,SAAS,aAAa,mBAChD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgC;;;;;;sEAChD,8OAAC;4DAAK,WAAU;;8EACd,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAChB,SAAS,aAAa,EAAE,oBAAoB;gEAAI;gEAAI,SAAS,aAAa,EAAE,oBAAoB;;;;;;;;;;;;;gDAKtG,CAAC,SAAS,aAAa,IAAI,SAAS,aAAa,mBAChD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgC;;;;;;sEAChD,8OAAC;4DAAK,WAAU;;gEACb,SAAS,aAAa,IAAI;gEAAK;gEAAI,SAAS,aAAa,IAAI;gEAAK;;;;;;;;;;;;;gDAKxE,SAAS,MAAM,IAAI,SAAS,MAAM,KAAK,uBACtC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgC;;;;;;sEAChD,8OAAC;4DAAK,WAAU;sEACb,SAAS,MAAM,KAAK,SAAS,UAAU;;;;;;;;;;;;8DAK9C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgC;;;;;;sEAChD,8OAAC;4DAAK,WAAU;sEAAe,SAAS,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;8CAMhE,8OAAC;oCAAI,WAAU;8CACZ,UACC,wBAAwB;kDACxB;;0DACE,8OAAC,kIAAA,CAAA,SAAM;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,wCAAwC,EAAE,SAAS,EAAE,EAAE;;kEAEnF,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;oDACb,SAAS,kBAAkB;oDAAC;;;;;;;0DAEnD,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;gDACV,MAAK;gDACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,SAAS,EAAE,CAAC,KAAK,CAAC;;kEAE3D,8OAAC,2MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;gDACV,MAAK;gDACL,SAAS,IAAM,OAAO,IAAI,CAAC;;kEAE3B,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;uDAK1C,kBAAkB;kDAClB;;4CACG,kBAAkB,UAAU,GAC3B,0BAA0B;0DAC1B,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;0DACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;8EACvB,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFAAgB;;;;;;sFAC9B,8OAAC;4EAAE,WAAU;;gFAAgC;gFACT,kBAAkB,SAAS,IAAI,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,kBAAkB,SAAS,GAAG;oFAAE,WAAW;oFAAM,QAAQ,2IAAA,CAAA,KAAE;gFAAC;;;;;;;;;;;;;;;;;;;sEAIhK,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SACL,kBAAkB,MAAM,KAAK,aAAa,YAC1C,kBAAkB,MAAM,KAAK,aAAa,gBAAgB;;oEAEzD,kBAAkB,MAAM,KAAK,aAAa;oEAC1C,kBAAkB,MAAM,KAAK,cAAc;oEAC3C,kBAAkB,MAAM,KAAK,cAAc;;;;;;;;;;;;wDAG/C,kBAAkB,MAAM,KAAK,2BAC5B,8OAAC;4DAAE,WAAU;sEAAqC;;;;;;;;;;;;;;;;uDAOxD,wBAAwB;0DACxB,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAM;gDAAqB,cAAc;;kEAC/C,8OAAC,kIAAA,CAAA,gBAAa;wDAAC,OAAO;kEACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,WAAU;4DAAS,MAAK;;8EAC9B,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;kEAIrC,8OAAC,kIAAA,CAAA,gBAAa;wDAAC,WAAU;;0EACvB,8OAAC,kIAAA,CAAA,eAAY;;kFACX,8OAAC,kIAAA,CAAA,cAAW;kFAAC;;;;;;kFACb,8OAAC,kIAAA,CAAA,oBAAiB;;4EAAC;4EACmB,SAAS,KAAK;4EAAC;;;;;;;;;;;;;0EAGvD,8OAAC,kKAAA,CAAA,0BAAuB;gEACtB,UAAU;oEACR,IAAI,SAAS,EAAE;oEACf,OAAO,SAAS,KAAK;oEACrB,QAAQ,SAAS,MAAM;oEACvB,aAAa,SAAS,WAAW;oEACjC,cAAc,SAAS,YAAY;oEACnC,WAAW,SAAS,SAAS;gEAC/B;gEACA,WAAW;oEACT,uBAAuB;oEACvB,oCAAoC;oEACpC;gEACF;gEACA,UAAU,IAAM,uBAAuB;;;;;;;;;;;;;;;;;;0DAK/C,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,WAAU;gDAAS,MAAK;;kEAChD,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW9D", "debugId": null}}]}