{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/profiles.ts"], "sourcesContent": ["import { supabase } from './supabase';\nimport { Database } from './database.types';\n\ntype Profile = Database['public']['Tables']['profiles']['Row'];\ntype ProfileInsert = Database['public']['Tables']['profiles']['Insert'];\ntype ProfileUpdate = Database['public']['Tables']['profiles']['Update'];\n\ntype Influencer = Database['public']['Tables']['influencers']['Row'];\ntype InfluencerInsert = Database['public']['Tables']['influencers']['Insert'];\ntype InfluencerUpdate = Database['public']['Tables']['influencers']['Update'];\n\ntype Business = Database['public']['Tables']['businesses']['Row'];\ntype BusinessInsert = Database['public']['Tables']['businesses']['Insert'];\ntype BusinessUpdate = Database['public']['Tables']['businesses']['Update'];\n\n// Profile functions\nexport const getProfile = async (userId: string) => {\n  const { data, error } = await supabase\n    .from('profiles')\n    .select('*')\n    .eq('id', userId)\n    .single();\n  \n  return { data, error };\n};\n\nexport const updateProfile = async (userId: string, updates: ProfileUpdate) => {\n  const { data, error } = await supabase\n    .from('profiles')\n    .update(updates)\n    .eq('id', userId)\n    .select()\n    .single();\n\n  return { data, error };\n};\n\nexport const createProfile = async (profileData: ProfileInsert) => {\n  const { data, error } = await supabase\n    .from('profiles')\n    .insert(profileData)\n    .select()\n    .single();\n\n  return { data, error };\n};\n\n// Get public influencer profile by username\nexport const getPublicInfluencerProfile = async (username: string) => {\n  // Use RPC function to get influencer data\n  const { data: influencerData, error: influencerError } = await supabase\n    .rpc('get_influencers_with_details', {\n      search_term: username,\n      min_followers: 0,\n      max_followers: 999999999,\n      min_price: 0,\n      max_price: 999999,\n      platform_filter: '',\n      category_filter: '',\n      location_filter: '',\n      limit_count: 10\n    });\n\n  if (influencerError || !influencerData || influencerData.length === 0) {\n    return { data: null, error: influencerError || { message: 'Influencer not found' } };\n  }\n\n  // Find the exact username match\n  const exactMatch = influencerData.find(item => item.username === username);\n  if (!exactMatch) {\n    return { data: null, error: { message: 'Influencer not found' } };\n  }\n\n  const data = exactMatch;\n\n  // Transform data to match expected structure\n  const transformedData = {\n    id: data.id,\n    username: data.username,\n    full_name: data.full_name,\n    avatar_url: data.avatar_url,\n    bio: data.bio,\n    location: data.location,\n    created_at: data.created_at,\n    gender: data.gender,\n    age: data.age,\n    is_verified: data.is_verified,\n    platforms: [\n      ...(data.instagram_followers > 0 ? [{\n        platform_id: 1,\n        platform_name: 'Instagram',\n        platform_icon: '📷',\n        handle: `@${data.username}`,\n        followers_count: data.instagram_followers,\n        is_verified: data.is_verified\n      }] : []),\n      ...(data.tiktok_followers > 0 ? [{\n        platform_id: 2,\n        platform_name: 'TikTok',\n        platform_icon: '🎵',\n        handle: `@${data.username}`,\n        followers_count: data.tiktok_followers,\n        is_verified: false\n      }] : []),\n      ...(data.youtube_subscribers > 0 ? [{\n        platform_id: 3,\n        platform_name: 'YouTube',\n        platform_icon: '📺',\n        handle: `@${data.username}`,\n        followers_count: data.youtube_subscribers,\n        is_verified: false\n      }] : [])\n    ],\n    categories: [], // TODO: Add categories when implemented\n    pricing: [\n      ...(data.price_per_post ? [{\n        platform_id: 1,\n        platform_name: 'Instagram',\n        platform_icon: '📷',\n        content_type_id: 1,\n        content_type_name: 'Post',\n        price: Number(data.price_per_post),\n        currency: 'KM'\n      }] : []),\n      ...(data.price_per_story ? [{\n        platform_id: 1,\n        platform_name: 'Instagram',\n        platform_icon: '📷',\n        content_type_id: 2,\n        content_type_name: 'Story',\n        price: Number(data.price_per_story),\n        currency: 'KM'\n      }] : []),\n      ...(data.price_per_reel ? [{\n        platform_id: 1,\n        platform_name: 'Instagram',\n        platform_icon: '📷',\n        content_type_id: 3,\n        content_type_name: 'Reel',\n        price: Number(data.price_per_reel),\n        currency: 'KM'\n      }] : [])\n    ],\n    portfolio_items: [], // TODO: Add portfolio items when implemented\n    total_followers: (data.instagram_followers || 0) +\n                    (data.tiktok_followers || 0) +\n                    (data.youtube_subscribers || 0),\n    min_price: Math.min(\n      ...[data.price_per_post, data.price_per_story, data.price_per_reel]\n        .filter(Boolean)\n        .map(Number)\n    ) || 0,\n    max_price: Math.max(\n      ...[data.price_per_post, data.price_per_story, data.price_per_reel]\n        .filter(Boolean)\n        .map(Number)\n    ) || 0\n  };\n\n  return { data: transformedData, error: null };\n};\n\nexport const upsertProfile = async (userId: string, updates: ProfileUpdate) => {\n  // First try to get existing profile\n  const { data: existingProfile } = await getProfile(userId);\n\n  if (existingProfile) {\n    // Profile exists, update it\n    return updateProfile(userId, updates);\n  } else {\n    // Profile doesn't exist, create it\n    const profileData: ProfileInsert = {\n      id: userId,\n      user_type: updates.user_type || 'influencer',\n      username: updates.username || null,\n      full_name: updates.full_name || null,\n      avatar_url: updates.avatar_url || null,\n      bio: updates.bio || null,\n      website_url: updates.website_url || null,\n      location: updates.location || null,\n    };\n    return createProfile(profileData);\n  }\n};\n\nexport const checkUsernameAvailable = async (username: string, excludeUserId?: string) => {\n  let query = supabase\n    .from('profiles')\n    .select('id')\n    .eq('username', username);\n  \n  if (excludeUserId) {\n    query = query.neq('id', excludeUserId);\n  }\n  \n  const { data, error } = await query;\n  \n  if (error) return { available: false, error };\n  return { available: data.length === 0, error: null };\n};\n\n// Influencer functions\nexport const getInfluencer = async (userId: string) => {\n  const { data, error } = await supabase\n    .from('influencers')\n    .select(`\n      *,\n      profiles (*)\n    `)\n    .eq('id', userId)\n    .single();\n  \n  return { data, error };\n};\n\nexport const createInfluencer = async (influencerData: InfluencerInsert) => {\n  const { data, error } = await supabase\n    .from('influencers')\n    .insert(influencerData)\n    .select()\n    .single();\n  \n  return { data, error };\n};\n\nexport const updateInfluencer = async (userId: string, updates: InfluencerUpdate) => {\n  const { data, error } = await supabase\n    .from('influencers')\n    .update(updates)\n    .eq('id', userId)\n    .select()\n    .single();\n\n  return { data, error };\n};\n\nexport const getInfluencers = async (filters?: {\n  niche?: string;\n  minFollowers?: number;\n  maxFollowers?: number;\n  location?: string;\n  limit?: number;\n  offset?: number;\n}) => {\n  let query = supabase\n    .from('influencers')\n    .select(`\n      *,\n      profiles (*)\n    `);\n\n  if (filters?.niche) {\n    query = query.ilike('niche', `%${filters.niche}%`);\n  }\n\n  if (filters?.minFollowers) {\n    query = query.gte('instagram_followers', filters.minFollowers);\n  }\n\n  if (filters?.maxFollowers) {\n    query = query.lte('instagram_followers', filters.maxFollowers);\n  }\n\n  if (filters?.location) {\n    query = query.eq('profiles.location', filters.location);\n  }\n\n  if (filters?.limit) {\n    query = query.limit(filters.limit);\n  }\n\n  if (filters?.offset) {\n    query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);\n  }\n\n  const { data, error } = await query;\n  return { data, error };\n};\n\n// Business functions\nexport const getBusiness = async (userId: string) => {\n  const { data, error } = await supabase\n    .from('businesses')\n    .select(`\n      *,\n      profiles (*)\n    `)\n    .eq('id', userId)\n    .single();\n  \n  return { data, error };\n};\n\nexport const createBusiness = async (businessData: BusinessInsert) => {\n  const { data, error } = await supabase\n    .from('businesses')\n    .insert(businessData)\n    .select()\n    .single();\n  \n  return { data, error };\n};\n\nexport const updateBusiness = async (userId: string, updates: BusinessUpdate) => {\n  const { data, error } = await supabase\n    .from('businesses')\n    .update(updates)\n    .eq('id', userId)\n    .select()\n    .single();\n\n  return { data, error };\n};\n\n// Category functions\nexport const getCategories = async () => {\n  const { data, error } = await supabase\n    .from('categories')\n    .select('*')\n    .order('name');\n\n  return { data, error };\n};\n\nexport const getInfluencerCategories = async (influencerId: string) => {\n  const { data, error } = await supabase\n    .from('influencer_categories')\n    .select(`\n      category_id,\n      is_primary,\n      categories (*)\n    `)\n    .eq('influencer_id', influencerId);\n\n  return { data, error };\n};\n\nexport const updateInfluencerCategories = async (influencerId: string, categoryIds: number[]) => {\n  // First, delete existing categories\n  await supabase\n    .from('influencer_categories')\n    .delete()\n    .eq('influencer_id', influencerId);\n\n  // Then insert new categories\n  if (categoryIds.length > 0) {\n    const categoryData = categoryIds.map((categoryId, index) => ({\n      influencer_id: influencerId,\n      category_id: categoryId,\n      is_primary: index === 0 // First category is primary\n    }));\n\n    const { data, error } = await supabase\n      .from('influencer_categories')\n      .insert(categoryData)\n      .select();\n\n    return { data, error };\n  }\n\n  return { data: [], error: null };\n};\n\nexport const getBusinessTargetCategories = async (businessId: string) => {\n  const { data, error } = await supabase\n    .from('business_target_categories')\n    .select(`\n      category_id,\n      categories (*)\n    `)\n    .eq('business_id', businessId);\n\n  return { data, error };\n};\n\nexport const updateBusinessTargetCategories = async (businessId: string, categoryIds: number[]) => {\n  // First, delete existing categories\n  await supabase\n    .from('business_target_categories')\n    .delete()\n    .eq('business_id', businessId);\n\n  // Then insert new categories\n  if (categoryIds.length > 0) {\n    const categoryData = categoryIds.map(categoryId => ({\n      business_id: businessId,\n      category_id: categoryId\n    }));\n\n    const { data, error } = await supabase\n      .from('business_target_categories')\n      .insert(categoryData)\n      .select();\n\n    return { data, error };\n  }\n\n  return { data: [], error: null };\n};\n\n// Combined profile functions\nexport const getFullProfile = async (userId: string) => {\n  const { data: profile, error: profileError } = await getProfile(userId);\n  \n  if (profileError || !profile) {\n    return { data: null, error: profileError };\n  }\n\n  if (profile.user_type === 'influencer') {\n    const { data: influencer, error: influencerError } = await getInfluencer(userId);\n    return { \n      data: influencer ? { ...profile, influencer } : profile, \n      error: influencerError \n    };\n  } else if (profile.user_type === 'business') {\n    const { data: business, error: businessError } = await getBusiness(userId);\n    return { \n      data: business ? { ...profile, business } : profile, \n      error: businessError \n    };\n  }\n\n  return { data: profile, error: null };\n};\n\n// Upload avatar function\nexport const uploadAvatar = async (userId: string, file: File) => {\n  const fileExt = file.name.split('.').pop();\n  const fileName = `${userId}-${Math.random()}.${fileExt}`;\n  const filePath = `avatars/${fileName}`;\n\n  const { error: uploadError } = await supabase.storage\n    .from('avatars')\n    .upload(filePath, file);\n\n  if (uploadError) {\n    return { data: null, error: uploadError };\n  }\n\n  const { data } = supabase.storage\n    .from('avatars')\n    .getPublicUrl(filePath);\n\n  // Update profile with new avatar URL\n  const { error: updateError } = await updateProfile(userId, {\n    avatar_url: data.publicUrl,\n  });\n\n  if (updateError) {\n    return { data: null, error: updateError };\n  }\n\n  return { data: data.publicUrl, error: null };\n};\n\n// Platform and Pricing functions\nexport const getInfluencerPlatforms = async (influencerId: string) => {\n  const { data, error } = await supabase\n    .from('influencer_platforms')\n    .select(`\n      *,\n      platforms (*)\n    `)\n    .eq('influencer_id', influencerId)\n    .eq('is_active', true);\n\n  return { data, error };\n};\n\nexport const getInfluencerPricing = async (influencerId: string) => {\n  const { data, error } = await supabase\n    .from('influencer_platform_pricing')\n    .select(`\n      *,\n      platforms (*),\n      content_types (*)\n    `)\n    .eq('influencer_id', influencerId)\n    .eq('is_available', true);\n\n  return { data, error };\n};\n\nexport const updateInfluencerPlatforms = async (influencerId: string, platforms: any[]) => {\n  // First, delete existing platforms\n  await supabase\n    .from('influencer_platforms')\n    .delete()\n    .eq('influencer_id', influencerId);\n\n  // Then insert new platforms\n  if (platforms.length > 0) {\n    const platformData = platforms.map(platform => ({\n      influencer_id: influencerId,\n      platform_id: platform.platform_id,\n      handle: platform.handle || null,\n      followers_count: platform.followers_count || 0,\n      is_verified: false,\n      is_active: true\n    }));\n\n    const { data, error } = await supabase\n      .from('influencer_platforms')\n      .insert(platformData)\n      .select();\n\n    return { data, error };\n  }\n\n  return { data: [], error: null };\n};\n\nexport const updateInfluencerPricing = async (influencerId: string, pricing: any[]) => {\n  // First, delete existing pricing\n  await supabase\n    .from('influencer_platform_pricing')\n    .delete()\n    .eq('influencer_id', influencerId);\n\n  // Then insert new pricing\n  if (pricing.length > 0) {\n    const pricingData = pricing.map(price => ({\n      influencer_id: influencerId,\n      platform_id: price.platform_id,\n      content_type_id: price.content_type_id,\n      price: price.price,\n      currency: 'KM',\n      is_available: price.is_available !== false\n    }));\n\n    const { data, error } = await supabase\n      .from('influencer_platform_pricing')\n      .insert(pricingData)\n      .select();\n\n    return { data, error };\n  }\n\n  return { data: [], error: null };\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAgBO,MAAM,aAAa,OAAO;IAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,gBAAgB,OAAO,QAAgB;IAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,gBAAgB,OAAO;IAClC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,aACP,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,MAAM,6BAA6B,OAAO;IAC/C,0CAA0C;IAC1C,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACpE,GAAG,CAAC,gCAAgC;QACnC,aAAa;QACb,eAAe;QACf,eAAe;QACf,WAAW;QACX,WAAW;QACX,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,aAAa;IACf;IAEF,IAAI,mBAAmB,CAAC,kBAAkB,eAAe,MAAM,KAAK,GAAG;QACrE,OAAO;YAAE,MAAM;YAAM,OAAO,mBAAmB;gBAAE,SAAS;YAAuB;QAAE;IACrF;IAEA,gCAAgC;IAChC,MAAM,aAAa,eAAe,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;IACjE,IAAI,CAAC,YAAY;QACf,OAAO;YAAE,MAAM;YAAM,OAAO;gBAAE,SAAS;YAAuB;QAAE;IAClE;IAEA,MAAM,OAAO;IAEb,6CAA6C;IAC7C,MAAM,kBAAkB;QACtB,IAAI,KAAK,EAAE;QACX,UAAU,KAAK,QAAQ;QACvB,WAAW,KAAK,SAAS;QACzB,YAAY,KAAK,UAAU;QAC3B,KAAK,KAAK,GAAG;QACb,UAAU,KAAK,QAAQ;QACvB,YAAY,KAAK,UAAU;QAC3B,QAAQ,KAAK,MAAM;QACnB,KAAK,KAAK,GAAG;QACb,aAAa,KAAK,WAAW;QAC7B,WAAW;eACL,KAAK,mBAAmB,GAAG,IAAI;gBAAC;oBAClC,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,QAAQ,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;oBAC3B,iBAAiB,KAAK,mBAAmB;oBACzC,aAAa,KAAK,WAAW;gBAC/B;aAAE,GAAG,EAAE;eACH,KAAK,gBAAgB,GAAG,IAAI;gBAAC;oBAC/B,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,QAAQ,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;oBAC3B,iBAAiB,KAAK,gBAAgB;oBACtC,aAAa;gBACf;aAAE,GAAG,EAAE;eACH,KAAK,mBAAmB,GAAG,IAAI;gBAAC;oBAClC,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,QAAQ,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;oBAC3B,iBAAiB,KAAK,mBAAmB;oBACzC,aAAa;gBACf;aAAE,GAAG,EAAE;SACR;QACD,YAAY,EAAE;QACd,SAAS;eACH,KAAK,cAAc,GAAG;gBAAC;oBACzB,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,OAAO,OAAO,KAAK,cAAc;oBACjC,UAAU;gBACZ;aAAE,GAAG,EAAE;eACH,KAAK,eAAe,GAAG;gBAAC;oBAC1B,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,OAAO,OAAO,KAAK,eAAe;oBAClC,UAAU;gBACZ;aAAE,GAAG,EAAE;eACH,KAAK,cAAc,GAAG;gBAAC;oBACzB,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,OAAO,OAAO,KAAK,cAAc;oBACjC,UAAU;gBACZ;aAAE,GAAG,EAAE;SACR;QACD,iBAAiB,EAAE;QACnB,iBAAiB,CAAC,KAAK,mBAAmB,IAAI,CAAC,IAC/B,CAAC,KAAK,gBAAgB,IAAI,CAAC,IAC3B,CAAC,KAAK,mBAAmB,IAAI,CAAC;QAC9C,WAAW,KAAK,GAAG,IACd;YAAC,KAAK,cAAc;YAAE,KAAK,eAAe;YAAE,KAAK,cAAc;SAAC,CAChE,MAAM,CAAC,SACP,GAAG,CAAC,YACJ;QACL,WAAW,KAAK,GAAG,IACd;YAAC,KAAK,cAAc;YAAE,KAAK,eAAe;YAAE,KAAK,cAAc;SAAC,CAChE,MAAM,CAAC,SACP,GAAG,CAAC,YACJ;IACP;IAEA,OAAO;QAAE,MAAM;QAAiB,OAAO;IAAK;AAC9C;AAEO,MAAM,gBAAgB,OAAO,QAAgB;IAClD,oCAAoC;IACpC,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,WAAW;IAEnD,IAAI,iBAAiB;QACnB,4BAA4B;QAC5B,OAAO,cAAc,QAAQ;IAC/B,OAAO;QACL,mCAAmC;QACnC,MAAM,cAA6B;YACjC,IAAI;YACJ,WAAW,QAAQ,SAAS,IAAI;YAChC,UAAU,QAAQ,QAAQ,IAAI;YAC9B,WAAW,QAAQ,SAAS,IAAI;YAChC,YAAY,QAAQ,UAAU,IAAI;YAClC,KAAK,QAAQ,GAAG,IAAI;YACpB,aAAa,QAAQ,WAAW,IAAI;YACpC,UAAU,QAAQ,QAAQ,IAAI;QAChC;QACA,OAAO,cAAc;IACvB;AACF;AAEO,MAAM,yBAAyB,OAAO,UAAkB;IAC7D,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,YACL,MAAM,CAAC,MACP,EAAE,CAAC,YAAY;IAElB,IAAI,eAAe;QACjB,QAAQ,MAAM,GAAG,CAAC,MAAM;IAC1B;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAE9B,IAAI,OAAO,OAAO;QAAE,WAAW;QAAO;IAAM;IAC5C,OAAO;QAAE,WAAW,KAAK,MAAM,KAAK;QAAG,OAAO;IAAK;AACrD;AAGO,MAAM,gBAAgB,OAAO;IAClC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,CAAC;;;IAGT,CAAC,EACA,EAAE,CAAC,MAAM,QACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,mBAAmB,OAAO;IACrC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,gBACP,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,mBAAmB,OAAO,QAAgB;IACrD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,iBAAiB,OAAO;IAQnC,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,eACL,MAAM,CAAC,CAAC;;;IAGT,CAAC;IAEH,IAAI,SAAS,OAAO;QAClB,QAAQ,MAAM,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAC,CAAC,CAAC;IACnD;IAEA,IAAI,SAAS,cAAc;QACzB,QAAQ,MAAM,GAAG,CAAC,uBAAuB,QAAQ,YAAY;IAC/D;IAEA,IAAI,SAAS,cAAc;QACzB,QAAQ,MAAM,GAAG,CAAC,uBAAuB,QAAQ,YAAY;IAC/D;IAEA,IAAI,SAAS,UAAU;QACrB,QAAQ,MAAM,EAAE,CAAC,qBAAqB,QAAQ,QAAQ;IACxD;IAEA,IAAI,SAAS,OAAO;QAClB,QAAQ,MAAM,KAAK,CAAC,QAAQ,KAAK;IACnC;IAEA,IAAI,SAAS,QAAQ;QACnB,QAAQ,MAAM,KAAK,CAAC,QAAQ,MAAM,EAAE,QAAQ,MAAM,GAAG,CAAC,QAAQ,KAAK,IAAI,EAAE,IAAI;IAC/E;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAC9B,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,MAAM,cAAc,OAAO;IAChC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,CAAC;;;IAGT,CAAC,EACA,EAAE,CAAC,MAAM,QACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,iBAAiB,OAAO;IACnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,cACP,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,iBAAiB,OAAO,QAAgB;IACnD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,MAAM,gBAAgB;IAC3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,KACP,KAAK,CAAC;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yBACL,MAAM,CAAC,CAAC;;;;IAIT,CAAC,EACA,EAAE,CAAC,iBAAiB;IAEvB,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,6BAA6B,OAAO,cAAsB;IACrE,oCAAoC;IACpC,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,yBACL,MAAM,GACN,EAAE,CAAC,iBAAiB;IAEvB,6BAA6B;IAC7B,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,MAAM,eAAe,YAAY,GAAG,CAAC,CAAC,YAAY,QAAU,CAAC;gBAC3D,eAAe;gBACf,aAAa;gBACb,YAAY,UAAU,EAAE,4BAA4B;YACtD,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yBACL,MAAM,CAAC,cACP,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,OAAO;QAAE,MAAM,EAAE;QAAE,OAAO;IAAK;AACjC;AAEO,MAAM,8BAA8B,OAAO;IAChD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,8BACL,MAAM,CAAC,CAAC;;;IAGT,CAAC,EACA,EAAE,CAAC,eAAe;IAErB,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,iCAAiC,OAAO,YAAoB;IACvE,oCAAoC;IACpC,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,8BACL,MAAM,GACN,EAAE,CAAC,eAAe;IAErB,6BAA6B;IAC7B,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,MAAM,eAAe,YAAY,GAAG,CAAC,CAAA,aAAc,CAAC;gBAClD,aAAa;gBACb,aAAa;YACf,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,8BACL,MAAM,CAAC,cACP,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,OAAO;QAAE,MAAM,EAAE;QAAE,OAAO;IAAK;AACjC;AAGO,MAAM,iBAAiB,OAAO;IACnC,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,WAAW;IAEhE,IAAI,gBAAgB,CAAC,SAAS;QAC5B,OAAO;YAAE,MAAM;YAAM,OAAO;QAAa;IAC3C;IAEA,IAAI,QAAQ,SAAS,KAAK,cAAc;QACtC,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,cAAc;QACzE,OAAO;YACL,MAAM,aAAa;gBAAE,GAAG,OAAO;gBAAE;YAAW,IAAI;YAChD,OAAO;QACT;IACF,OAAO,IAAI,QAAQ,SAAS,KAAK,YAAY;QAC3C,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,YAAY;QACnE,OAAO;YACL,MAAM,WAAW;gBAAE,GAAG,OAAO;gBAAE;YAAS,IAAI;YAC5C,OAAO;QACT;IACF;IAEA,OAAO;QAAE,MAAM;QAAS,OAAO;IAAK;AACtC;AAGO,MAAM,eAAe,OAAO,QAAgB;IACjD,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;IACxC,MAAM,WAAW,GAAG,OAAO,CAAC,EAAE,KAAK,MAAM,GAAG,CAAC,EAAE,SAAS;IACxD,MAAM,WAAW,CAAC,QAAQ,EAAE,UAAU;IAEtC,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAClD,IAAI,CAAC,WACL,MAAM,CAAC,UAAU;IAEpB,IAAI,aAAa;QACf,OAAO;YAAE,MAAM;YAAM,OAAO;QAAY;IAC1C;IAEA,MAAM,EAAE,IAAI,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAC9B,IAAI,CAAC,WACL,YAAY,CAAC;IAEhB,qCAAqC;IACrC,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,cAAc,QAAQ;QACzD,YAAY,KAAK,SAAS;IAC5B;IAEA,IAAI,aAAa;QACf,OAAO;YAAE,MAAM;YAAM,OAAO;QAAY;IAC1C;IAEA,OAAO;QAAE,MAAM,KAAK,SAAS;QAAE,OAAO;IAAK;AAC7C;AAGO,MAAM,yBAAyB,OAAO;IAC3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wBACL,MAAM,CAAC,CAAC;;;IAGT,CAAC,EACA,EAAE,CAAC,iBAAiB,cACpB,EAAE,CAAC,aAAa;IAEnB,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,uBAAuB,OAAO;IACzC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,+BACL,MAAM,CAAC,CAAC;;;;IAIT,CAAC,EACA,EAAE,CAAC,iBAAiB,cACpB,EAAE,CAAC,gBAAgB;IAEtB,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,4BAA4B,OAAO,cAAsB;IACpE,mCAAmC;IACnC,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,wBACL,MAAM,GACN,EAAE,CAAC,iBAAiB;IAEvB,4BAA4B;IAC5B,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,MAAM,eAAe,UAAU,GAAG,CAAC,CAAA,WAAY,CAAC;gBAC9C,eAAe;gBACf,aAAa,SAAS,WAAW;gBACjC,QAAQ,SAAS,MAAM,IAAI;gBAC3B,iBAAiB,SAAS,eAAe,IAAI;gBAC7C,aAAa;gBACb,WAAW;YACb,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wBACL,MAAM,CAAC,cACP,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,OAAO;QAAE,MAAM,EAAE;QAAE,OAAO;IAAK;AACjC;AAEO,MAAM,0BAA0B,OAAO,cAAsB;IAClE,iCAAiC;IACjC,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,+BACL,MAAM,GACN,EAAE,CAAC,iBAAiB;IAEvB,0BAA0B;IAC1B,IAAI,QAAQ,MAAM,GAAG,GAAG;QACtB,MAAM,cAAc,QAAQ,GAAG,CAAC,CAAA,QAAS,CAAC;gBACxC,eAAe;gBACf,aAAa,MAAM,WAAW;gBAC9B,iBAAiB,MAAM,eAAe;gBACtC,OAAO,MAAM,KAAK;gBAClB,UAAU;gBACV,cAAc,MAAM,YAAY,KAAK;YACvC,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,+BACL,MAAM,CAAC,aACP,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,OAAO;QAAE,MAAM,EAAE;QAAE,OAAO;IAAK;AACjC", "debugId": null}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 536, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 591, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 761, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/navigation/DesktopNavigation.tsx"], "sourcesContent": ["'use client';\n\nimport { usePathname, useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Button } from '@/components/ui/button';\nimport { cn } from '@/lib/utils';\nimport {\n  LayoutDashboard,\n  User,\n  Settings,\n  Building2,\n  Users,\n  FileText,\n  MessageCircle,\n  DollarSign,\n  LogOut,\n  Menu,\n  Send,\n  Inbox\n} from 'lucide-react';\nimport { useState } from 'react';\nimport {\n  She<PERSON>,\n  SheetContent,\n  SheetHeader,\n  SheetTitle,\n  SheetTrigger,\n} from '@/components/ui/sheet';\n\ninterface SidebarItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  description?: string;\n}\n\ninterface DesktopNavigationProps {\n  userType: 'influencer' | 'business';\n  className?: string;\n}\n\nexport function DesktopNavigation({ userType, className }: DesktopNavigationProps) {\n  const pathname = usePathname();\n  const { signOut } = useAuth();\n  const router = useRouter();\n  const [isOpen, setIsOpen] = useState(false);\n\n  // Navigacija za influencer korisnike\n  const influencerNavigation: SidebarItem[] = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard/influencer',\n      icon: LayoutDashboard,\n      description: 'Pregled aktivnosti i statistika'\n    },\n    {\n      name: 'Moj račun',\n      href: '/dashboard/influencer/account',\n      icon: User,\n      description: 'Lični podaci i sigurnost'\n    },\n    {\n      name: 'Postavke profila',\n      href: '/dashboard/influencer/profile',\n      icon: Settings,\n      description: 'Javni profil i cijene'\n    },\n    {\n      name: 'Kampanje',\n      href: '/marketplace/campaigns',\n      icon: FileText,\n      description: 'Dostupne kampanje'\n    },\n    {\n      name: 'Ponude i aplikacije',\n      href: '/dashboard/influencer/offers',\n      icon: Inbox,\n      description: 'Direktne ponude i aplikacije'\n    },\n    {\n      name: 'Poruke',\n      href: '/dashboard/chat',\n      icon: MessageCircle,\n      description: 'Komunikacija sa brendovima'\n    },\n    {\n      name: 'Zarada',\n      href: '/dashboard/influencer/earnings',\n      icon: DollarSign,\n      description: 'Pregled zarade i isplate'\n    }\n  ];\n\n  // Navigacija za biznis korisnike\n  const businessNavigation: SidebarItem[] = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard/biznis',\n      icon: LayoutDashboard,\n      description: 'Pregled kampanja i statistika'\n    },\n    {\n      name: 'Moj račun',\n      href: '/dashboard/biznis/account',\n      icon: Building2,\n      description: 'Podaci o firmi i sigurnost'\n    },\n    {\n      name: 'Postavke profila',\n      href: '/dashboard/biznis/profile',\n      icon: Settings,\n      description: 'Javni profil firme'\n    },\n    {\n      name: 'Moje kampanje',\n      href: '/dashboard/campaigns',\n      icon: FileText,\n      description: 'Upravljanje kampanjama'\n    },\n    {\n      name: 'Aplikacije',\n      href: '/dashboard/biznis/applications',\n      icon: FileText,\n      description: 'Aplikacije na kampanje'\n    },\n    {\n      name: 'Moje ponude',\n      href: '/dashboard/biznis/offers',\n      icon: Send,\n      description: 'Direktne ponude influencerima'\n    },\n    {\n      name: 'Influenceri',\n      href: '/marketplace/influencers',\n      icon: Users,\n      description: 'Pronađi influencere'\n    },\n    {\n      name: 'Poruke',\n      href: '/dashboard/chat',\n      icon: MessageCircle,\n      description: 'Komunikacija sa influencerima'\n    }\n  ];\n\n  const navigation = userType === 'influencer' ? influencerNavigation : businessNavigation;\n\n  const handleSignOut = async () => {\n    try {\n      await signOut();\n      router.push('/');\n      setIsOpen(false);\n    } catch (error) {\n      console.error('Error signing out:', error);\n    }\n  };\n\n  return (\n    <div className={cn(\"hidden md:block\", className)}>\n      {/* Desktop Navigation Trigger */}\n      <Sheet open={isOpen} onOpenChange={setIsOpen}>\n        <SheetTrigger asChild>\n          <Button\n            variant=\"outline\"\n            size=\"icon\"\n            className=\"fixed top-4 left-4 z-40 bg-card border-border shadow-lg hover:bg-accent\"\n          >\n            <Menu className=\"h-4 w-4\" />\n            <span className=\"sr-only\">Otvori navigaciju</span>\n          </Button>\n        </SheetTrigger>\n        \n        <SheetContent side=\"left\" className=\"w-80 p-0\">\n          <div className=\"flex flex-col h-full\">\n            {/* Header */}\n            <SheetHeader className=\"p-6 border-b border-border\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-primary rounded-lg flex items-center justify-center\">\n                  <span className=\"text-primary-foreground font-bold text-lg\">🔗</span>\n                </div>\n                <SheetTitle className=\"text-lg font-bold text-foreground\">\n                  InfluConnect\n                </SheetTitle>\n              </div>\n            </SheetHeader>\n\n            {/* Navigation */}\n            <nav className=\"flex-1 px-4 py-6 space-y-2 overflow-y-auto\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href || pathname.startsWith(item.href + '/');\n                \n                return (\n                  <Link \n                    key={item.name} \n                    href={item.href}\n                    onClick={() => setIsOpen(false)}\n                  >\n                    <div className={cn(\n                      \"group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-colors\",\n                      isActive\n                        ? \"bg-primary text-primary-foreground\"\n                        : \"text-muted-foreground hover:bg-accent hover:text-accent-foreground\"\n                    )}>\n                      <item.icon className=\"flex-shrink-0 h-5 w-5 mr-3\" />\n                      <div className=\"flex-1\">\n                        <div className=\"font-medium\">{item.name}</div>\n                        {item.description && (\n                          <div className=\"text-xs opacity-75 mt-0.5\">\n                            {item.description}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </Link>\n                );\n              })}\n            </nav>\n\n            {/* Footer */}\n            <div className=\"p-4 border-t border-border\">\n              <Button\n                variant=\"ghost\"\n                onClick={handleSignOut}\n                className=\"w-full justify-start text-destructive hover:text-destructive hover:bg-destructive/10\"\n              >\n                <LogOut className=\"h-4 w-4 mr-2\" />\n                Odjava\n              </Button>\n            </div>\n          </div>\n        </SheetContent>\n      </Sheet>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AAtBA;;;;;;;;;;AA0CO,SAAS,kBAAkB,EAAE,QAAQ,EAAE,SAAS,EAA0B;IAC/E,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qCAAqC;IACrC,MAAM,uBAAsC;QAC1C;YACE,MAAM;YACN,MAAM;YACN,MAAM,4NAAA,CAAA,kBAAe;YACrB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,8MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;YACX,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,wNAAA,CAAA,gBAAa;YACnB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kNAAA,CAAA,aAAU;YAChB,aAAa;QACf;KACD;IAED,iCAAiC;IACjC,MAAM,qBAAoC;QACxC;YACE,MAAM;YACN,MAAM;YACN,MAAM,4NAAA,CAAA,kBAAe;YACrB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,gNAAA,CAAA,YAAS;YACf,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,8MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,8MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;YACX,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,wNAAA,CAAA,gBAAa;YACnB,aAAa;QACf;KACD;IAED,MAAM,aAAa,aAAa,eAAe,uBAAuB;IAEtE,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;YACZ,UAAU;QACZ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;kBAEpC,cAAA,8OAAC,iIAAA,CAAA,QAAK;YAAC,MAAM;YAAQ,cAAc;;8BACjC,8OAAC,iIAAA,CAAA,eAAY;oBAAC,OAAO;8BACnB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;8BAI9B,8OAAC,iIAAA,CAAA,eAAY;oBAAC,MAAK;oBAAO,WAAU;8BAClC,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,iIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;sDAE9D,8OAAC,iIAAA,CAAA,aAAU;4CAAC,WAAU;sDAAoC;;;;;;;;;;;;;;;;;0CAO9D,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,WAAW,aAAa,KAAK,IAAI,IAAI,SAAS,UAAU,CAAC,KAAK,IAAI,GAAG;oCAE3E,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,SAAS,IAAM,UAAU;kDAEzB,cAAA,8OAAC;4CAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,sFACA,WACI,uCACA;;8DAEJ,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;8DACrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAe,KAAK,IAAI;;;;;;wDACtC,KAAK,WAAW,kBACf,8OAAC;4DAAI,WAAU;sEACZ,KAAK,WAAW;;;;;;;;;;;;;;;;;;uCAfpB,KAAK,IAAI;;;;;gCAsBpB;;;;;;0CAIF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD", "debugId": null}}, {"offset": {"line": 1101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/navigation/MobileBottomNavigation.tsx"], "sourcesContent": ["'use client';\n\nimport { usePathname } from 'next/navigation';\nimport Link from 'next/link';\nimport { cn } from '@/lib/utils';\nimport {\n  LayoutDashboard,\n  FileText,\n  Inbox,\n  MessageCircle,\n  Menu,\n  User,\n  Settings,\n  DollarSign,\n  LogOut\n} from 'lucide-react';\nimport { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport {\n  Sheet,\n  <PERSON>et<PERSON>ontent,\n  SheetHeader,\n  SheetTitle,\n  SheetTrigger,\n} from '@/components/ui/sheet';\nimport { Button } from '@/components/ui/button';\n\ninterface MobileBottomNavigationProps {\n  userType: 'influencer' | 'business';\n}\n\ninterface NavItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  label: string;\n}\n\ninterface MenuNavItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  description?: string;\n}\n\nexport function MobileBottomNavigation({ userType }: MobileBottomNavigationProps) {\n  const pathname = usePathname();\n  const { signOut } = useAuth();\n  const router = useRouter();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  // Glavne 4 ikonice za influencere\n  const influencerMainNav: NavItem[] = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard/influencer',\n      icon: LayoutDashboard,\n      label: 'Home'\n    },\n    {\n      name: 'Kampanje',\n      href: '/marketplace/campaigns',\n      icon: FileText,\n      label: 'Prilike'\n    },\n    {\n      name: 'Ponude',\n      href: '/dashboard/influencer/offers',\n      icon: Inbox,\n      label: 'Ponude'\n    },\n    {\n      name: 'Poruke',\n      href: '/dashboard/chat',\n      icon: MessageCircle,\n      label: 'Poruke'\n    }\n  ];\n\n  // Glavne 4 ikonice za biznis korisnike\n  const businessMainNav: NavItem[] = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard/biznis',\n      icon: LayoutDashboard,\n      label: 'Home'\n    },\n    {\n      name: 'Kampanje',\n      href: '/dashboard/campaigns',\n      icon: FileText,\n      label: 'Kampanje'\n    },\n    {\n      name: 'Aplikacije',\n      href: '/dashboard/biznis/applications',\n      icon: Inbox,\n      label: 'Aplikacije'\n    },\n    {\n      name: 'Poruke',\n      href: '/dashboard/chat',\n      icon: MessageCircle,\n      label: 'Poruke'\n    }\n  ];\n\n  // Ostali elementi za hamburger meni - influenceri\n  const influencerMenuNav: MenuNavItem[] = [\n    {\n      name: 'Moj račun',\n      href: '/dashboard/influencer/account',\n      icon: User,\n      description: 'Lični podaci i sigurnost'\n    },\n    {\n      name: 'Postavke profila',\n      href: '/dashboard/influencer/profile',\n      icon: Settings,\n      description: 'Javni profil i cijene'\n    },\n    {\n      name: 'Zarada',\n      href: '/dashboard/influencer/earnings',\n      icon: DollarSign,\n      description: 'Pregled zarade i isplate'\n    }\n  ];\n\n  // Ostali elementi za hamburger meni - biznis\n  const businessMenuNav: MenuNavItem[] = [\n    {\n      name: 'Moj račun',\n      href: '/dashboard/biznis/account',\n      icon: User,\n      description: 'Podaci o firmi i sigurnost'\n    },\n    {\n      name: 'Postavke profila',\n      href: '/dashboard/biznis/profile',\n      icon: Settings,\n      description: 'Javni profil firme'\n    },\n    {\n      name: 'Influenceri',\n      href: '/marketplace/influencers',\n      icon: User,\n      description: 'Pronađi influencere'\n    }\n  ];\n\n  const mainNavigation = userType === 'influencer' ? influencerMainNav : businessMainNav;\n  const menuNavigation = userType === 'influencer' ? influencerMenuNav : businessMenuNav;\n\n  const handleSignOut = async () => {\n    try {\n      await signOut();\n      router.push('/');\n      setIsMenuOpen(false);\n    } catch (error) {\n      console.error('Error signing out:', error);\n    }\n  };\n\n  const isActive = (href: string) => {\n    return pathname === href || pathname.startsWith(href + '/');\n  };\n\n  return (\n    <>\n      {/* Mobile Bottom Navigation */}\n      <div className=\"fixed bottom-0 left-0 right-0 z-50 bg-card border-t border-border md:hidden\">\n        <div className=\"flex items-center justify-around py-2\">\n          {/* Glavne 4 ikonice */}\n          {mainNavigation.map((item) => (\n            <Link key={item.name} href={item.href}>\n              <div className={cn(\n                \"flex flex-col items-center justify-center p-2 min-w-[60px] rounded-lg transition-colors\",\n                isActive(item.href)\n                  ? \"text-primary bg-primary/10\"\n                  : \"text-muted-foreground hover:text-foreground hover:bg-accent\"\n              )}>\n                <item.icon className=\"h-5 w-5 mb-1\" />\n                <span className=\"text-xs font-medium\">{item.label}</span>\n              </div>\n            </Link>\n          ))}\n\n          {/* Hamburger Menu */}\n          <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>\n            <SheetTrigger asChild>\n              <div className=\"flex flex-col items-center justify-center p-2 min-w-[60px] rounded-lg transition-colors text-muted-foreground hover:text-foreground hover:bg-accent cursor-pointer\">\n                <Menu className=\"h-5 w-5 mb-1\" />\n                <span className=\"text-xs font-medium\">Više</span>\n              </div>\n            </SheetTrigger>\n            <SheetContent side=\"bottom\" className=\"h-auto max-h-[80vh]\">\n              <SheetHeader>\n                <SheetTitle>Meni</SheetTitle>\n              </SheetHeader>\n              \n              <div className=\"grid gap-4 py-4\">\n                {menuNavigation.map((item) => (\n                  <Link \n                    key={item.name} \n                    href={item.href}\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    <div className={cn(\n                      \"flex items-center space-x-3 p-3 rounded-lg transition-colors\",\n                      isActive(item.href)\n                        ? \"bg-primary text-primary-foreground\"\n                        : \"hover:bg-accent\"\n                    )}>\n                      <item.icon className=\"h-5 w-5\" />\n                      <div className=\"flex-1\">\n                        <div className=\"font-medium\">{item.name}</div>\n                        {item.description && (\n                          <div className=\"text-sm opacity-75 mt-0.5\">\n                            {item.description}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </Link>\n                ))}\n                \n                {/* Odjava */}\n                <Button\n                  variant=\"ghost\"\n                  onClick={handleSignOut}\n                  className=\"flex items-center justify-start space-x-3 p-3 w-full text-destructive hover:text-destructive hover:bg-destructive/10\"\n                >\n                  <LogOut className=\"h-5 w-5\" />\n                  <span className=\"font-medium\">Odjava</span>\n                </Button>\n              </div>\n            </SheetContent>\n          </Sheet>\n        </div>\n      </div>\n\n      {/* Spacer za bottom navigation */}\n      <div className=\"h-16 md:hidden\" />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AAEA;AAOA;AA1BA;;;;;;;;;;;AA8CO,SAAS,uBAAuB,EAAE,QAAQ,EAA+B;IAC9E,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,kCAAkC;IAClC,MAAM,oBAA+B;QACnC;YACE,MAAM;YACN,MAAM;YACN,MAAM,4NAAA,CAAA,kBAAe;YACrB,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,8MAAA,CAAA,WAAQ;YACd,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;QACT;KACD;IAED,uCAAuC;IACvC,MAAM,kBAA6B;QACjC;YACE,MAAM;YACN,MAAM;YACN,MAAM,4NAAA,CAAA,kBAAe;YACrB,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,8MAAA,CAAA,WAAQ;YACd,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;QACT;KACD;IAED,kDAAkD;IAClD,MAAM,oBAAmC;QACvC;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kNAAA,CAAA,aAAU;YAChB,aAAa;QACf;KACD;IAED,6CAA6C;IAC7C,MAAM,kBAAiC;QACrC;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,aAAa;QACf;KACD;IAED,MAAM,iBAAiB,aAAa,eAAe,oBAAoB;IACvE,MAAM,iBAAiB,aAAa,eAAe,oBAAoB;IAEvE,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;YACZ,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,OAAO,aAAa,QAAQ,SAAS,UAAU,CAAC,OAAO;IACzD;IAEA,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBAEZ,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,4JAAA,CAAA,UAAI;gCAAiB,MAAM,KAAK,IAAI;0CACnC,cAAA,8OAAC;oCAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,2FACA,SAAS,KAAK,IAAI,IACd,+BACA;;sDAEJ,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,8OAAC;4CAAK,WAAU;sDAAuB,KAAK,KAAK;;;;;;;;;;;;+BAR1C,KAAK,IAAI;;;;;sCActB,8OAAC,iIAAA,CAAA,QAAK;4BAAC,MAAM;4BAAY,cAAc;;8CACrC,8OAAC,iIAAA,CAAA,eAAY;oCAAC,OAAO;8CACnB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;;;;;;8CAG1C,8OAAC,iIAAA,CAAA,eAAY;oCAAC,MAAK;oCAAS,WAAU;;sDACpC,8OAAC,iIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC,iIAAA,CAAA,aAAU;0DAAC;;;;;;;;;;;sDAGd,8OAAC;4CAAI,WAAU;;gDACZ,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,4JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,SAAS,IAAM,cAAc;kEAE7B,cAAA,8OAAC;4DAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,gEACA,SAAS,KAAK,IAAI,IACd,uCACA;;8EAEJ,8OAAC,KAAK,IAAI;oEAAC,WAAU;;;;;;8EACrB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAAe,KAAK,IAAI;;;;;;wEACtC,KAAK,WAAW,kBACf,8OAAC;4EAAI,WAAU;sFACZ,KAAK,WAAW;;;;;;;;;;;;;;;;;;uDAfpB,KAAK,IAAI;;;;;8DAwBlB,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS;oDACT,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS1C,8OAAC;gBAAI,WAAU;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 1458, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/navigation/ResponsiveNavigation.tsx"], "sourcesContent": ["'use client';\n\nimport { DesktopNavigation } from './DesktopNavigation';\nimport { MobileBottomNavigation } from './MobileBottomNavigation';\n\ninterface ResponsiveNavigationProps {\n  userType: 'influencer' | 'business';\n}\n\nexport function ResponsiveNavigation({ userType }: ResponsiveNavigationProps) {\n  return (\n    <>\n      {/* Desktop Navigation - prikazuje se samo na desktop uređajima */}\n      <DesktopNavigation userType={userType} />\n      \n      {/* Mobile Bottom Navigation - prikazuje se samo na mobile uređajima */}\n      <MobileBottomNavigation userType={userType} />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASO,SAAS,qBAAqB,EAAE,QAAQ,EAA6B;IAC1E,qBACE;;0BAEE,8OAAC,qJAAA,CAAA,oBAAiB;gBAAC,UAAU;;;;;;0BAG7B,8OAAC,0JAAA,CAAA,yBAAsB;gBAAC,UAAU;;;;;;;;AAGxC", "debugId": null}}, {"offset": {"line": 1492, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1536, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction ScrollArea({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\n  return (\n    <ScrollAreaPrimitive.Root\n      data-slot=\"scroll-area\"\n      className={cn(\"relative\", className)}\n      {...props}\n    >\n      <ScrollAreaPrimitive.Viewport\n        data-slot=\"scroll-area-viewport\"\n        className=\"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1\"\n      >\n        {children}\n      </ScrollAreaPrimitive.Viewport>\n      <ScrollBar />\n      <ScrollAreaPrimitive.Corner />\n    </ScrollAreaPrimitive.Root>\n  )\n}\n\nfunction ScrollBar({\n  className,\n  orientation = \"vertical\",\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\n  return (\n    <ScrollAreaPrimitive.ScrollAreaScrollbar\n      data-slot=\"scroll-area-scrollbar\"\n      orientation={orientation}\n      className={cn(\n        \"flex touch-none p-px transition-colors select-none\",\n        orientation === \"vertical\" &&\n          \"h-full w-2.5 border-l border-l-transparent\",\n        orientation === \"horizontal\" &&\n          \"h-2.5 flex-col border-t border-t-transparent\",\n        className\n      )}\n      {...props}\n    >\n      <ScrollAreaPrimitive.ScrollAreaThumb\n        data-slot=\"scroll-area-thumb\"\n        className=\"bg-border relative flex-1 rounded-full\"\n      />\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>\n  )\n}\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAC3B,aAAU;gBACV,WAAU;0BAET;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAGjC;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,UAAU,EACxB,GAAG,OACkE;IACrE,qBACE,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,aAAU;QACV,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,gBAAgB,cACd,8CACF,gBAAgB,gBACd,gDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAClC,aAAU;YACV,WAAU;;;;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 1604, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1864, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/notifications.ts"], "sourcesContent": ["import { supabase } from './supabase';\n\nexport interface Notification {\n  id: string;\n  user_id: string;\n  type: string;\n  title: string;\n  message: string;\n  data: Record<string, any>;\n  read: boolean;\n  created_at: string;\n  updated_at: string;\n}\n\nexport type NotificationType = \n  | 'offer_received'\n  | 'offer_accepted'\n  | 'offer_rejected'\n  | 'campaign_application'\n  | 'campaign_accepted'\n  | 'campaign_rejected'\n  | 'message_received'\n  | 'payment_received';\n\n// Create a new notification\nexport async function createNotification(\n  userId: string,\n  type: NotificationType,\n  title: string,\n  message: string,\n  data: Record<string, any> = {}\n) {\n  const { data: notification, error } = await supabase.rpc('create_notification', {\n    p_user_id: userId,\n    p_type: type,\n    p_title: title,\n    p_message: message,\n    p_data: data\n  });\n\n  if (error) {\n    console.error('Error creating notification:', error);\n    return { data: null, error };\n  }\n\n  return { data: notification, error: null };\n}\n\n// Get user notifications\nexport async function getUserNotifications(userId?: string, limit = 50) {\n  let query = supabase\n    .from('notifications')\n    .select('*')\n    .order('created_at', { ascending: false })\n    .limit(limit);\n\n  if (userId) {\n    query = query.eq('user_id', userId);\n  }\n\n  const { data, error } = await query;\n\n  if (error) {\n    console.error('Error fetching notifications:', error);\n    return { data: null, error };\n  }\n\n  return { data, error: null };\n}\n\n// Mark notification as read\nexport async function markNotificationAsRead(notificationId: string) {\n  const { data, error } = await supabase\n    .from('notifications')\n    .update({ read: true })\n    .eq('id', notificationId)\n    .select()\n    .single();\n\n  if (error) {\n    console.error('Error marking notification as read:', error);\n    return { data: null, error };\n  }\n\n  return { data, error: null };\n}\n\n// Mark all notifications as read for user\nexport async function markAllNotificationsAsRead(userId: string) {\n  const { data, error } = await supabase\n    .from('notifications')\n    .update({ read: true })\n    .eq('user_id', userId)\n    .eq('read', false);\n\n  if (error) {\n    console.error('Error marking all notifications as read:', error);\n    return { data: null, error };\n  }\n\n  return { data, error: null };\n}\n\n// Get unread notification count\nexport async function getUnreadNotificationCount(userId: string) {\n  const { count, error } = await supabase\n    .from('notifications')\n    .select('*', { count: 'exact', head: true })\n    .eq('user_id', userId)\n    .eq('read', false);\n\n  if (error) {\n    console.error('Error getting unread count:', error);\n    return { count: 0, error };\n  }\n\n  return { count: count || 0, error: null };\n}\n\n// Delete notification\nexport async function deleteNotification(notificationId: string) {\n  const { error } = await supabase\n    .from('notifications')\n    .delete()\n    .eq('id', notificationId);\n\n  if (error) {\n    console.error('Error deleting notification:', error);\n    return { error };\n  }\n\n  return { error: null };\n}\n\n// Helper functions for specific notification types\n\nexport async function notifyOfferReceived(\n  influencerId: string,\n  businessName: string,\n  offerTitle: string,\n  offerId: string\n) {\n  return createNotification(\n    influencerId,\n    'offer_received',\n    'Nova direktna ponuda',\n    `${businessName} vam je poslao ponudu: \"${offerTitle}\"`,\n    { offer_id: offerId, business_name: businessName }\n  );\n}\n\nexport async function notifyOfferAccepted(\n  businessId: string,\n  influencerName: string,\n  offerTitle: string,\n  offerId: string\n) {\n  return createNotification(\n    businessId,\n    'offer_accepted',\n    'Ponuda prihvaćena',\n    `${influencerName} je prihvatio vašu ponudu: \"${offerTitle}\"`,\n    { offer_id: offerId, influencer_name: influencerName }\n  );\n}\n\nexport async function notifyOfferRejected(\n  businessId: string,\n  influencerName: string,\n  offerTitle: string,\n  offerId: string\n) {\n  return createNotification(\n    businessId,\n    'offer_rejected',\n    'Ponuda odbijena',\n    `${influencerName} je odbio vašu ponudu: \"${offerTitle}\"`,\n    { offer_id: offerId, influencer_name: influencerName }\n  );\n}\n\nexport async function notifyCampaignApplication(\n  businessId: string,\n  influencerName: string,\n  campaignTitle: string,\n  applicationId: string\n) {\n  return createNotification(\n    businessId,\n    'campaign_application',\n    'Nova aplikacija na kampanju',\n    `${influencerName} se prijavio na kampanju: \"${campaignTitle}\"`,\n    { application_id: applicationId, influencer_name: influencerName }\n  );\n}\n\nexport async function notifyCampaignAccepted(\n  influencerId: string,\n  businessName: string,\n  campaignTitle: string,\n  applicationId: string\n) {\n  return createNotification(\n    influencerId,\n    'campaign_accepted',\n    'Aplikacija prihvaćena',\n    `${businessName} je prihvatio vašu aplikaciju za kampanju: \"${campaignTitle}\"`,\n    { application_id: applicationId, business_name: businessName }\n  );\n}\n\nexport async function notifyCampaignRejected(\n  influencerId: string,\n  businessName: string,\n  campaignTitle: string,\n  applicationId: string\n) {\n  return createNotification(\n    influencerId,\n    'campaign_rejected',\n    'Aplikacija odbijena',\n    `${businessName} je odbio vašu aplikaciju za kampanju: \"${campaignTitle}\"`,\n    { application_id: applicationId, business_name: businessName }\n  );\n}\n\nexport async function notifyMessageReceived(\n  userId: string,\n  senderName: string,\n  conversationId: string\n) {\n  return createNotification(\n    userId,\n    'message_received',\n    'Nova poruka',\n    `${senderName} vam je poslao novu poruku`,\n    { conversation_id: conversationId, sender_name: senderName }\n  );\n}\n\nexport async function notifyPaymentReceived(\n  influencerId: string,\n  amount: number,\n  currency: string,\n  campaignTitle: string\n) {\n  return createNotification(\n    influencerId,\n    'payment_received',\n    'Plaćanje primljeno',\n    `Primili ste plaćanje od ${amount} ${currency} za kampanju: \"${campaignTitle}\"`,\n    { amount, currency, campaign_title: campaignTitle }\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;;AAyBO,eAAe,mBACpB,MAAc,EACd,IAAsB,EACtB,KAAa,EACb,OAAe,EACf,OAA4B,CAAC,CAAC;IAE9B,MAAM,EAAE,MAAM,YAAY,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,uBAAuB;QAC9E,WAAW;QACX,QAAQ;QACR,SAAS;QACT,WAAW;QACX,QAAQ;IACV;IAEA,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,OAAO;QAAE,MAAM;QAAc,OAAO;IAAK;AAC3C;AAGO,eAAe,qBAAqB,MAAe,EAAE,QAAQ,EAAE;IACpE,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM,GACvC,KAAK,CAAC;IAET,IAAI,QAAQ;QACV,QAAQ,MAAM,EAAE,CAAC,WAAW;IAC9B;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAE9B,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,OAAO;QAAE;QAAM,OAAO;IAAK;AAC7B;AAGO,eAAe,uBAAuB,cAAsB;IACjE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC;QAAE,MAAM;IAAK,GACpB,EAAE,CAAC,MAAM,gBACT,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,OAAO;QAAE;QAAM,OAAO;IAAK;AAC7B;AAGO,eAAe,2BAA2B,MAAc;IAC7D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC;QAAE,MAAM;IAAK,GACpB,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,QAAQ;IAEd,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,OAAO;QAAE;QAAM,OAAO;IAAK;AAC7B;AAGO,eAAe,2BAA2B,MAAc;IAC7D,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACpC,IAAI,CAAC,iBACL,MAAM,CAAC,KAAK;QAAE,OAAO;QAAS,MAAM;IAAK,GACzC,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,QAAQ;IAEd,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YAAE,OAAO;YAAG;QAAM;IAC3B;IAEA,OAAO;QAAE,OAAO,SAAS;QAAG,OAAO;IAAK;AAC1C;AAGO,eAAe,mBAAmB,cAAsB;IAC7D,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,iBACL,MAAM,GACN,EAAE,CAAC,MAAM;IAEZ,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YAAE;QAAM;IACjB;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB;AAIO,eAAe,oBACpB,YAAoB,EACpB,YAAoB,EACpB,UAAkB,EAClB,OAAe;IAEf,OAAO,mBACL,cACA,kBACA,wBACA,GAAG,aAAa,wBAAwB,EAAE,WAAW,CAAC,CAAC,EACvD;QAAE,UAAU;QAAS,eAAe;IAAa;AAErD;AAEO,eAAe,oBACpB,UAAkB,EAClB,cAAsB,EACtB,UAAkB,EAClB,OAAe;IAEf,OAAO,mBACL,YACA,kBACA,qBACA,GAAG,eAAe,4BAA4B,EAAE,WAAW,CAAC,CAAC,EAC7D;QAAE,UAAU;QAAS,iBAAiB;IAAe;AAEzD;AAEO,eAAe,oBACpB,UAAkB,EAClB,cAAsB,EACtB,UAAkB,EAClB,OAAe;IAEf,OAAO,mBACL,YACA,kBACA,mBACA,GAAG,eAAe,wBAAwB,EAAE,WAAW,CAAC,CAAC,EACzD;QAAE,UAAU;QAAS,iBAAiB;IAAe;AAEzD;AAEO,eAAe,0BACpB,UAAkB,EAClB,cAAsB,EACtB,aAAqB,EACrB,aAAqB;IAErB,OAAO,mBACL,YACA,wBACA,+BACA,GAAG,eAAe,2BAA2B,EAAE,cAAc,CAAC,CAAC,EAC/D;QAAE,gBAAgB;QAAe,iBAAiB;IAAe;AAErE;AAEO,eAAe,uBACpB,YAAoB,EACpB,YAAoB,EACpB,aAAqB,EACrB,aAAqB;IAErB,OAAO,mBACL,cACA,qBACA,yBACA,GAAG,aAAa,4CAA4C,EAAE,cAAc,CAAC,CAAC,EAC9E;QAAE,gBAAgB;QAAe,eAAe;IAAa;AAEjE;AAEO,eAAe,uBACpB,YAAoB,EACpB,YAAoB,EACpB,aAAqB,EACrB,aAAqB;IAErB,OAAO,mBACL,cACA,qBACA,uBACA,GAAG,aAAa,wCAAwC,EAAE,cAAc,CAAC,CAAC,EAC1E;QAAE,gBAAgB;QAAe,eAAe;IAAa;AAEjE;AAEO,eAAe,sBACpB,MAAc,EACd,UAAkB,EAClB,cAAsB;IAEtB,OAAO,mBACL,QACA,oBACA,eACA,GAAG,WAAW,0BAA0B,CAAC,EACzC;QAAE,iBAAiB;QAAgB,aAAa;IAAW;AAE/D;AAEO,eAAe,sBACpB,YAAoB,EACpB,MAAc,EACd,QAAgB,EAChB,aAAqB;IAErB,OAAO,mBACL,cACA,oBACA,sBACA,CAAC,wBAAwB,EAAE,OAAO,CAAC,EAAE,SAAS,eAAe,EAAE,cAAc,CAAC,CAAC,EAC/E;QAAE;QAAQ;QAAU,gBAAgB;IAAc;AAEtD", "debugId": null}}, {"offset": {"line": 2036, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/notifications/NotificationDropdown.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuLabel,\n  DropdownMenuTrigger,\n  DropdownMenuSeparator,\n  DropdownMenuItem,\n} from '@/components/ui/dropdown-menu';\nimport { \n  Bell, \n  Check, \n  CheckCheck,\n  Inbox,\n  MessageCircle,\n  DollarSign,\n  FileText,\n  Building2,\n  User\n} from 'lucide-react';\nimport { \n  getUserNotifications, \n  markNotificationAsRead, \n  markAllNotificationsAsRead,\n  getUnreadNotificationCount,\n  type Notification \n} from '@/lib/notifications';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { formatDistanceToNow } from 'date-fns';\nimport { hr } from 'date-fns/locale';\nimport { toast } from 'sonner';\nimport Link from 'next/link';\n\nexport function NotificationDropdown() {\n  const { user } = useAuth();\n  const [notifications, setNotifications] = useState<Notification[]>([]);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isOpen, setIsOpen] = useState(false);\n\n  useEffect(() => {\n    if (user) {\n      loadNotifications();\n      loadUnreadCount();\n    }\n  }, [user]);\n\n  const loadNotifications = async () => {\n    if (!user) return;\n    \n    setIsLoading(true);\n    try {\n      const { data, error } = await getUserNotifications(user.id, 20);\n      if (error) {\n        console.error('Error loading notifications:', error);\n      } else {\n        setNotifications(data || []);\n      }\n    } catch (error) {\n      console.error('Error loading notifications:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loadUnreadCount = async () => {\n    if (!user) return;\n    \n    try {\n      const { count, error } = await getUnreadNotificationCount(user.id);\n      if (error) {\n        console.error('Error loading unread count:', error);\n      } else {\n        setUnreadCount(count);\n      }\n    } catch (error) {\n      console.error('Error loading unread count:', error);\n    }\n  };\n\n  const handleMarkAsRead = async (notificationId: string) => {\n    try {\n      const { error } = await markNotificationAsRead(notificationId);\n      if (error) {\n        toast.error('Greška pri označavanju notifikacije');\n      } else {\n        setNotifications(prev => \n          prev.map(n => n.id === notificationId ? { ...n, read: true } : n)\n        );\n        setUnreadCount(prev => Math.max(0, prev - 1));\n      }\n    } catch (error) {\n      console.error('Error marking notification as read:', error);\n      toast.error('Greška pri označavanju notifikacije');\n    }\n  };\n\n  const handleMarkAllAsRead = async () => {\n    if (!user) return;\n    \n    try {\n      const { error } = await markAllNotificationsAsRead(user.id);\n      if (error) {\n        toast.error('Greška pri označavanju notifikacija');\n      } else {\n        setNotifications(prev => prev.map(n => ({ ...n, read: true })));\n        setUnreadCount(0);\n        toast.success('Sve notifikacije su označene kao pročitane');\n      }\n    } catch (error) {\n      console.error('Error marking all notifications as read:', error);\n      toast.error('Greška pri označavanju notifikacija');\n    }\n  };\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'offer_received':\n      case 'offer_accepted':\n      case 'offer_rejected':\n        return <Inbox className=\"h-4 w-4\" />;\n      case 'campaign_application':\n      case 'campaign_accepted':\n      case 'campaign_rejected':\n        return <FileText className=\"h-4 w-4\" />;\n      case 'message_received':\n        return <MessageCircle className=\"h-4 w-4\" />;\n      case 'payment_received':\n        return <DollarSign className=\"h-4 w-4\" />;\n      default:\n        return <Bell className=\"h-4 w-4\" />;\n    }\n  };\n\n  const getNotificationLink = (notification: Notification) => {\n    switch (notification.type) {\n      case 'offer_received':\n      case 'offer_accepted':\n      case 'offer_rejected':\n        if (notification.data.offer_id) {\n          return `/dashboard/influencer/offers/${notification.data.offer_id}`;\n        }\n        return '/dashboard/influencer/offers';\n      case 'campaign_application':\n      case 'campaign_accepted':\n      case 'campaign_rejected':\n        return '/dashboard/campaigns';\n      case 'message_received':\n        return '/dashboard/messages';\n      default:\n        return '/dashboard';\n    }\n  };\n\n  if (!user) return null;\n\n  return (\n    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n          <Bell className=\"h-5 w-5\" />\n          {unreadCount > 0 && (\n            <Badge \n              variant=\"destructive\" \n              className=\"absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n            >\n              {unreadCount > 99 ? '99+' : unreadCount}\n            </Badge>\n          )}\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\" className=\"w-80\">\n        <DropdownMenuLabel className=\"flex items-center justify-between p-4\">\n          <h3 className=\"font-semibold\">Notifikacije</h3>\n          {unreadCount > 0 && (\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={handleMarkAllAsRead}\n              className=\"text-xs\"\n            >\n              <CheckCheck className=\"h-3 w-3 mr-1\" />\n              Označi sve\n            </Button>\n          )}\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        \n        <ScrollArea className=\"h-96\">\n          {isLoading ? (\n            <div className=\"flex items-center justify-center p-8\">\n              <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-primary\"></div>\n            </div>\n          ) : notifications.length === 0 ? (\n            <div className=\"flex flex-col items-center justify-center p-8 text-center\">\n              <Bell className=\"h-8 w-8 text-muted-foreground mb-2\" />\n              <p className=\"text-sm text-muted-foreground\">Nema novih notifikacija</p>\n            </div>\n          ) : (\n            <div className=\"space-y-1\">\n              {notifications.map((notification) => (\n                <div\n                  key={notification.id}\n                  className={`p-3 hover:bg-muted/50 transition-colors border-l-2 ${\n                    notification.read ? 'border-transparent' : 'border-primary'\n                  }`}\n                >\n                  <Link \n                    href={getNotificationLink(notification)}\n                    onClick={() => {\n                      if (!notification.read) {\n                        handleMarkAsRead(notification.id);\n                      }\n                      setIsOpen(false);\n                    }}\n                    className=\"block\"\n                  >\n                    <div className=\"flex items-start gap-3\">\n                      <div className=\"flex-shrink-0 mt-0.5\">\n                        {getNotificationIcon(notification.type)}\n                      </div>\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-start justify-between\">\n                          <h4 className={`text-sm font-medium ${\n                            notification.read ? 'text-muted-foreground' : 'text-foreground'\n                          }`}>\n                            {notification.title}\n                          </h4>\n                          {!notification.read && (\n                            <Button\n                              variant=\"ghost\"\n                              size=\"sm\"\n                              onClick={(e) => {\n                                e.preventDefault();\n                                e.stopPropagation();\n                                handleMarkAsRead(notification.id);\n                              }}\n                              className=\"h-6 w-6 p-0 ml-2\"\n                            >\n                              <Check className=\"h-3 w-3\" />\n                            </Button>\n                          )}\n                        </div>\n                        <p className={`text-xs mt-1 ${\n                          notification.read ? 'text-muted-foreground' : 'text-muted-foreground'\n                        }`}>\n                          {notification.message}\n                        </p>\n                        <p className=\"text-xs text-muted-foreground mt-1\">\n                          {formatDistanceToNow(new Date(notification.created_at), { \n                            addSuffix: true, \n                            locale: hr \n                          })}\n                        </p>\n                      </div>\n                    </div>\n                  </Link>\n                </div>\n              ))}\n            </div>\n          )}\n        </ScrollArea>\n        \n        {notifications.length > 0 && (\n          <>\n            <DropdownMenuSeparator />\n            <div className=\"p-2\">\n              <Button variant=\"ghost\" size=\"sm\" className=\"w-full\" asChild>\n                <Link href=\"/dashboard/notifications\">\n                  Pogledaj sve notifikacije\n                </Link>\n              </Button>\n            </div>\n          </>\n        )}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAOA;AACA;AACA;AACA;AACA;AApCA;;;;;;;;;;;;;;AAsCO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;YACA;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,oBAAoB;QACxB,IAAI,CAAC,MAAM;QAEX,aAAa;QACb,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,2HAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,EAAE,EAAE;YAC5D,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,gCAAgC;YAChD,OAAO;gBACL,iBAAiB,QAAQ,EAAE;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,2HAAA,CAAA,6BAA0B,AAAD,EAAE,KAAK,EAAE;YACjE,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,+BAA+B;YAC/C,OAAO;gBACL,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,2HAAA,CAAA,yBAAsB,AAAD,EAAE;YAC/C,IAAI,OAAO;gBACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,iBAAiB;4BAAE,GAAG,CAAC;4BAAE,MAAM;wBAAK,IAAI;gBAEjE,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,2HAAA,CAAA,6BAA0B,AAAD,EAAE,KAAK,EAAE;YAC1D,IAAI,OAAO;gBACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,iBAAiB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC;4BAAE,GAAG,CAAC;4BAAE,MAAM;wBAAK,CAAC;gBAC5D,eAAe;gBACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,kNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ,aAAa,IAAI;YACvB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,IAAI,aAAa,IAAI,CAAC,QAAQ,EAAE;oBAC9B,OAAO,CAAC,6BAA6B,EAAE,aAAa,IAAI,CAAC,QAAQ,EAAE;gBACrE;gBACA,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC,4IAAA,CAAA,eAAY;QAAC,MAAM;QAAQ,cAAc;;0BACxC,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAK,WAAU;;sCAC1C,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACf,cAAc,mBACb,8OAAC,iIAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,WAAU;sCAET,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAKpC,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAM;gBAAM,WAAU;;kCACzC,8OAAC,4IAAA,CAAA,oBAAiB;wBAAC,WAAU;;0CAC3B,8OAAC;gCAAG,WAAU;0CAAgB;;;;;;4BAC7B,cAAc,mBACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAK7C,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCAEtB,8OAAC,0IAAA,CAAA,aAAU;wBAAC,WAAU;kCACnB,0BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;mCAEf,cAAc,MAAM,KAAK,kBAC3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;iDAG/C,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,8OAAC;oCAEC,WAAW,CAAC,mDAAmD,EAC7D,aAAa,IAAI,GAAG,uBAAuB,kBAC3C;8CAEF,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,oBAAoB;wCAC1B,SAAS;4CACP,IAAI,CAAC,aAAa,IAAI,EAAE;gDACtB,iBAAiB,aAAa,EAAE;4CAClC;4CACA,UAAU;wCACZ;wCACA,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,oBAAoB,aAAa,IAAI;;;;;;8DAExC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAW,CAAC,oBAAoB,EAClC,aAAa,IAAI,GAAG,0BAA0B,mBAC9C;8EACC,aAAa,KAAK;;;;;;gEAEpB,CAAC,aAAa,IAAI,kBACjB,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,CAAC;wEACR,EAAE,cAAc;wEAChB,EAAE,eAAe;wEACjB,iBAAiB,aAAa,EAAE;oEAClC;oEACA,WAAU;8EAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAIvB,8OAAC;4DAAE,WAAW,CAAC,aAAa,EAC1B,aAAa,IAAI,GAAG,0BAA0B,yBAC9C;sEACC,aAAa,OAAO;;;;;;sEAEvB,8OAAC;4DAAE,WAAU;sEACV,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,aAAa,UAAU,GAAG;gEACtD,WAAW;gEACX,QAAQ,2IAAA,CAAA,KAAE;4DACZ;;;;;;;;;;;;;;;;;;;;;;;mCAlDH,aAAa,EAAE;;;;;;;;;;;;;;;oBA6D7B,cAAc,MAAM,GAAG,mBACtB;;0CACE,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0CACtB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAS,OAAO;8CAC1D,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtD", "debugId": null}}, {"offset": {"line": 2508, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/dashboard/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { getProfile } from '@/lib/profiles';\nimport { ResponsiveNavigation } from '@/components/navigation/ResponsiveNavigation';\nimport { NotificationDropdown } from '@/components/notifications/NotificationDropdown';\nimport { Loader2 } from 'lucide-react';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n  requiredUserType?: 'influencer' | 'business';\n}\n\nexport function DashboardLayout({ children, requiredUserType }: DashboardLayoutProps) {\n  const { user, loading: authLoading } = useAuth();\n  const router = useRouter();\n  const [profile, setProfile] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (authLoading) return;\n\n    if (!user) {\n      router.push('/prijava');\n      return;\n    }\n\n    loadProfile();\n  }, [user, authLoading, router]);\n\n  const loadProfile = async () => {\n    try {\n      setLoading(true);\n      const { data, error } = await getProfile(user!.id);\n\n      if (error) {\n        console.error('Profile loading error:', error);\n        if (error.message && error.message.includes('No rows')) {\n          router.push('/profil/kreiranje');\n          return;\n        }\n        setError('Greška pri učitavanju profila');\n        return;\n      }\n\n      if (!data) {\n        router.push('/profil/kreiranje');\n        return;\n      }\n\n      setProfile(data);\n\n      // Provjeri da li korisnik ima pravo pristupa ovoj stranici\n      if (requiredUserType && data.user_type !== requiredUserType) {\n        // Preusmjeri na odgovarajući dashboard\n        if (data.user_type === 'influencer') {\n          router.push('/dashboard/influencer');\n        } else if (data.user_type === 'business') {\n          router.push('/dashboard/biznis');\n        }\n        return;\n      }\n    } catch (err) {\n      console.error('Unexpected error in loadProfile:', err);\n      setError('Neočekivana greška');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Loading state\n  if (authLoading || loading) {\n    return (\n      <div className=\"min-h-screen bg-background flex items-center justify-center\">\n        <div className=\"flex flex-col items-center space-y-4\">\n          <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\n          <p className=\"text-muted-foreground\">Učitavanje...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-background flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-foreground mb-2\">Greška</h2>\n          <p className=\"text-muted-foreground mb-4\">{error}</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90\"\n          >\n            Pokušaj ponovo\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // No profile state\n  if (!profile) {\n    return null; // Router redirect will handle this\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Responsive Navigation */}\n      <ResponsiveNavigation userType={profile.user_type} />\n\n      {/* Main Content - full width without sidebar */}\n      <div className=\"flex flex-col min-h-screen\">\n        {/* Header */}\n        <header className=\"bg-card border-b border-border px-6 py-4 md:pl-20\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-foreground\">\n                {profile.user_type === 'influencer' ? 'Influencer Dashboard' : 'Biznis Dashboard'}\n              </h1>\n              <p className=\"text-muted-foreground\">\n                Dobrodošli, {profile.full_name || profile.username}\n              </p>\n            </div>\n\n            {/* User Info */}\n            <div className=\"flex items-center space-x-3\">\n              <NotificationDropdown />\n              {profile.avatar_url && (\n                <img\n                  src={profile.avatar_url}\n                  alt={profile.username}\n                  className=\"w-8 h-8 rounded-full object-cover\"\n                />\n              )}\n              <div className=\"text-right hidden sm:block\">\n                <p className=\"text-sm font-medium text-foreground\">\n                  {profile.full_name || profile.username}\n                </p>\n                <p className=\"text-xs text-muted-foreground\">\n                  @{profile.username}\n                </p>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        {/* Page Content */}\n        <main className=\"flex-1 overflow-auto pb-16 md:pb-0\">\n          <div className=\"p-6\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAeO,SAAS,gBAAgB,EAAE,QAAQ,EAAE,gBAAgB,EAAwB;IAClF,MAAM,EAAE,IAAI,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC5C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;QAEjB,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,CAAC;YACZ;QACF;QAEA;IACF,GAAG;QAAC;QAAM;QAAa;KAAO;IAE9B,MAAM,cAAc;QAClB,IAAI;YACF,WAAW;YACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAM,EAAE;YAEjD,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;oBACtD,OAAO,IAAI,CAAC;oBACZ;gBACF;gBACA,SAAS;gBACT;YACF;YAEA,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,WAAW;YAEX,2DAA2D;YAC3D,IAAI,oBAAoB,KAAK,SAAS,KAAK,kBAAkB;gBAC3D,uCAAuC;gBACvC,IAAI,KAAK,SAAS,KAAK,cAAc;oBACnC,OAAO,IAAI,CAAC;gBACd,OAAO,IAAI,KAAK,SAAS,KAAK,YAAY;oBACxC,OAAO,IAAI,CAAC;gBACd;gBACA;YACF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oCAAoC;YAClD,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,gBAAgB;IAChB,IAAI,eAAe,SAAS;QAC1B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0C;;;;;;kCACxD,8OAAC;wBAAE,WAAU;kCAA8B;;;;;;kCAC3C,8OAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,mBAAmB;IACnB,IAAI,CAAC,SAAS;QACZ,OAAO,MAAM,mCAAmC;IAClD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,wJAAA,CAAA,uBAAoB;gBAAC,UAAU,QAAQ,SAAS;;;;;;0BAGjD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,QAAQ,SAAS,KAAK,eAAe,yBAAyB;;;;;;sDAEjE,8OAAC;4CAAE,WAAU;;gDAAwB;gDACtB,QAAQ,SAAS,IAAI,QAAQ,QAAQ;;;;;;;;;;;;;8CAKtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,2JAAA,CAAA,uBAAoB;;;;;wCACpB,QAAQ,UAAU,kBACjB,8OAAC;4CACC,KAAK,QAAQ,UAAU;4CACvB,KAAK,QAAQ,QAAQ;4CACrB,WAAU;;;;;;sDAGd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,QAAQ,SAAS,IAAI,QAAQ,QAAQ;;;;;;8DAExC,8OAAC;oDAAE,WAAU;;wDAAgC;wDACzC,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ5B,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 2802, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2897, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAG7B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,qMAAA,CAAA,aAAgB,CAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,kKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2946, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"skeleton\"\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2969, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/chat-permissions.ts"], "sourcesContent": ["import { supabase } from '@/lib/supabase';\n\nexport interface ChatPermission {\n  id: string;\n  business_id: string;\n  influencer_id: string;\n  offer_id: string | null;\n  campaign_application_id: string | null;\n  business_approved: boolean | null;\n  influencer_approved: boolean | null;\n  chat_enabled: boolean | null;\n  created_at: string | null;\n  updated_at: string | null;\n}\n\n/**\n * Create or update chat permission for direct offer\n */\nexport async function upsertOfferChatPermission(\n  businessId: string,\n  influencerId: string,\n  offerId: string,\n  businessApproved: boolean = false,\n  influencerApproved: boolean = false\n) {\n  const { data, error } = await supabase.rpc('upsert_chat_permission', {\n    p_business_id: businessId,\n    p_influencer_id: influencerId,\n    p_offer_id: offerId,\n    p_business_approved: businessApproved,\n    p_influencer_approved: influencerApproved\n  });\n\n  if (error) {\n    console.error('Error upserting offer chat permission:', error);\n    throw error;\n  }\n\n  return data;\n}\n\n/**\n * Create or update chat permission for campaign application\n */\nexport async function upsertApplicationChatPermission(\n  businessId: string,\n  influencerId: string,\n  applicationId: string,\n  businessApproved: boolean = false,\n  influencerApproved: boolean = false\n) {\n  const { data, error } = await supabase.rpc('upsert_chat_permission', {\n    p_business_id: businessId,\n    p_influencer_id: influencerId,\n    p_campaign_application_id: applicationId,\n    p_business_approved: businessApproved,\n    p_influencer_approved: influencerApproved\n  });\n\n  if (error) {\n    console.error('Error upserting application chat permission:', error);\n    throw error;\n  }\n\n  return data;\n}\n\n/**\n * Check if chat is enabled between business and influencer for specific offer/application\n */\nexport async function isChatEnabled(\n  businessId: string,\n  influencerId: string,\n  offerId?: string,\n  applicationId?: string\n): Promise<boolean> {\n  let query = supabase\n    .from('chat_permissions')\n    .select('chat_enabled')\n    .eq('business_id', businessId)\n    .eq('influencer_id', influencerId);\n\n  if (offerId) {\n    query = query.eq('offer_id', offerId);\n  } else if (applicationId) {\n    query = query.eq('campaign_application_id', applicationId);\n  } else {\n    throw new Error('Either offerId or applicationId must be provided');\n  }\n\n  const { data, error } = await query.single();\n\n  if (error) {\n    if (error.code === 'PGRST116') {\n      // No permission record found, chat not enabled\n      return false;\n    }\n    console.error('Error checking chat permission:', error);\n    throw error;\n  }\n\n  return data?.chat_enabled || false;\n}\n\n/**\n * Get chat permission details\n */\nexport async function getChatPermission(\n  businessId: string,\n  influencerId: string,\n  offerId?: string,\n  applicationId?: string\n): Promise<ChatPermission | null> {\n  let query = supabase\n    .from('chat_permissions')\n    .select('*')\n    .eq('business_id', businessId)\n    .eq('influencer_id', influencerId);\n\n  if (offerId) {\n    query = query.eq('offer_id', offerId);\n  } else if (applicationId) {\n    query = query.eq('campaign_application_id', applicationId);\n  } else {\n    throw new Error('Either offerId or applicationId must be provided');\n  }\n\n  const { data, error } = await query.single();\n\n  if (error) {\n    if (error.code === 'PGRST116') {\n      // No permission record found\n      return null;\n    }\n    console.error('Error getting chat permission:', error);\n    throw error;\n  }\n\n  return data;\n}\n\n/**\n * Approve chat from business side\n */\nexport async function approveBusinessChat(\n  businessId: string,\n  influencerId: string,\n  offerId?: string,\n  applicationId?: string\n) {\n  let query = supabase\n    .from('chat_permissions')\n    .update({ business_approved: true })\n    .eq('business_id', businessId)\n    .eq('influencer_id', influencerId);\n\n  if (offerId) {\n    query = query.eq('offer_id', offerId);\n  } else if (applicationId) {\n    query = query.eq('campaign_application_id', applicationId);\n  } else {\n    throw new Error('Either offerId or applicationId must be provided');\n  }\n\n  const { error } = await query;\n\n  if (error) {\n    console.error('Error approving business chat:', error);\n    throw error;\n  }\n}\n\n/**\n * Approve chat from influencer side\n */\nexport async function approveInfluencerChat(\n  businessId: string,\n  influencerId: string,\n  offerId?: string,\n  applicationId?: string\n) {\n  let query = supabase\n    .from('chat_permissions')\n    .update({ influencer_approved: true })\n    .eq('business_id', businessId)\n    .eq('influencer_id', influencerId);\n\n  if (offerId) {\n    query = query.eq('offer_id', offerId);\n  } else if (applicationId) {\n    query = query.eq('campaign_application_id', applicationId);\n  } else {\n    throw new Error('Either offerId or applicationId must be provided');\n  }\n\n  const { error } = await query;\n\n  if (error) {\n    console.error('Error approving influencer chat:', error);\n    throw error;\n  }\n}\n\n/**\n * Get all chat permissions for a user (business or influencer)\n */\nexport async function getUserChatPermissions(userId: string): Promise<ChatPermission[]> {\n  const { data, error } = await supabase\n    .from('chat_permissions')\n    .select('*')\n    .or(`business_id.eq.${userId},influencer_id.eq.${userId}`)\n    .order('created_at', { ascending: false });\n\n  if (error) {\n    console.error('Error getting user chat permissions:', error);\n    throw error;\n  }\n\n  return data || [];\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAkBO,eAAe,0BACpB,UAAkB,EAClB,YAAoB,EACpB,OAAe,EACf,mBAA4B,KAAK,EACjC,qBAA8B,KAAK;IAEnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,0BAA0B;QACnE,eAAe;QACf,iBAAiB;QACjB,YAAY;QACZ,qBAAqB;QACrB,uBAAuB;IACzB;IAEA,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,0CAA0C;QACxD,MAAM;IACR;IAEA,OAAO;AACT;AAKO,eAAe,gCACpB,UAAkB,EAClB,YAAoB,EACpB,aAAqB,EACrB,mBAA4B,KAAK,EACjC,qBAA8B,KAAK;IAEnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,0BAA0B;QACnE,eAAe;QACf,iBAAiB;QACjB,2BAA2B;QAC3B,qBAAqB;QACrB,uBAAuB;IACzB;IAEA,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,MAAM;IACR;IAEA,OAAO;AACT;AAKO,eAAe,cACpB,UAAkB,EAClB,YAAoB,EACpB,OAAgB,EAChB,aAAsB;IAEtB,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,oBACL,MAAM,CAAC,gBACP,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,iBAAiB;IAEvB,IAAI,SAAS;QACX,QAAQ,MAAM,EAAE,CAAC,YAAY;IAC/B,OAAO,IAAI,eAAe;QACxB,QAAQ,MAAM,EAAE,CAAC,2BAA2B;IAC9C,OAAO;QACL,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,MAAM;IAE1C,IAAI,OAAO;QACT,IAAI,MAAM,IAAI,KAAK,YAAY;YAC7B,+CAA+C;YAC/C,OAAO;QACT;QACA,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;IAEA,OAAO,MAAM,gBAAgB;AAC/B;AAKO,eAAe,kBACpB,UAAkB,EAClB,YAAoB,EACpB,OAAgB,EAChB,aAAsB;IAEtB,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,iBAAiB;IAEvB,IAAI,SAAS;QACX,QAAQ,MAAM,EAAE,CAAC,YAAY;IAC/B,OAAO,IAAI,eAAe;QACxB,QAAQ,MAAM,EAAE,CAAC,2BAA2B;IAC9C,OAAO;QACL,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,MAAM;IAE1C,IAAI,OAAO;QACT,IAAI,MAAM,IAAI,KAAK,YAAY;YAC7B,6BAA6B;YAC7B,OAAO;QACT;QACA,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;IAEA,OAAO;AACT;AAKO,eAAe,oBACpB,UAAkB,EAClB,YAAoB,EACpB,OAAgB,EAChB,aAAsB;IAEtB,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,oBACL,MAAM,CAAC;QAAE,mBAAmB;IAAK,GACjC,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,iBAAiB;IAEvB,IAAI,SAAS;QACX,QAAQ,MAAM,EAAE,CAAC,YAAY;IAC/B,OAAO,IAAI,eAAe;QACxB,QAAQ,MAAM,EAAE,CAAC,2BAA2B;IAC9C,OAAO;QACL,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM;IAExB,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAKO,eAAe,sBACpB,UAAkB,EAClB,YAAoB,EACpB,OAAgB,EAChB,aAAsB;IAEtB,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,oBACL,MAAM,CAAC;QAAE,qBAAqB;IAAK,GACnC,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,iBAAiB;IAEvB,IAAI,SAAS;QACX,QAAQ,MAAM,EAAE,CAAC,YAAY;IAC/B,OAAO,IAAI,eAAe;QACxB,QAAQ,MAAM,EAAE,CAAC,2BAA2B;IAC9C,OAAO;QACL,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM;IAExB,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAKO,eAAe,uBAAuB,MAAc;IACzD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,EAAE,CAAC,CAAC,eAAe,EAAE,OAAO,kBAAkB,EAAE,QAAQ,EACxD,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,wCAAwC;QACtD,MAAM;IACR;IAEA,OAAO,QAAQ,EAAE;AACnB", "debugId": null}}, {"offset": {"line": 3096, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/chat.ts"], "sourcesContent": ["import { supabase } from './supabase';\r\nimport { isChatEnabled, upsertApplicationChatPermission, upsertOfferChatPermission } from './chat-permissions';\r\n\r\nexport interface ChatRoom {\r\n  id: string;\r\n  business_id: string;\r\n  influencer_id: string;\r\n  campaign_application_id: string | null;\r\n  offer_id: string | null;\r\n  room_title: string;\r\n  room_type: string;\r\n  created_at: string | null;\r\n  updated_at: string | null;\r\n  last_message_at: string | null;\r\n\r\n  // Joined data\r\n  business_profile?: {\r\n    full_name: string;\r\n    username: string;\r\n    avatar_url: string | null;\r\n  };\r\n  influencer_profile?: {\r\n    full_name: string;\r\n    username: string;\r\n    avatar_url: string | null;\r\n  };\r\n  unread_count?: number;\r\n}\r\n\r\nexport interface ChatMessage {\r\n  id: string;\r\n  room_id: string;\r\n  sender_id: string;\r\n  sender_type: string;\r\n  message_text: string | null;\r\n  file_url: string | null;\r\n  file_name: string | null;\r\n  file_type: string | null;\r\n  file_size: number | null;\r\n  created_at: string | null;\r\n  read_at: string | null;\r\n  edited_at: string | null;\r\n\r\n  // Joined data\r\n  sender_profile?: {\r\n    full_name: string;\r\n    username: string;\r\n    avatar_url: string | null;\r\n  };\r\n}\r\n\r\nexport interface ChatParticipant {\r\n  id: string;\r\n  room_id: string;\r\n  user_id: string;\r\n  user_type: 'business' | 'influencer';\r\n  joined_at: string;\r\n  last_read_at: string | null;\r\n  is_active: boolean;\r\n}\r\n\r\n/**\r\n * Get or create a chat room for a campaign application\r\n */\r\nexport async function getOrCreateChatRoomForApplication(\r\n  campaignApplicationId: string\r\n): Promise<{ data: ChatRoom | null; error: any }> {\r\n\r\n  try {\r\n    // First, get the campaign application details\r\n    const { data: application, error: appError } = await supabase\r\n      .from('campaign_applications')\r\n      .select(`\r\n        id,\r\n        campaign_id,\r\n        influencer_id,\r\n        campaigns!inner(\r\n          id,\r\n          title,\r\n          business_id\r\n        )\r\n      `)\r\n      .eq('id', campaignApplicationId)\r\n      .single();\r\n\r\n    if (appError || !application) {\r\n      return { data: null, error: appError || 'Application not found' };\r\n    }\r\n\r\n    const businessId = application.campaigns.business_id;\r\n    const influencerId = application.influencer_id;\r\n    const campaignTitle = application.campaigns.title;\r\n\r\n    // Check if chat is enabled via permissions\r\n    const chatEnabled = await isChatEnabled(businessId, influencerId, undefined, campaignApplicationId);\r\n    if (!chatEnabled) {\r\n      return { data: null, error: 'Chat not enabled for this application' };\r\n    }\r\n\r\n    // Check if room already exists\r\n    const { data: existingRoom, error: roomError } = await supabase\r\n      .from('chat_rooms')\r\n      .select('*')\r\n      .eq('business_id', businessId)\r\n      .eq('influencer_id', influencerId)\r\n      .eq('campaign_application_id', campaignApplicationId)\r\n      .single();\r\n\r\n    if (existingRoom) {\r\n      return { data: existingRoom, error: null };\r\n    }\r\n\r\n    // Create new room\r\n    const roomTitle = `Kampanja: ${campaignTitle}`;\r\n    const { data: newRoom, error: createError } = await supabase\r\n      .from('chat_rooms')\r\n      .insert({\r\n        business_id: businessId,\r\n        influencer_id: influencerId,\r\n        campaign_application_id: campaignApplicationId,\r\n        room_title: roomTitle,\r\n        room_type: 'campaign_application'\r\n      })\r\n      .select()\r\n      .single();\r\n\r\n    return { data: newRoom, error: createError };\r\n  } catch (error) {\r\n    return { data: null, error };\r\n  }\r\n}\r\n\r\n/**\r\n * Get or create a chat room for a direct offer\r\n */\r\nexport async function getOrCreateChatRoomForOffer(\r\n  offerId: string\r\n): Promise<{ data: ChatRoom | null; error: any }> {\r\n\r\n  try {\r\n    console.log('getOrCreateChatRoomForOffer: Starting for offer:', offerId);\r\n    // First, get the offer details\r\n    const { data: offer, error: offerError } = await supabase\r\n      .from('direct_offers')\r\n      .select('id, title, business_id, influencer_id')\r\n      .eq('id', offerId)\r\n      .single();\r\n\r\n    if (offerError || !offer) {\r\n      console.log('getOrCreateChatRoomForOffer: Offer error:', offerError);\r\n      return { data: null, error: offerError || 'Offer not found' };\r\n    }\r\n\r\n    console.log('getOrCreateChatRoomForOffer: Offer found:', offer);\r\n\r\n    // Check if chat is enabled via permissions\r\n    console.log('getOrCreateChatRoomForOffer: Checking if chat enabled...');\r\n    const chatEnabled = await isChatEnabled(offer.business_id, offer.influencer_id, offerId);\r\n    console.log('getOrCreateChatRoomForOffer: Chat enabled:', chatEnabled);\r\n    if (!chatEnabled) {\r\n      return { data: null, error: 'Chat not enabled for this offer' };\r\n    }\r\n\r\n    // Check if room already exists\r\n    console.log('getOrCreateChatRoomForOffer: Checking for existing room...');\r\n    const { data: existingRoom, error: roomError } = await supabase\r\n      .from('chat_rooms')\r\n      .select('*')\r\n      .eq('business_id', offer.business_id)\r\n      .eq('influencer_id', offer.influencer_id)\r\n      .eq('offer_id', offerId)\r\n      .single();\r\n\r\n    console.log('getOrCreateChatRoomForOffer: Existing room check:', { existingRoom, roomError });\r\n\r\n    if (existingRoom) {\r\n      console.log('getOrCreateChatRoomForOffer: Found existing room:', existingRoom);\r\n      return { data: existingRoom, error: null };\r\n    }\r\n\r\n    // Create new room\r\n    const roomTitle = `Direktna ponuda: ${offer.title}`;\r\n    console.log('getOrCreateChatRoomForOffer: Creating new room with title:', roomTitle);\r\n    const { data: newRoom, error: createError } = await supabase\r\n      .from('chat_rooms')\r\n      .insert({\r\n        business_id: offer.business_id,\r\n        influencer_id: offer.influencer_id,\r\n        offer_id: offerId,\r\n        room_title: roomTitle,\r\n        room_type: 'direct_offer'\r\n      })\r\n      .select()\r\n      .single();\r\n\r\n    console.log('getOrCreateChatRoomForOffer: Create result:', { newRoom, createError });\r\n    return { data: newRoom, error: createError };\r\n  } catch (error) {\r\n    return { data: null, error };\r\n  }\r\n}\r\n\r\n/**\r\n * Get all chat rooms for current user\r\n */\r\nexport async function getUserChatRooms(): Promise<{ data: ChatRoom[] | null; error: any }> {\r\n\r\n  try {\r\n    console.log('getUserChatRooms: Starting...');\r\n    const { data: user } = await supabase.auth.getUser();\r\n    if (!user.user) {\r\n      console.log('getUserChatRooms: Not authenticated');\r\n      return { data: null, error: 'Not authenticated' };\r\n    }\r\n\r\n    console.log('getUserChatRooms: User ID:', user.user.id);\r\n    const { data: profile } = await supabase\r\n      .from('profiles')\r\n      .select('user_type')\r\n      .eq('id', user.user.id)\r\n      .single();\r\n\r\n    if (!profile) {\r\n      console.log('getUserChatRooms: Profile not found');\r\n      return { data: null, error: 'Profile not found' };\r\n    }\r\n\r\n    console.log('getUserChatRooms: User type:', profile.user_type);\r\n\r\n    // Get rooms based on user type - simplified query first\r\n    console.log('getUserChatRooms: Querying chat_rooms...');\r\n    const query = supabase\r\n      .from('chat_rooms')\r\n      .select('*')\r\n      .order('last_message_at', { ascending: false, nullsFirst: false });\r\n\r\n    if (profile.user_type === 'business') {\r\n      query.eq('business_id', user.user.id);\r\n    } else {\r\n      query.eq('influencer_id', user.user.id);\r\n    }\r\n\r\n    const { data: rooms, error } = await query;\r\n    console.log('getUserChatRooms: Query result:', { rooms, error });\r\n\r\n    if (error) {\r\n      console.log('getUserChatRooms: Database error:', error);\r\n      return { data: null, error };\r\n    }\r\n\r\n    console.log('getUserChatRooms: Found rooms:', rooms?.length || 0);\r\n\r\n    // Enhance rooms with profile data\r\n    if (rooms && rooms.length > 0) {\r\n      const enhancedRooms = await Promise.all(\r\n        rooms.map(async (room) => {\r\n          // Get business profile\r\n          const { data: businessProfile } = await supabase\r\n            .from('profiles')\r\n            .select('full_name, username, avatar_url')\r\n            .eq('id', room.business_id)\r\n            .single();\r\n\r\n          // Get influencer profile\r\n          const { data: influencerProfile } = await supabase\r\n            .from('profiles')\r\n            .select('full_name, username, avatar_url')\r\n            .eq('id', room.influencer_id)\r\n            .single();\r\n\r\n          return {\r\n            ...room,\r\n            business_profile: businessProfile,\r\n            influencer_profile: influencerProfile\r\n          };\r\n        })\r\n      );\r\n\r\n      console.log('getUserChatRooms: Enhanced rooms with profiles');\r\n      return { data: enhancedRooms, error: null };\r\n    }\r\n\r\n    return { data: rooms || [], error: null };\r\n  } catch (error) {\r\n    console.log('getUserChatRooms: Catch error:', error);\r\n    return { data: null, error };\r\n  }\r\n}\r\n\r\n/**\r\n * Get messages for a chat room\r\n */\r\nexport async function getChatMessages(\r\n  roomId: string,\r\n  limit: number = 50,\r\n  offset: number = 0\r\n): Promise<{ data: ChatMessage[] | null; error: any }> {\r\n\r\n  try {\r\n    const { data: messages, error } = await supabase\r\n      .from('chat_messages')\r\n      .select(`\r\n        *,\r\n        sender_profile:profiles!chat_messages_sender_id_fkey(\r\n          full_name,\r\n          username,\r\n          avatar_url\r\n        )\r\n      `)\r\n      .eq('room_id', roomId)\r\n      .order('created_at', { ascending: false })\r\n      .range(offset, offset + limit - 1);\r\n\r\n    if (error) {\r\n      return { data: null, error };\r\n    }\r\n\r\n    // Reverse to show oldest first\r\n    return { data: messages?.reverse() || [], error: null };\r\n  } catch (error) {\r\n    return { data: null, error };\r\n  }\r\n}\r\n\r\n/**\r\n * Send a message to a chat room\r\n */\r\nexport async function sendChatMessage(\r\n  roomId: string,\r\n  messageText?: string,\r\n  fileUrl?: string,\r\n  fileName?: string,\r\n  fileType?: string,\r\n  fileSize?: number\r\n): Promise<{ data: ChatMessage | null; error: any }> {\r\n\r\n  try {\r\n    const { data: user } = await supabase.auth.getUser();\r\n    if (!user.user) {\r\n      return { data: null, error: 'Not authenticated' };\r\n    }\r\n\r\n    const { data: profile } = await supabase\r\n      .from('profiles')\r\n      .select('user_type')\r\n      .eq('id', user.user.id)\r\n      .single();\r\n\r\n    if (!profile) {\r\n      return { data: null, error: 'Profile not found' };\r\n    }\r\n\r\n    // Validate that either message text or file is provided\r\n    if (!messageText && !fileUrl) {\r\n      return { data: null, error: 'Message must contain text or file' };\r\n    }\r\n\r\n    const { data: message, error } = await supabase\r\n      .from('chat_messages')\r\n      .insert({\r\n        room_id: roomId,\r\n        sender_id: user.user.id,\r\n        sender_type: profile.user_type,\r\n        message_text: messageText,\r\n        file_url: fileUrl,\r\n        file_name: fileName,\r\n        file_type: fileType,\r\n        file_size: fileSize\r\n      })\r\n      .select(`\r\n        *,\r\n        sender_profile:profiles!chat_messages_sender_id_fkey(\r\n          full_name,\r\n          username,\r\n          avatar_url\r\n        )\r\n      `)\r\n      .single();\r\n\r\n    return { data: message, error };\r\n  } catch (error) {\r\n    return { data: null, error };\r\n  }\r\n}\r\n\r\n/**\r\n * Mark messages as read\r\n */\r\nexport async function markMessagesAsRead(\r\n  roomId: string,\r\n  messageIds?: string[]\r\n): Promise<{ error: any }> {\r\n\r\n  try {\r\n    console.log('markMessagesAsRead called with:', { roomId, messageIds });\r\n\r\n    const { data: user } = await supabase.auth.getUser();\r\n    if (!user.user) {\r\n      console.log('markMessagesAsRead: Not authenticated');\r\n      return { error: 'Not authenticated' };\r\n    }\r\n\r\n    let query = supabase\r\n      .from('chat_messages')\r\n      .update({ read_at: new Date().toISOString() })\r\n      .eq('room_id', roomId)\r\n      .neq('sender_id', user.user.id)\r\n      .is('read_at', null);\r\n\r\n    if (messageIds) {\r\n      console.log('markMessagesAsRead: Adding messageIds filter:', messageIds);\r\n\r\n      // Ensure messageIds is an array\r\n      const idsArray = Array.isArray(messageIds) ? messageIds : [messageIds];\r\n      console.log('markMessagesAsRead: IDs as array:', idsArray);\r\n\r\n      // Validate that all messageIds are valid UUIDs\r\n      const validIds = idsArray.filter(id =>\r\n        typeof id === 'string' &&\r\n        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id)\r\n      );\r\n      console.log('markMessagesAsRead: Valid UUIDs:', validIds);\r\n\r\n      if (validIds.length > 0) {\r\n        query = query.in('id', validIds);\r\n      }\r\n    }\r\n\r\n    console.log('markMessagesAsRead: Executing query...');\r\n    const { error } = await query;\r\n    console.log('markMessagesAsRead: Query result:', { error });\r\n    return { error };\r\n  } catch (error) {\r\n    console.log('markMessagesAsRead: Catch error:', error);\r\n    return { error };\r\n  }\r\n}\r\n\r\n/**\r\n * Update participant's last read timestamp\r\n */\r\nexport async function updateLastReadAt(roomId: string): Promise<{ error: any }> {\r\n\r\n  try {\r\n    const { data: user } = await supabase.auth.getUser();\r\n    if (!user.user) {\r\n      return { error: 'Not authenticated' };\r\n    }\r\n\r\n    const { error } = await supabase\r\n      .from('chat_participants')\r\n      .update({ last_read_at: new Date().toISOString() })\r\n      .eq('room_id', roomId)\r\n      .eq('user_id', user.user.id);\r\n\r\n    return { error };\r\n  } catch (error) {\r\n    return { error };\r\n  }\r\n}\r\n\r\n/**\r\n * Enable chat for a campaign application (creates permission and room)\r\n */\r\nexport async function enableChatForApplication(\r\n  campaignApplicationId: string,\r\n  businessApproved: boolean = true,\r\n  influencerApproved: boolean = true\r\n): Promise<{ data: ChatRoom | null; error: any }> {\r\n\r\n  try {\r\n    // Get application details\r\n    const { data: application, error: appError } = await supabase\r\n      .from('campaign_applications')\r\n      .select(`\r\n        id,\r\n        campaign_id,\r\n        influencer_id,\r\n        campaigns!inner(\r\n          id,\r\n          title,\r\n          business_id\r\n        )\r\n      `)\r\n      .eq('id', campaignApplicationId)\r\n      .single();\r\n\r\n    if (appError || !application) {\r\n      return { data: null, error: appError || 'Application not found' };\r\n    }\r\n\r\n    const businessId = application.campaigns.business_id;\r\n    const influencerId = application.influencer_id;\r\n\r\n    // Create or update chat permission\r\n    await upsertApplicationChatPermission(\r\n      businessId,\r\n      influencerId,\r\n      campaignApplicationId,\r\n      businessApproved,\r\n      influencerApproved\r\n    );\r\n\r\n    // Create chat room\r\n    return await getOrCreateChatRoomForApplication(campaignApplicationId);\r\n  } catch (error) {\r\n    return { data: null, error };\r\n  }\r\n}\r\n\r\n/**\r\n * Enable chat for a direct offer (creates permission and room)\r\n */\r\nexport async function enableChatForOffer(\r\n  offerId: string,\r\n  businessApproved: boolean = true,\r\n  influencerApproved: boolean = true\r\n): Promise<{ data: ChatRoom | null; error: any }> {\r\n\r\n  try {\r\n    console.log('enableChatForOffer: Starting for offer:', offerId);\r\n    // Get offer details\r\n    const { data: offer, error: offerError } = await supabase\r\n      .from('direct_offers')\r\n      .select('id, title, business_id, influencer_id')\r\n      .eq('id', offerId)\r\n      .single();\r\n\r\n    if (offerError || !offer) {\r\n      console.log('enableChatForOffer: Offer error:', offerError);\r\n      return { data: null, error: offerError || 'Offer not found' };\r\n    }\r\n\r\n    console.log('enableChatForOffer: Offer found:', offer);\r\n\r\n    // Create or update chat permission\r\n    console.log('enableChatForOffer: Creating permission...');\r\n    await upsertOfferChatPermission(\r\n      offer.business_id,\r\n      offer.influencer_id,\r\n      offerId,\r\n      businessApproved,\r\n      influencerApproved\r\n    );\r\n\r\n    // Create chat room\r\n    console.log('enableChatForOffer: Creating chat room...');\r\n    const result = await getOrCreateChatRoomForOffer(offerId);\r\n    console.log('enableChatForOffer: Result:', result);\r\n    return result;\r\n  } catch (error) {\r\n    console.log('enableChatForOffer: Catch error:', error);\r\n    return { data: null, error };\r\n  }\r\n}\r\n\r\n/**\r\n * Get a specific chat room by ID\r\n */\r\nexport async function getChatRoom(roomId: string): Promise<{ data: ChatRoom | null; error: any }> {\r\n\r\n  try {\r\n    console.log('getChatRoom: Loading room:', roomId);\r\n\r\n    // First get the basic room data\r\n    const { data: room, error: roomError } = await supabase\r\n      .from('chat_rooms')\r\n      .select('*')\r\n      .eq('id', roomId)\r\n      .single();\r\n\r\n    console.log('getChatRoom: Room data:', { room, roomError });\r\n\r\n    if (roomError || !room) {\r\n      return { data: null, error: roomError };\r\n    }\r\n\r\n    // Then get the profile data separately\r\n    const { data: businessProfile } = await supabase\r\n      .from('profiles')\r\n      .select('full_name, username, avatar_url')\r\n      .eq('id', room.business_id)\r\n      .single();\r\n\r\n    const { data: influencerProfile } = await supabase\r\n      .from('profiles')\r\n      .select('full_name, username, avatar_url')\r\n      .eq('id', room.influencer_id)\r\n      .single();\r\n\r\n    console.log('getChatRoom: Profiles:', { businessProfile, influencerProfile });\r\n\r\n    // Combine the data\r\n    const enrichedRoom = {\r\n      ...room,\r\n      business_profile: businessProfile,\r\n      influencer_profile: influencerProfile\r\n    };\r\n\r\n    console.log('getChatRoom: Final room:', enrichedRoom);\r\n    return { data: enrichedRoom, error: null };\r\n  } catch (error) {\r\n    console.log('getChatRoom: Catch error:', error);\r\n    return { data: null, error };\r\n  }\r\n}"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AA+DO,eAAe,kCACpB,qBAA6B;IAG7B,IAAI;QACF,8CAA8C;QAC9C,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,QAAQ,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC1D,IAAI,CAAC,yBACL,MAAM,CAAC,CAAC;;;;;;;;;MAST,CAAC,EACA,EAAE,CAAC,MAAM,uBACT,MAAM;QAET,IAAI,YAAY,CAAC,aAAa;YAC5B,OAAO;gBAAE,MAAM;gBAAM,OAAO,YAAY;YAAwB;QAClE;QAEA,MAAM,aAAa,YAAY,SAAS,CAAC,WAAW;QACpD,MAAM,eAAe,YAAY,aAAa;QAC9C,MAAM,gBAAgB,YAAY,SAAS,CAAC,KAAK;QAEjD,2CAA2C;QAC3C,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,cAAc,WAAW;QAC7E,IAAI,CAAC,aAAa;YAChB,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAwC;QACtE;QAEA,+BAA+B;QAC/B,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC5D,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,iBAAiB,cACpB,EAAE,CAAC,2BAA2B,uBAC9B,MAAM;QAET,IAAI,cAAc;YAChB,OAAO;gBAAE,MAAM;gBAAc,OAAO;YAAK;QAC3C;QAEA,kBAAkB;QAClB,MAAM,YAAY,CAAC,UAAU,EAAE,eAAe;QAC9C,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACzD,IAAI,CAAC,cACL,MAAM,CAAC;YACN,aAAa;YACb,eAAe;YACf,yBAAyB;YACzB,YAAY;YACZ,WAAW;QACb,GACC,MAAM,GACN,MAAM;QAET,OAAO;YAAE,MAAM;YAAS,OAAO;QAAY;IAC7C,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAKO,eAAe,4BACpB,OAAe;IAGf,IAAI;QACF,QAAQ,GAAG,CAAC,oDAAoD;QAChE,+BAA+B;QAC/B,MAAM,EAAE,MAAM,KAAK,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACtD,IAAI,CAAC,iBACL,MAAM,CAAC,yCACP,EAAE,CAAC,MAAM,SACT,MAAM;QAET,IAAI,cAAc,CAAC,OAAO;YACxB,QAAQ,GAAG,CAAC,6CAA6C;YACzD,OAAO;gBAAE,MAAM;gBAAM,OAAO,cAAc;YAAkB;QAC9D;QAEA,QAAQ,GAAG,CAAC,6CAA6C;QAEzD,2CAA2C;QAC3C,QAAQ,GAAG,CAAC;QACZ,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,WAAW,EAAE,MAAM,aAAa,EAAE;QAChF,QAAQ,GAAG,CAAC,8CAA8C;QAC1D,IAAI,CAAC,aAAa;YAChB,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAkC;QAChE;QAEA,+BAA+B;QAC/B,QAAQ,GAAG,CAAC;QACZ,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC5D,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,MAAM,WAAW,EACnC,EAAE,CAAC,iBAAiB,MAAM,aAAa,EACvC,EAAE,CAAC,YAAY,SACf,MAAM;QAET,QAAQ,GAAG,CAAC,qDAAqD;YAAE;YAAc;QAAU;QAE3F,IAAI,cAAc;YAChB,QAAQ,GAAG,CAAC,qDAAqD;YACjE,OAAO;gBAAE,MAAM;gBAAc,OAAO;YAAK;QAC3C;QAEA,kBAAkB;QAClB,MAAM,YAAY,CAAC,iBAAiB,EAAE,MAAM,KAAK,EAAE;QACnD,QAAQ,GAAG,CAAC,8DAA8D;QAC1E,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACzD,IAAI,CAAC,cACL,MAAM,CAAC;YACN,aAAa,MAAM,WAAW;YAC9B,eAAe,MAAM,aAAa;YAClC,UAAU;YACV,YAAY;YACZ,WAAW;QACb,GACC,MAAM,GACN,MAAM;QAET,QAAQ,GAAG,CAAC,+CAA+C;YAAE;YAAS;QAAY;QAClF,OAAO;YAAE,MAAM;YAAS,OAAO;QAAY;IAC7C,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAKO,eAAe;IAEpB,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,QAAQ,GAAG,CAAC;YACZ,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAoB;QAClD;QAEA,QAAQ,GAAG,CAAC,8BAA8B,KAAK,IAAI,CAAC,EAAE;QACtD,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACrC,IAAI,CAAC,YACL,MAAM,CAAC,aACP,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,EACrB,MAAM;QAET,IAAI,CAAC,SAAS;YACZ,QAAQ,GAAG,CAAC;YACZ,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAoB;QAClD;QAEA,QAAQ,GAAG,CAAC,gCAAgC,QAAQ,SAAS;QAE7D,wDAAwD;QACxD,QAAQ,GAAG,CAAC;QACZ,MAAM,QAAQ,sHAAA,CAAA,WAAQ,CACnB,IAAI,CAAC,cACL,MAAM,CAAC,KACP,KAAK,CAAC,mBAAmB;YAAE,WAAW;YAAO,YAAY;QAAM;QAElE,IAAI,QAAQ,SAAS,KAAK,YAAY;YACpC,MAAM,EAAE,CAAC,eAAe,KAAK,IAAI,CAAC,EAAE;QACtC,OAAO;YACL,MAAM,EAAE,CAAC,iBAAiB,KAAK,IAAI,CAAC,EAAE;QACxC;QAEA,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;QACrC,QAAQ,GAAG,CAAC,mCAAmC;YAAE;YAAO;QAAM;QAE9D,IAAI,OAAO;YACT,QAAQ,GAAG,CAAC,qCAAqC;YACjD,OAAO;gBAAE,MAAM;gBAAM;YAAM;QAC7B;QAEA,QAAQ,GAAG,CAAC,kCAAkC,OAAO,UAAU;QAE/D,kCAAkC;QAClC,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC7B,MAAM,gBAAgB,MAAM,QAAQ,GAAG,CACrC,MAAM,GAAG,CAAC,OAAO;gBACf,uBAAuB;gBACvB,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7C,IAAI,CAAC,YACL,MAAM,CAAC,mCACP,EAAE,CAAC,MAAM,KAAK,WAAW,EACzB,MAAM;gBAET,yBAAyB;gBACzB,MAAM,EAAE,MAAM,iBAAiB,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC/C,IAAI,CAAC,YACL,MAAM,CAAC,mCACP,EAAE,CAAC,MAAM,KAAK,aAAa,EAC3B,MAAM;gBAET,OAAO;oBACL,GAAG,IAAI;oBACP,kBAAkB;oBAClB,oBAAoB;gBACtB;YACF;YAGF,QAAQ,GAAG,CAAC;YACZ,OAAO;gBAAE,MAAM;gBAAe,OAAO;YAAK;QAC5C;QAEA,OAAO;YAAE,MAAM,SAAS,EAAE;YAAE,OAAO;QAAK;IAC1C,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC,kCAAkC;QAC9C,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAKO,eAAe,gBACpB,MAAc,EACd,QAAgB,EAAE,EAClB,SAAiB,CAAC;IAGlB,IAAI;QACF,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7C,IAAI,CAAC,iBACL,MAAM,CAAC,CAAC;;;;;;;MAOT,CAAC,EACA,EAAE,CAAC,WAAW,QACd,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC,QAAQ,SAAS,QAAQ;QAElC,IAAI,OAAO;YACT,OAAO;gBAAE,MAAM;gBAAM;YAAM;QAC7B;QAEA,+BAA+B;QAC/B,OAAO;YAAE,MAAM,UAAU,aAAa,EAAE;YAAE,OAAO;QAAK;IACxD,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAKO,eAAe,gBACpB,MAAc,EACd,WAAoB,EACpB,OAAgB,EAChB,QAAiB,EACjB,QAAiB,EACjB,QAAiB;IAGjB,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAoB;QAClD;QAEA,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACrC,IAAI,CAAC,YACL,MAAM,CAAC,aACP,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,EACrB,MAAM;QAET,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAoB;QAClD;QAEA,wDAAwD;QACxD,IAAI,CAAC,eAAe,CAAC,SAAS;YAC5B,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAoC;QAClE;QAEA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC5C,IAAI,CAAC,iBACL,MAAM,CAAC;YACN,SAAS;YACT,WAAW,KAAK,IAAI,CAAC,EAAE;YACvB,aAAa,QAAQ,SAAS;YAC9B,cAAc;YACd,UAAU;YACV,WAAW;YACX,WAAW;YACX,WAAW;QACb,GACC,MAAM,CAAC,CAAC;;;;;;;MAOT,CAAC,EACA,MAAM;QAET,OAAO;YAAE,MAAM;YAAS;QAAM;IAChC,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAKO,eAAe,mBACpB,MAAc,EACd,UAAqB;IAGrB,IAAI;QACF,QAAQ,GAAG,CAAC,mCAAmC;YAAE;YAAQ;QAAW;QAEpE,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,QAAQ,GAAG,CAAC;YACZ,OAAO;gBAAE,OAAO;YAAoB;QACtC;QAEA,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,iBACL,MAAM,CAAC;YAAE,SAAS,IAAI,OAAO,WAAW;QAAG,GAC3C,EAAE,CAAC,WAAW,QACd,GAAG,CAAC,aAAa,KAAK,IAAI,CAAC,EAAE,EAC7B,EAAE,CAAC,WAAW;QAEjB,IAAI,YAAY;YACd,QAAQ,GAAG,CAAC,iDAAiD;YAE7D,gCAAgC;YAChC,MAAM,WAAW,MAAM,OAAO,CAAC,cAAc,aAAa;gBAAC;aAAW;YACtE,QAAQ,GAAG,CAAC,qCAAqC;YAEjD,+CAA+C;YAC/C,MAAM,WAAW,SAAS,MAAM,CAAC,CAAA,KAC/B,OAAO,OAAO,YACd,kEAAkE,IAAI,CAAC;YAEzE,QAAQ,GAAG,CAAC,oCAAoC;YAEhD,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,QAAQ,MAAM,EAAE,CAAC,MAAM;YACzB;QACF;QAEA,QAAQ,GAAG,CAAC;QACZ,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM;QACxB,QAAQ,GAAG,CAAC,qCAAqC;YAAE;QAAM;QACzD,OAAO;YAAE;QAAM;IACjB,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC,oCAAoC;QAChD,OAAO;YAAE;QAAM;IACjB;AACF;AAKO,eAAe,iBAAiB,MAAc;IAEnD,IAAI;QACF,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAClD,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,OAAO;gBAAE,OAAO;YAAoB;QACtC;QAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,qBACL,MAAM,CAAC;YAAE,cAAc,IAAI,OAAO,WAAW;QAAG,GAChD,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,WAAW,KAAK,IAAI,CAAC,EAAE;QAE7B,OAAO;YAAE;QAAM;IACjB,EAAE,OAAO,OAAO;QACd,OAAO;YAAE;QAAM;IACjB;AACF;AAKO,eAAe,yBACpB,qBAA6B,EAC7B,mBAA4B,IAAI,EAChC,qBAA8B,IAAI;IAGlC,IAAI;QACF,0BAA0B;QAC1B,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,QAAQ,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC1D,IAAI,CAAC,yBACL,MAAM,CAAC,CAAC;;;;;;;;;MAST,CAAC,EACA,EAAE,CAAC,MAAM,uBACT,MAAM;QAET,IAAI,YAAY,CAAC,aAAa;YAC5B,OAAO;gBAAE,MAAM;gBAAM,OAAO,YAAY;YAAwB;QAClE;QAEA,MAAM,aAAa,YAAY,SAAS,CAAC,WAAW;QACpD,MAAM,eAAe,YAAY,aAAa;QAE9C,mCAAmC;QACnC,MAAM,CAAA,GAAA,iIAAA,CAAA,kCAA+B,AAAD,EAClC,YACA,cACA,uBACA,kBACA;QAGF,mBAAmB;QACnB,OAAO,MAAM,kCAAkC;IACjD,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAKO,eAAe,mBACpB,OAAe,EACf,mBAA4B,IAAI,EAChC,qBAA8B,IAAI;IAGlC,IAAI;QACF,QAAQ,GAAG,CAAC,2CAA2C;QACvD,oBAAoB;QACpB,MAAM,EAAE,MAAM,KAAK,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACtD,IAAI,CAAC,iBACL,MAAM,CAAC,yCACP,EAAE,CAAC,MAAM,SACT,MAAM;QAET,IAAI,cAAc,CAAC,OAAO;YACxB,QAAQ,GAAG,CAAC,oCAAoC;YAChD,OAAO;gBAAE,MAAM;gBAAM,OAAO,cAAc;YAAkB;QAC9D;QAEA,QAAQ,GAAG,CAAC,oCAAoC;QAEhD,mCAAmC;QACnC,QAAQ,GAAG,CAAC;QACZ,MAAM,CAAA,GAAA,iIAAA,CAAA,4BAAyB,AAAD,EAC5B,MAAM,WAAW,EACjB,MAAM,aAAa,EACnB,SACA,kBACA;QAGF,mBAAmB;QACnB,QAAQ,GAAG,CAAC;QACZ,MAAM,SAAS,MAAM,4BAA4B;QACjD,QAAQ,GAAG,CAAC,+BAA+B;QAC3C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC,oCAAoC;QAChD,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAKO,eAAe,YAAY,MAAc;IAE9C,IAAI;QACF,QAAQ,GAAG,CAAC,8BAA8B;QAE1C,gCAAgC;QAChC,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACpD,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,QAAQ,GAAG,CAAC,2BAA2B;YAAE;YAAM;QAAU;QAEzD,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAU;QACxC;QAEA,uCAAuC;QACvC,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7C,IAAI,CAAC,YACL,MAAM,CAAC,mCACP,EAAE,CAAC,MAAM,KAAK,WAAW,EACzB,MAAM;QAET,MAAM,EAAE,MAAM,iBAAiB,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC/C,IAAI,CAAC,YACL,MAAM,CAAC,mCACP,EAAE,CAAC,MAAM,KAAK,aAAa,EAC3B,MAAM;QAET,QAAQ,GAAG,CAAC,0BAA0B;YAAE;YAAiB;QAAkB;QAE3E,mBAAmB;QACnB,MAAM,eAAe;YACnB,GAAG,IAAI;YACP,kBAAkB;YAClB,oBAAoB;QACtB;QAEA,QAAQ,GAAG,CAAC,4BAA4B;QACxC,OAAO;YAAE,MAAM;YAAc,OAAO;QAAK;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC,6BAA6B;QACzC,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 3560, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/chat/ChatList.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Skeleton } from '@/components/ui/skeleton';\nimport { MessageCircle, Users, Clock, CheckCircle2, Sparkles } from 'lucide-react';\nimport { ChatRoom as ChatRoomType } from '@/lib/chat';\nimport { getUserChatRooms } from '@/lib/chat';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface ChatListProps {\n  onSelectRoom: (room: ChatRoomType) => void;\n}\n\nexport function ChatList({ onSelectRoom }: ChatListProps) {\n  const { user } = useAuth();\n  const [rooms, setRooms] = useState<ChatRoomType[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    if (user) {\n      loadChatRooms();\n    }\n  }, [user]);\n\n  const loadChatRooms = async () => {\n    setLoading(true);\n    try {\n      const { data, error } = await getUserChatRooms();\n      if (error) {\n        console.error('Error loading chat rooms:', error);\n      } else {\n        setRooms(data || []);\n      }\n    } catch (error) {\n      console.error('Error loading chat rooms:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getInitials = (name: string | null | undefined) => {\n    if (!name) return '?';\n    return name\n      .split(' ')\n      .map(word => word.charAt(0))\n      .join('')\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  const formatLastMessageTime = (timestamp: string | null) => {\n    if (!timestamp) return '';\n    \n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n    \n    if (diffInHours < 24) {\n      return date.toLocaleTimeString('sr-RS', {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else {\n      return date.toLocaleDateString('sr-RS', {\n        day: 'numeric',\n        month: 'short'\n      });\n    }\n  };\n\n  const getOtherParticipant = (room: ChatRoomType) => {\n    if (!user) return null;\n    const userType = user.user_metadata?.user_type || 'influencer';\n    \n    if (userType === 'business') {\n      return room.influencer_profile;\n    } else {\n      return room.business_profile;\n    }\n  };\n\n  const getRoomTypeLabel = (roomType: string) => {\n    switch (roomType) {\n      case 'campaign_application':\n        return 'Kampanja';\n      case 'direct_offer':\n        return 'Direktna ponuda';\n      default:\n        return 'Chat';\n    }\n  };\n\n  if (loading) {\n    return (\n      <Card className=\"h-full\">\n        <CardHeader className=\"bg-gradient-to-r from-blue-50 to-purple-50 border-b\">\n          <CardTitle className=\"flex items-center gap-2\">\n            <div className=\"p-2 bg-blue-100 rounded-lg\">\n              <MessageCircle className=\"h-5 w-5 text-blue-600\" />\n            </div>\n            Poruke\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            {[1, 2, 3, 4].map((i) => (\n              <div key={i} className=\"flex items-center gap-3 p-3 rounded-lg border\">\n                <Skeleton className=\"h-12 w-12 rounded-full\" />\n                <div className=\"flex-1 space-y-2\">\n                  <div className=\"flex items-center justify-between\">\n                    <Skeleton className=\"h-4 w-32\" />\n                    <Skeleton className=\"h-3 w-12\" />\n                  </div>\n                  <Skeleton className=\"h-3 w-24\" />\n                  <Skeleton className=\"h-3 w-full\" />\n                </div>\n              </div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  if (rooms.length === 0) {\n    return (\n      <Card className=\"h-full\">\n        <CardHeader className=\"bg-gradient-to-r from-blue-50 to-purple-50 border-b\">\n          <CardTitle className=\"flex items-center gap-2\">\n            <div className=\"p-2 bg-blue-100 rounded-lg\">\n              <MessageCircle className=\"h-5 w-5 text-blue-600\" />\n            </div>\n            Poruke\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"flex-1 flex items-center justify-center\">\n          <div className=\"text-center text-muted-foreground py-12\">\n            <div className=\"relative mb-6\">\n              <div className=\"w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto\">\n                <Users className=\"h-10 w-10 text-blue-500\" />\n              </div>\n              <div className=\"absolute -top-1 -right-1 w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center\">\n                <Sparkles className=\"h-3 w-3 text-yellow-600\" />\n              </div>\n            </div>\n            <h3 className=\"font-semibold text-gray-900 mb-2\">Nemate aktivne razgovore</h3>\n            <p className=\"text-sm mb-4\">Razgovori će se pojaviti kada prihvatite ponude ili aplikacije.</p>\n            <div className=\"flex items-center justify-center gap-4 text-xs\">\n              <Badge variant=\"secondary\" className=\"bg-blue-50 text-blue-700 border-blue-200\">\n                <CheckCircle2 className=\"h-3 w-3\" />\n                Sigurno\n              </Badge>\n              <Badge variant=\"secondary\" className=\"bg-green-50 text-green-700 border-green-200\">\n                <Clock className=\"h-3 w-3\" />\n                Trenutno\n              </Badge>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card className=\"h-full\">\n      <CardHeader className=\"bg-gradient-to-r from-blue-50 to-purple-50 border-b\">\n        <CardTitle className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-2\">\n            <div className=\"p-2 bg-blue-100 rounded-lg\">\n              <MessageCircle className=\"h-5 w-5 text-blue-600\" />\n            </div>\n            Poruke\n          </div>\n          <Badge variant=\"secondary\" className=\"bg-blue-100 text-blue-800\">\n            {rooms.length}\n          </Badge>\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"flex-1 overflow-y-auto\">\n        <div className=\"space-y-1\">\n          {rooms.map((room) => {\n            const otherParticipant = getOtherParticipant(room);\n            const hasUnread = room.unread_count && room.unread_count > 0;\n\n            return (\n              <Button\n                key={room.id}\n                variant=\"ghost\"\n                className={`w-full justify-start p-4 h-auto transition-all duration-200 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 hover:shadow-md rounded-lg ${\n                  hasUnread ? 'bg-blue-50/50 border border-blue-200' : ''\n                }`}\n                onClick={() => onSelectRoom(room)}\n              >\n                <div className=\"flex items-center gap-3 w-full\">\n                  <div className=\"relative\">\n                    <Avatar className=\"h-12 w-12 ring-2 ring-white shadow-sm\">\n                      <AvatarImage src={otherParticipant?.avatar_url || ''} />\n                      <AvatarFallback className=\"bg-gradient-to-br from-blue-500 to-purple-500 text-white\">\n                        {otherParticipant ? getInitials(otherParticipant.full_name || otherParticipant.username) : '?'}\n                      </AvatarFallback>\n                    </Avatar>\n                    {hasUnread && (\n                      <div className=\"absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center\">\n                        <span className=\"text-xs font-bold text-white\">{room.unread_count}</span>\n                      </div>\n                    )}\n                  </div>\n\n                  <div className=\"flex-1 text-left\">\n                    <div className=\"flex items-center justify-between\">\n                      <h4 className={`font-medium ${hasUnread ? 'font-semibold text-gray-900' : 'text-gray-700'}`}>\n                        {otherParticipant?.full_name || otherParticipant?.username || 'Nepoznato'}\n                      </h4>\n                      <div className=\"flex items-center gap-2\">\n                        <span className=\"text-xs text-muted-foreground\">\n                          {formatLastMessageTime(room.last_message_at)}\n                        </span>\n                      </div>\n                    </div>\n\n                    <div className=\"flex items-center justify-between mt-1\">\n                      <p className=\"text-sm text-muted-foreground\">\n                        @{otherParticipant?.username || 'nepoznato'}\n                      </p>\n                      <Badge\n                        variant=\"outline\"\n                        className={`text-xs ${\n                          room.room_type === 'campaign_application'\n                            ? 'border-green-200 bg-green-50 text-green-700'\n                            : 'border-blue-200 bg-blue-50 text-blue-700'\n                        }`}\n                      >\n                        {getRoomTypeLabel(room.room_type)}\n                      </Badge>\n                    </div>\n\n                    <p className={`text-sm mt-1 truncate ${hasUnread ? 'font-medium text-gray-800' : 'text-muted-foreground'}`}>\n                      {room.room_title}\n                    </p>\n                  </div>\n                </div>\n              </Button>\n            );\n          })}\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAXA;;;;;;;;;;;AAiBO,SAAS,SAAS,EAAE,YAAY,EAAiB;IACtD,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,gBAAgB;QACpB,WAAW;QACX,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,kHAAA,CAAA,mBAAgB,AAAD;YAC7C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,6BAA6B;YAC7C,OAAO;gBACL,SAAS,QAAQ,EAAE;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,wBAAwB,CAAC;QAC7B,IAAI,CAAC,WAAW,OAAO;QAEvB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;QAEtE,IAAI,cAAc,IAAI;YACpB,OAAO,KAAK,kBAAkB,CAAC,SAAS;gBACtC,MAAM;gBACN,QAAQ;YACV;QACF,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC,SAAS;gBACtC,KAAK;gBACL,OAAO;YACT;QACF;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,CAAC,MAAM,OAAO;QAClB,MAAM,WAAW,KAAK,aAAa,EAAE,aAAa;QAElD,IAAI,aAAa,YAAY;YAC3B,OAAO,KAAK,kBAAkB;QAChC,OAAO;YACL,OAAO,KAAK,gBAAgB;QAC9B;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;4BACrB;;;;;;;;;;;;8BAIV,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,kBACjB,8OAAC;gCAAY,WAAU;;kDACrB,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;0DAEtB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;+BARd;;;;;;;;;;;;;;;;;;;;;IAgBtB;IAEA,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;4BACrB;;;;;;;;;;;;8BAIV,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGxB,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAe;;;;;;0CAC5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;0DACnC,8OAAC,qNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGtC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;0DACnC,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQ3C;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;gCACrB;;;;;;;sCAGR,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAY,WAAU;sCAClC,MAAM,MAAM;;;;;;;;;;;;;;;;;0BAInB,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC;wBACV,MAAM,mBAAmB,oBAAoB;wBAC7C,MAAM,YAAY,KAAK,YAAY,IAAI,KAAK,YAAY,GAAG;wBAE3D,qBACE,8OAAC,kIAAA,CAAA,SAAM;4BAEL,SAAQ;4BACR,WAAW,CAAC,oJAAoJ,EAC9J,YAAY,yCAAyC,IACrD;4BACF,SAAS,IAAM,aAAa;sCAE5B,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,8OAAC,kIAAA,CAAA,cAAW;wDAAC,KAAK,kBAAkB,cAAc;;;;;;kEAClD,8OAAC,kIAAA,CAAA,iBAAc;wDAAC,WAAU;kEACvB,mBAAmB,YAAY,iBAAiB,SAAS,IAAI,iBAAiB,QAAQ,IAAI;;;;;;;;;;;;4CAG9F,2BACC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAgC,KAAK,YAAY;;;;;;;;;;;;;;;;;kDAKvE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAW,CAAC,YAAY,EAAE,YAAY,gCAAgC,iBAAiB;kEACxF,kBAAkB,aAAa,kBAAkB,YAAY;;;;;;kEAEhE,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEACb,sBAAsB,KAAK,eAAe;;;;;;;;;;;;;;;;;0DAKjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;;4DAAgC;4DACzC,kBAAkB,YAAY;;;;;;;kEAElC,8OAAC,iIAAA,CAAA,QAAK;wDACJ,SAAQ;wDACR,WAAW,CAAC,QAAQ,EAClB,KAAK,SAAS,KAAK,yBACf,gDACA,4CACJ;kEAED,iBAAiB,KAAK,SAAS;;;;;;;;;;;;0DAIpC,8OAAC;gDAAE,WAAW,CAAC,sBAAsB,EAAE,YAAY,8BAA8B,yBAAyB;0DACvG,KAAK,UAAU;;;;;;;;;;;;;;;;;;2BAnDjB,KAAK,EAAE;;;;;oBAyDlB;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 4157, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4181, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/chat/ChatContextBar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { \n  ExternalLink, \n  Calendar, \n  DollarSign, \n  Target,\n  Building2,\n  User\n} from 'lucide-react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { supabase } from '@/lib/supabase';\nimport Link from 'next/link';\n\ninterface ChatContextBarProps {\n  campaignApplicationId?: string | null;\n  offerId?: string | null;\n}\n\ninterface CampaignData {\n  id: string;\n  title: string;\n  budget: number;\n  deadline: string | null;\n  status: string;\n  company_name: string;\n}\n\ninterface OfferData {\n  id: string;\n  title: string;\n  budget: number;\n  deadline: string | null;\n  status: string;\n  influencer_name: string;\n  business_name: string;\n}\n\nexport function ChatContextBar({ campaignApplicationId, offerId }: ChatContextBarProps) {\n  const { user } = useAuth();\n  const [campaignData, setCampaignData] = useState<CampaignData | null>(null);\n  const [offerData, setOfferData] = useState<OfferData | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    loadContextData();\n  }, [campaignApplicationId, offerId]);\n\n  const loadContextData = async () => {\n    setLoading(true);\n    try {\n      if (campaignApplicationId) {\n        await loadCampaignData();\n      } else if (offerId) {\n        await loadOfferData();\n      }\n    } catch (error) {\n      console.error('Error loading context data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadCampaignData = async () => {\n    if (!campaignApplicationId) return;\n\n    try {\n      const { data: application, error } = await supabase\n        .from('campaign_applications')\n        .select(`\n          id,\n          status,\n          campaigns (\n            id,\n            title,\n            budget,\n            deadline,\n            status,\n            businesses (\n              company_name\n            )\n          )\n        `)\n        .eq('id', campaignApplicationId)\n        .single();\n\n      if (error) {\n        console.error('Error loading campaign data:', error);\n        return;\n      }\n\n      if (application?.campaigns) {\n        setCampaignData({\n          id: application.campaigns.id,\n          title: application.campaigns.title,\n          budget: application.campaigns.budget,\n          deadline: application.campaigns.deadline,\n          status: application.status,\n          company_name: application.campaigns.businesses?.company_name || 'Nepoznato'\n        });\n      }\n    } catch (error) {\n      console.error('Error loading campaign data:', error);\n    }\n  };\n\n  const loadOfferData = async () => {\n    if (!offerId) return;\n\n    try {\n      // Get offer data with business info\n      const { data: offer, error } = await supabase\n        .from('direct_offers')\n        .select(`\n          id,\n          title,\n          budget,\n          deadline,\n          status,\n          influencer_id,\n          businesses (\n            company_name\n          )\n        `)\n        .eq('id', offerId)\n        .single();\n\n      if (error) {\n        console.error('Error loading offer data:', error);\n        return;\n      }\n\n      // Get influencer profile separately\n      const { data: influencerProfile } = await supabase\n        .from('profiles')\n        .select('username, full_name')\n        .eq('id', offer.influencer_id)\n        .single();\n\n      if (offer) {\n        setOfferData({\n          id: offer.id,\n          title: offer.title,\n          budget: offer.budget,\n          deadline: offer.deadline,\n          status: offer.status || 'pending',\n          business_name: offer.businesses?.company_name || 'Nepoznato',\n          influencer_name: influencerProfile?.full_name || influencerProfile?.username || 'Nepoznato'\n        });\n      }\n    } catch (error) {\n      console.error('Error loading offer data:', error);\n    }\n  };\n\n  const getContextLink = () => {\n    if (!user) return '#';\n    \n    const userType = user.user_metadata?.user_type || 'influencer';\n    \n    if (campaignApplicationId) {\n      if (userType === 'business') {\n        return `/dashboard/biznis/applications/${campaignApplicationId}`;\n      } else {\n        return `/dashboard/influencer/applications/${campaignApplicationId}`;\n      }\n    } else if (offerId) {\n      if (userType === 'business') {\n        return `/dashboard/biznis/offers/${offerId}`;\n      } else {\n        return `/dashboard/influencer/offers/${offerId}`;\n      }\n    }\n    \n    return '#';\n  };\n\n  const getContextTitle = () => {\n    if (campaignData) return campaignData.title;\n    if (offerData) return offerData.title;\n    if (campaignApplicationId) return 'Kampanja';\n    if (offerId) return 'Direktna ponuda';\n    return 'Razgovor';\n  };\n\n  const getContextType = () => {\n    if (campaignApplicationId) return 'Kampanja';\n    if (offerId) return 'Direktna ponuda';\n    return 'Razgovor';\n  };\n\n  const getBudget = () => {\n    if (campaignData) return campaignData.budget;\n    if (offerData) return offerData.budget;\n    return null;\n  };\n\n  const getDeadline = () => {\n    if (campaignData) return campaignData.deadline;\n    if (offerData) return offerData.deadline;\n    return null;\n  };\n\n  const getStatus = () => {\n    if (campaignData) return campaignData.status;\n    if (offerData) return offerData.status;\n    return null;\n  };\n\n  const getStatusBadge = (status: string | null) => {\n    if (!status) return null;\n    \n    switch (status) {\n      case 'active':\n      case 'accepted':\n        return <Badge variant=\"default\" className=\"bg-green-500\">{status === 'active' ? 'Aktivna' : 'Prihvaćeno'}</Badge>;\n      case 'pending':\n        return <Badge variant=\"secondary\">Na čekanju</Badge>;\n      case 'rejected':\n        return <Badge variant=\"destructive\">Odbačeno</Badge>;\n      case 'completed':\n        return <Badge variant=\"outline\" className=\"border-green-500 text-green-700\">Završeno</Badge>;\n      default:\n        return <Badge variant=\"outline\">{status}</Badge>;\n    }\n  };\n\n  if (loading) {\n    return (\n      <Card className=\"mb-4\">\n        <CardContent className=\"p-3\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-900\"></div>\n              <span className=\"text-sm text-muted-foreground\">Učitava kontekst...</span>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card className=\"mb-4 border-l-4 border-l-primary\">\n      <CardContent className=\"p-3\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"flex items-center gap-2\">\n              {campaignApplicationId ? (\n                <Target className=\"h-4 w-4 text-primary\" />\n              ) : (\n                <Building2 className=\"h-4 w-4 text-primary\" />\n              )}\n              <div>\n                <div className=\"flex items-center gap-2\">\n                  <span className=\"font-medium text-sm\">{getContextTitle()}</span>\n                  {getStatusBadge(getStatus())}\n                </div>\n                <div className=\"flex items-center gap-4 text-xs text-muted-foreground mt-1\">\n                  <span className=\"flex items-center gap-1\">\n                    <span className=\"font-medium\">{getContextType()}</span>\n                  </span>\n                  {getBudget() && (\n                    <span className=\"flex items-center gap-1\">\n                      <DollarSign className=\"h-3 w-3\" />\n                      {getBudget()?.toLocaleString()} KM\n                    </span>\n                  )}\n                  {getDeadline() && (\n                    <span className=\"flex items-center gap-1\">\n                      <Calendar className=\"h-3 w-3\" />\n                      {new Date(getDeadline()!).toLocaleDateString()}\n                    </span>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n          \n          <Button variant=\"outline\" size=\"sm\" asChild>\n            <Link href={getContextLink()}>\n              <ExternalLink className=\"h-3 w-3 mr-1\" />\n              Detalji\n            </Link>\n          </Button>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;AAhBA;;;;;;;;;;AA0CO,SAAS,eAAe,EAAE,qBAAqB,EAAE,OAAO,EAAuB;IACpF,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAuB;KAAQ;IAEnC,MAAM,kBAAkB;QACtB,WAAW;QACX,IAAI;YACF,IAAI,uBAAuB;gBACzB,MAAM;YACR,OAAO,IAAI,SAAS;gBAClB,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,uBAAuB;QAE5B,IAAI;YACF,MAAM,EAAE,MAAM,WAAW,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAChD,IAAI,CAAC,yBACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;QAaT,CAAC,EACA,EAAE,CAAC,MAAM,uBACT,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C;YACF;YAEA,IAAI,aAAa,WAAW;gBAC1B,gBAAgB;oBACd,IAAI,YAAY,SAAS,CAAC,EAAE;oBAC5B,OAAO,YAAY,SAAS,CAAC,KAAK;oBAClC,QAAQ,YAAY,SAAS,CAAC,MAAM;oBACpC,UAAU,YAAY,SAAS,CAAC,QAAQ;oBACxC,QAAQ,YAAY,MAAM;oBAC1B,cAAc,YAAY,SAAS,CAAC,UAAU,EAAE,gBAAgB;gBAClE;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,SAAS;QAEd,IAAI;YACF,oCAAoC;YACpC,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC1C,IAAI,CAAC,iBACL,MAAM,CAAC,CAAC;;;;;;;;;;QAUT,CAAC,EACA,EAAE,CAAC,MAAM,SACT,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C;YACF;YAEA,oCAAoC;YACpC,MAAM,EAAE,MAAM,iBAAiB,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC/C,IAAI,CAAC,YACL,MAAM,CAAC,uBACP,EAAE,CAAC,MAAM,MAAM,aAAa,EAC5B,MAAM;YAET,IAAI,OAAO;gBACT,aAAa;oBACX,IAAI,MAAM,EAAE;oBACZ,OAAO,MAAM,KAAK;oBAClB,QAAQ,MAAM,MAAM;oBACpB,UAAU,MAAM,QAAQ;oBACxB,QAAQ,MAAM,MAAM,IAAI;oBACxB,eAAe,MAAM,UAAU,EAAE,gBAAgB;oBACjD,iBAAiB,mBAAmB,aAAa,mBAAmB,YAAY;gBAClF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,WAAW,KAAK,aAAa,EAAE,aAAa;QAElD,IAAI,uBAAuB;YACzB,IAAI,aAAa,YAAY;gBAC3B,OAAO,CAAC,+BAA+B,EAAE,uBAAuB;YAClE,OAAO;gBACL,OAAO,CAAC,mCAAmC,EAAE,uBAAuB;YACtE;QACF,OAAO,IAAI,SAAS;YAClB,IAAI,aAAa,YAAY;gBAC3B,OAAO,CAAC,yBAAyB,EAAE,SAAS;YAC9C,OAAO;gBACL,OAAO,CAAC,6BAA6B,EAAE,SAAS;YAClD;QACF;QAEA,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,IAAI,cAAc,OAAO,aAAa,KAAK;QAC3C,IAAI,WAAW,OAAO,UAAU,KAAK;QACrC,IAAI,uBAAuB,OAAO;QAClC,IAAI,SAAS,OAAO;QACpB,OAAO;IACT;IAEA,MAAM,iBAAiB;QACrB,IAAI,uBAAuB,OAAO;QAClC,IAAI,SAAS,OAAO;QACpB,OAAO;IACT;IAEA,MAAM,YAAY;QAChB,IAAI,cAAc,OAAO,aAAa,MAAM;QAC5C,IAAI,WAAW,OAAO,UAAU,MAAM;QACtC,OAAO;IACT;IAEA,MAAM,cAAc;QAClB,IAAI,cAAc,OAAO,aAAa,QAAQ;QAC9C,IAAI,WAAW,OAAO,UAAU,QAAQ;QACxC,OAAO;IACT;IAEA,MAAM,YAAY;QAChB,IAAI,cAAc,OAAO,aAAa,MAAM;QAC5C,IAAI,WAAW,OAAO,UAAU,MAAM;QACtC,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,QAAQ,OAAO;QAEpB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAAgB,WAAW,WAAW,YAAY;;;;;;YAC9F,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAY;;;;;;YACpC,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAc;;;;;;YACtC,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAAkC;;;;;;YAC9E;gBACE,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAW;;;;;;QACrC;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM5D;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,sCACC,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;yDAElB,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CAEvB,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAuB;;;;;;gDACtC,eAAe;;;;;;;sDAElB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DACd,cAAA,8OAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;gDAEhC,6BACC,8OAAC;oDAAK,WAAU;;sEACd,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDACrB,aAAa;wDAAiB;;;;;;;gDAGlC,+BACC,8OAAC;oDAAK,WAAU;;sEACd,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDACnB,IAAI,KAAK,eAAgB,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQxD,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;wBAAK,OAAO;kCACzC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAM;;8CACV,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD", "debugId": null}}, {"offset": {"line": 4616, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/chat/ChatRoom.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport { Badge } from '@/components/ui/badge';\nimport { Skeleton } from '@/components/ui/skeleton';\nimport { Send, ArrowLeft, Clock, CheckCircle2, User, MessageCircle } from 'lucide-react';\nimport { ChatMessage, ChatRoom as ChatRoomType } from '@/lib/chat';\nimport { getChatMessages, sendChatMessage, markMessagesAsRead } from '@/lib/chat';\nimport { supabase } from '@/lib/supabase';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { ChatContextBar } from './ChatContextBar';\n\ninterface ChatRoomProps {\n  room: ChatRoomType;\n  onBack: () => void;\n}\n\nexport function ChatRoom({ room, onBack }: ChatRoomProps) {\n  const { user } = useAuth();\n  const [messages, setMessages] = useState<ChatMessage[]>([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [sending, setSending] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  useEffect(() => {\n    loadMessages();\n    \n    // Subscribe to new messages\n    const channel = supabase\n      .channel(`chat_room_${room.id}`)\n      .on(\n        'postgres_changes',\n        {\n          event: 'INSERT',\n          schema: 'public',\n          table: 'chat_messages',\n          filter: `room_id=eq.${room.id}`,\n        },\n        (payload) => {\n          const newMessage = payload.new as ChatMessage;\n          setMessages(prev => [...prev, newMessage]);\n        }\n      )\n      .subscribe();\n\n    return () => {\n      supabase.removeChannel(channel);\n    };\n  }, [room.id]);\n\n  const loadMessages = async () => {\n    setLoading(true);\n    try {\n      const { data, error } = await getChatMessages(room.id);\n      if (error) {\n        console.error('Error loading messages:', error);\n      } else {\n        setMessages(data || []);\n        // Mark unread messages as read (messages not sent by current user)\n        if (user && data) {\n          const unreadMessageIds = data\n            .filter(msg => msg.sender_id !== user.id && !msg.read_at)\n            .map(msg => msg.id);\n\n          if (unreadMessageIds.length > 0) {\n            await markMessagesAsRead(room.id, unreadMessageIds);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Error loading messages:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSendMessage = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!newMessage.trim() || !user || sending) return;\n\n    setSending(true);\n    try {\n      const userType = user.user_metadata?.user_type || 'influencer';\n      const { data, error } = await sendChatMessage(\n        room.id,\n        newMessage.trim()\n      );\n\n      if (error) {\n        console.error('Error sending message:', error);\n      } else {\n        setNewMessage('');\n      }\n    } catch (error) {\n      console.error('Error sending message:', error);\n    } finally {\n      setSending(false);\n    }\n  };\n\n  const getInitials = (name: string | null | undefined) => {\n    if (!name) return '?';\n    return name\n      .split(' ')\n      .map(word => word.charAt(0))\n      .join('')\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  const formatTime = (timestamp: string) => {\n    return new Date(timestamp).toLocaleTimeString('sr-RS', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const formatDate = (timestamp: string) => {\n    return new Date(timestamp).toLocaleDateString('sr-RS', {\n      day: 'numeric',\n      month: 'long',\n      year: 'numeric'\n    });\n  };\n\n  const isMyMessage = (message: ChatMessage) => {\n    return user && message.sender_id === user.id;\n  };\n\n  const getOtherParticipant = () => {\n    if (!user) return null;\n    const userType = user.user_metadata?.user_type || 'influencer';\n    \n    if (userType === 'business') {\n      return room.influencer_profile;\n    } else {\n      return room.business_profile;\n    }\n  };\n\n  const otherParticipant = getOtherParticipant();\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      {/* Context Bar */}\n      <ChatContextBar\n        campaignApplicationId={room.campaign_application_id}\n        offerId={room.offer_id}\n      />\n\n      <Card className=\"flex-1 flex flex-col shadow-lg\">\n        {/* Enhanced Header */}\n        <CardHeader className=\"flex-shrink-0 border-b bg-gradient-to-r from-blue-50 to-purple-50\">\n          <div className=\"flex items-center gap-3\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={onBack}\n              className=\"p-2 hover:bg-white/80 rounded-full\"\n            >\n              <ArrowLeft className=\"h-4 w-4\" />\n            </Button>\n\n            <div className=\"relative\">\n              <Avatar className=\"h-12 w-12 ring-2 ring-white shadow-md\">\n                <AvatarImage src={otherParticipant?.avatar_url || ''} />\n                <AvatarFallback className=\"bg-gradient-to-br from-blue-500 to-purple-500 text-white\">\n                  {otherParticipant ? getInitials(otherParticipant.full_name || otherParticipant.username) : '?'}\n                </AvatarFallback>\n              </Avatar>\n              <div className=\"absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white\"></div>\n            </div>\n\n            <div className=\"flex-1\">\n              <CardTitle className=\"text-lg text-gray-900\">\n                {otherParticipant?.full_name || otherParticipant?.username || 'Nepoznato'}\n              </CardTitle>\n              <div className=\"flex items-center gap-2\">\n                <p className=\"text-sm text-muted-foreground\">\n                  @{otherParticipant?.username || 'nepoznato'}\n                </p>\n                <div className=\"flex items-center gap-1 text-xs text-green-600\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                  <span>Online</span>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex items-center gap-2\">\n              <Badge\n                variant=\"secondary\"\n                className={`${\n                  room.room_type === 'campaign_application'\n                    ? 'bg-green-100 text-green-800 border-green-200'\n                    : 'bg-blue-100 text-blue-800 border-blue-200'\n                }`}\n              >\n                <MessageCircle className=\"h-3 w-3 mr-1\" />\n                {room.room_type === 'campaign_application' ? 'Kampanja' : 'Direktna ponuda'}\n              </Badge>\n            </div>\n          </div>\n        </CardHeader>\n\n      <CardContent className=\"flex-1 flex flex-col\">\n        {/* Enhanced Messages */}\n        <div className=\"flex-1 overflow-y-auto space-y-4 bg-gradient-to-b from-gray-50/30 to-white rounded-lg p-4\">\n          {loading ? (\n            <div className=\"space-y-4\">\n              {[1, 2, 3, 4].map((i) => (\n                <div key={i} className={`flex ${i % 2 === 0 ? 'justify-end' : 'justify-start'}`}>\n                  <div className=\"max-w-[70%] space-y-2\">\n                    <Skeleton className=\"h-12 w-48 rounded-lg\" />\n                    <Skeleton className=\"h-3 w-16\" />\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : messages.length === 0 ? (\n            <div className=\"flex-1 flex items-center justify-center\">\n              <div className=\"text-center text-muted-foreground py-12\">\n                <div className=\"w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <MessageCircle className=\"h-8 w-8 text-blue-500\" />\n                </div>\n                <h3 className=\"font-semibold text-gray-900 mb-2\">Počnite razgovor</h3>\n                <p className=\"text-sm\">Pošaljite prvu poruku da započnete komunikaciju!</p>\n              </div>\n            </div>\n          ) : (\n            messages.map((message, index) => {\n              const showDate = index === 0 ||\n                formatDate(message.created_at || '') !== formatDate(messages[index - 1]?.created_at || '');\n\n              return (\n                <div key={message.id}>\n                  {showDate && (\n                    <div className=\"text-center text-xs text-muted-foreground mb-4\">\n                      <div className=\"bg-white/80 backdrop-blur-sm px-3 py-1 rounded-full inline-block border\">\n                        {formatDate(message.created_at || '')}\n                      </div>\n                    </div>\n                  )}\n\n                  <div className={`flex ${isMyMessage(message) ? 'justify-end' : 'justify-start'}`}>\n                    <div className={`max-w-[70%] ${isMyMessage(message) ? 'order-2' : 'order-1'}`}>\n                      <div\n                        className={`rounded-2xl px-4 py-3 shadow-sm ${\n                          isMyMessage(message)\n                            ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'\n                            : 'bg-white border border-gray-200'\n                        }`}\n                      >\n                        <p className=\"text-sm leading-relaxed\">{message.message_text}</p>\n                      </div>\n                      <div className={`flex items-center gap-1 mt-1 ${\n                        isMyMessage(message) ? 'justify-end' : 'justify-start'\n                      }`}>\n                        <Clock className=\"h-3 w-3 text-muted-foreground\" />\n                        <p className=\"text-xs text-muted-foreground\">\n                          {formatTime(message.created_at || '')}\n                        </p>\n                        {isMyMessage(message) && (\n                          <CheckCircle2 className=\"h-3 w-3 text-blue-500\" />\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              );\n            })\n          )}\n          <div ref={messagesEndRef} />\n        </div>\n\n        {/* Enhanced Message Input */}\n        <div className=\"border-t bg-white rounded-b-lg p-4\">\n          <form onSubmit={handleSendMessage} className=\"flex gap-3\">\n            <Input\n              value={newMessage}\n              onChange={(e) => setNewMessage(e.target.value)}\n              placeholder=\"Napišite poruku...\"\n              disabled={sending}\n              className=\"flex-1 rounded-full border-gray-300 focus:border-blue-500 focus:ring-blue-500\"\n            />\n            <Button\n              type=\"submit\"\n              disabled={!newMessage.trim() || sending}\n              className=\"rounded-full w-12 h-12 p-0 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 shadow-lg\"\n            >\n              {sending ? (\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n              ) : (\n                <Send className=\"h-4 w-4\" />\n              )}\n            </Button>\n          </form>\n        </div>\n      </CardContent>\n    </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;;;AAqBO,SAAS,SAAS,EAAE,IAAI,EAAE,MAAM,EAAiB;IACtD,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QAEA,4BAA4B;QAC5B,MAAM,UAAU,sHAAA,CAAA,WAAQ,CACrB,OAAO,CAAC,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE,EAC9B,EAAE,CACD,oBACA;YACE,OAAO;YACP,QAAQ;YACR,OAAO;YACP,QAAQ,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE;QACjC,GACA,CAAC;YACC,MAAM,aAAa,QAAQ,GAAG;YAC9B,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAW;QAC3C,GAED,SAAS;QAEZ,OAAO;YACL,sHAAA,CAAA,WAAQ,CAAC,aAAa,CAAC;QACzB;IACF,GAAG;QAAC,KAAK,EAAE;KAAC;IAEZ,MAAM,eAAe;QACnB,WAAW;QACX,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,kHAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,EAAE;YACrD,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,2BAA2B;YAC3C,OAAO;gBACL,YAAY,QAAQ,EAAE;gBACtB,mEAAmE;gBACnE,IAAI,QAAQ,MAAM;oBAChB,MAAM,mBAAmB,KACtB,MAAM,CAAC,CAAA,MAAO,IAAI,SAAS,KAAK,KAAK,EAAE,IAAI,CAAC,IAAI,OAAO,EACvD,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;oBAEpB,IAAI,iBAAiB,MAAM,GAAG,GAAG;wBAC/B,MAAM,CAAA,GAAA,kHAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,EAAE,EAAE;oBACpC;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,EAAE,cAAc;QAChB,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,QAAQ,SAAS;QAE5C,WAAW;QACX,IAAI;YACF,MAAM,WAAW,KAAK,aAAa,EAAE,aAAa;YAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,kHAAA,CAAA,kBAAe,AAAD,EAC1C,KAAK,EAAE,EACP,WAAW,IAAI;YAGjB,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0BAA0B;YAC1C,OAAO;gBACL,cAAc;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,WAAW,kBAAkB,CAAC,SAAS;YACrD,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,WAAW,kBAAkB,CAAC,SAAS;YACrD,KAAK;YACL,OAAO;YACP,MAAM;QACR;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,QAAQ,QAAQ,SAAS,KAAK,KAAK,EAAE;IAC9C;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,MAAM,OAAO;QAClB,MAAM,WAAW,KAAK,aAAa,EAAE,aAAa;QAElD,IAAI,aAAa,YAAY;YAC3B,OAAO,KAAK,kBAAkB;QAChC,OAAO;YACL,OAAO,KAAK,gBAAgB;QAC9B;IACF;IAEA,MAAM,mBAAmB;IAEzB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,4IAAA,CAAA,iBAAc;gBACb,uBAAuB,KAAK,uBAAuB;gBACnD,SAAS,KAAK,QAAQ;;;;;;0BAGxB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCAEd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAGvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAK,kBAAkB,cAAc;;;;;;8DAClD,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB,mBAAmB,YAAY,iBAAiB,SAAS,IAAI,iBAAiB,QAAQ,IAAI;;;;;;;;;;;;sDAG/F,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAGjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,kBAAkB,aAAa,kBAAkB,YAAY;;;;;;sDAEhE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;;wDAAgC;wDACzC,kBAAkB,YAAY;;;;;;;8DAElC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;8CAKZ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wCACJ,SAAQ;wCACR,WAAW,GACT,KAAK,SAAS,KAAK,yBACf,iDACA,6CACJ;;0DAEF,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;4CACxB,KAAK,SAAS,KAAK,yBAAyB,aAAa;;;;;;;;;;;;;;;;;;;;;;;kCAMpE,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,8OAAC;gCAAI,WAAU;;oCACZ,wBACC,8OAAC;wCAAI,WAAU;kDACZ;4CAAC;4CAAG;4CAAG;4CAAG;yCAAE,CAAC,GAAG,CAAC,CAAC,kBACjB,8OAAC;gDAAY,WAAW,CAAC,KAAK,EAAE,IAAI,MAAM,IAAI,gBAAgB,iBAAiB;0DAC7E,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oIAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC,oIAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;;+CAHd;;;;;;;;;+CAQZ,SAAS,MAAM,KAAK,kBACtB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;;;;;;8DAE3B,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;;;;;;+CAI3B,SAAS,GAAG,CAAC,CAAC,SAAS;wCACrB,MAAM,WAAW,UAAU,KACzB,WAAW,QAAQ,UAAU,IAAI,QAAQ,WAAW,QAAQ,CAAC,QAAQ,EAAE,EAAE,cAAc;wCAEzF,qBACE,8OAAC;;gDACE,0BACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACZ,WAAW,QAAQ,UAAU,IAAI;;;;;;;;;;;8DAKxC,8OAAC;oDAAI,WAAW,CAAC,KAAK,EAAE,YAAY,WAAW,gBAAgB,iBAAiB;8DAC9E,cAAA,8OAAC;wDAAI,WAAW,CAAC,YAAY,EAAE,YAAY,WAAW,YAAY,WAAW;;0EAC3E,8OAAC;gEACC,WAAW,CAAC,gCAAgC,EAC1C,YAAY,WACR,4DACA,mCACJ;0EAEF,cAAA,8OAAC;oEAAE,WAAU;8EAA2B,QAAQ,YAAY;;;;;;;;;;;0EAE9D,8OAAC;gEAAI,WAAW,CAAC,6BAA6B,EAC5C,YAAY,WAAW,gBAAgB,iBACvC;;kFACA,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,8OAAC;wEAAE,WAAU;kFACV,WAAW,QAAQ,UAAU,IAAI;;;;;;oEAEnC,YAAY,0BACX,8OAAC,qNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;2CA5BxB,QAAQ,EAAE;;;;;oCAmCxB;kDAEF,8OAAC;wCAAI,KAAK;;;;;;;;;;;;0CAIZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,UAAU;oCAAmB,WAAU;;sDAC3C,8OAAC,iIAAA,CAAA,QAAK;4CACJ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,aAAY;4CACZ,UAAU;4CACV,WAAU;;;;;;sDAEZ,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,UAAU,CAAC,WAAW,IAAI,MAAM;4CAChC,WAAU;sDAET,wBACC,8OAAC;gDAAI,WAAU;;;;;qEAEf,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShC", "debugId": null}}, {"offset": {"line": 5196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/chat/Chat.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { ChatList } from './ChatList';\nimport { ChatRoom } from './ChatRoom';\nimport { ChatRoom as ChatRoomType, getChatRoom } from '@/lib/chat';\nimport { Skeleton } from '@/components/ui/skeleton';\nimport { Card, CardContent, CardHeader } from '@/components/ui/card';\n\ninterface ChatProps {\n  initialRoomId?: string;\n}\n\nexport function Chat({ initialRoomId }: ChatProps) {\n  const [selectedRoom, setSelectedRoom] = useState<ChatRoomType | null>(null);\n  const [loading, setLoading] = useState(false);\n\n  useEffect(() => {\n    if (initialRoomId) {\n      loadRoom(initialRoomId);\n    }\n  }, [initialRoomId]);\n\n  const loadRoom = async (roomId: string) => {\n    setLoading(true);\n    try {\n      const { data: room, error } = await getChatRoom(roomId);\n      if (room && !error) {\n        setSelectedRoom(room);\n      }\n    } catch (error) {\n      console.error('Error loading room:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSelectRoom = (room: ChatRoomType) => {\n    setSelectedRoom(room);\n  };\n\n  const handleBackToList = () => {\n    setSelectedRoom(null);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"h-[600px] w-full\">\n        <Card className=\"h-full\">\n          <CardHeader className=\"space-y-2\">\n            <Skeleton className=\"h-6 w-32\" />\n            <Skeleton className=\"h-4 w-48\" />\n          </CardHeader>\n          <CardContent>\n            {/* Chat room skeleton */}\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center gap-3\">\n                <Skeleton className=\"h-10 w-10 rounded-full\" />\n                <div className=\"flex-1 space-y-2\">\n                  <Skeleton className=\"h-4 w-32\" />\n                  <Skeleton className=\"h-3 w-24\" />\n                </div>\n                <Skeleton className=\"h-6 w-16\" />\n              </div>\n              <div className=\"space-y-3\">\n                {[1, 2, 3, 4].map((i) => (\n                  <div key={i} className=\"flex items-start gap-3\">\n                    <Skeleton className=\"h-8 w-8 rounded-full\" />\n                    <div className=\"flex-1 space-y-2\">\n                      <Skeleton className=\"h-4 w-full\" />\n                      <Skeleton className=\"h-4 w-3/4\" />\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-[600px] w-full\">\n      <div className=\"h-full rounded-lg overflow-hidden shadow-lg border\">\n        {selectedRoom ? (\n          <ChatRoom room={selectedRoom} onBack={handleBackToList} />\n        ) : (\n          <ChatList onSelectRoom={handleSelectRoom} />\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAaO,SAAS,KAAK,EAAE,aAAa,EAAa;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe;YACjB,SAAS;QACX;IACF,GAAG;QAAC;KAAc;IAElB,MAAM,WAAW,OAAO;QACtB,WAAW;QACX,IAAI;YACF,MAAM,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,kHAAA,CAAA,cAAW,AAAD,EAAE;YAChD,IAAI,QAAQ,CAAC,OAAO;gBAClB,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;IAClB;IAEA,MAAM,mBAAmB;QACvB,gBAAgB;IAClB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;kCAEtB,8OAAC,gIAAA,CAAA,cAAW;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAEtB,8OAAC,oIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,8OAAC;oCAAI,WAAU;8CACZ;wCAAC;wCAAG;wCAAG;wCAAG;qCAAE,CAAC,GAAG,CAAC,CAAC,kBACjB,8OAAC;4CAAY,WAAU;;8DACrB,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oIAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC,oIAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;;;2CAJd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAc1B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACZ,6BACC,8OAAC,sIAAA,CAAA,WAAQ;gBAAC,MAAM;gBAAc,QAAQ;;;;;qCAEtC,8OAAC,sIAAA,CAAA,WAAQ;gBAAC,cAAc;;;;;;;;;;;;;;;;AAKlC", "debugId": null}}, {"offset": {"line": 5429, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/app/dashboard/chat/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useSearchParams } from 'next/navigation';\nimport { DashboardLayout } from '@/components/dashboard/DashboardLayout';\nimport { Chat } from '@/components/chat/Chat';\nimport { MessageCircle, Users, Sparkles } from 'lucide-react';\n\nexport default function ChatPage() {\n  const searchParams = useSearchParams();\n  const roomId = searchParams.get('room');\n  return (\n    <DashboardLayout>\n      <div className=\"space-y-6\">\n        {/* Enhanced Header */}\n        <div className=\"relative overflow-hidden rounded-lg bg-gradient-to-r from-green-600 to-blue-600 p-6 text-white\">\n          <div className=\"relative z-10\">\n            <div className=\"flex items-center gap-3 mb-2\">\n              <div className=\"p-2 bg-white/20 rounded-lg\">\n                <MessageCircle className=\"h-6 w-6\" />\n              </div>\n              <h1 className=\"text-3xl font-bold tracking-tight\">Poruke</h1>\n            </div>\n            <p className=\"text-green-100 mb-4\">\n              Komunicirajte sa partnerima o kampanjama i ponudama\n            </p>\n            <div className=\"flex items-center gap-4\">\n              <div className=\"flex items-center gap-2 text-sm bg-white/20 px-3 py-1 rounded-full\">\n                <Users className=\"h-4 w-4\" />\n                <span>Sigurna komunikacija</span>\n              </div>\n              <div className=\"flex items-center gap-2 text-sm bg-white/20 px-3 py-1 rounded-full\">\n                <Sparkles className=\"h-4 w-4\" />\n                <span>Trenutne poruke</span>\n              </div>\n            </div>\n          </div>\n          <div className=\"absolute top-0 right-0 -mt-4 -mr-4 h-24 w-24 rounded-full bg-white/10\" />\n          <div className=\"absolute bottom-0 left-0 -mb-8 -ml-8 h-32 w-32 rounded-full bg-white/5\" />\n        </div>\n\n        <Chat initialRoomId={roomId || undefined} />\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,SAAS,aAAa,GAAG,CAAC;IAChC,qBACE,8OAAC,kJAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;sDAE3B,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;;;;;;;8CAEpD,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;8CAGnC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAIZ,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAGjB,8OAAC,kIAAA,CAAA,OAAI;oBAAC,eAAe,UAAU;;;;;;;;;;;;;;;;;AAIvC", "debugId": null}}]}