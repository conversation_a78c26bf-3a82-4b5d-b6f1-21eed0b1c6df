{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAG7B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;;AAGb,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,6JAAA,CAAA,aAAgB,OAGrC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;;;AAGb,eAAe,WAAW,GAAG,qKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/marketplace.ts"], "sourcesContent": ["import { supabase } from './supabase';\n\n// Tipovi za marketplace\nexport interface SearchFilters {\n  search?: string;\n  categories?: number[];\n  platforms?: number[];\n  contentTypes?: number[];\n  minPrice?: number;\n  maxPrice?: number;\n  minFollowers?: number;\n  maxFollowers?: number;\n  location?: string;\n  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';\n  minAge?: number;\n  maxAge?: number;\n  verifiedOnly?: boolean;\n  sortBy?: 'relevance' | 'price_asc' | 'price_desc' | 'followers_desc' | 'newest';\n  limit?: number;\n  offset?: number;\n}\n\nexport interface InfluencerSearchResult {\n  id: string;\n  username: string;\n  full_name: string;\n  avatar_url: string;\n  bio: string;\n  location: string;\n  gender: string;\n  age: number;\n  is_verified: boolean;\n  categories: string[];\n  platforms: Array<{\n    platform_id: number;\n    platform_name: string;\n    platform_icon: string;\n    handle: string;\n    followers_count: number;\n    is_verified: boolean;\n  }>;\n  pricing: Array<{\n    platform_id: number;\n    platform_name: string;\n    content_type_id: number;\n    content_type_name: string;\n    price: number;\n    currency: string;\n  }>;\n  min_price: number;\n  max_price: number;\n  total_followers: number;\n  relevance_score: number;\n}\n\nexport interface PublicInfluencerProfile {\n  id: string;\n  username: string;\n  full_name: string;\n  avatar_url: string;\n  bio: string;\n  location: string;\n  gender: string;\n  age: number;\n  is_verified: boolean;\n  categories: Array<{\n    id: number;\n    name: string;\n    icon: string;\n    is_primary: boolean;\n  }>;\n  platforms: Array<{\n    platform_id: number;\n    platform_name: string;\n    platform_icon: string;\n    handle: string;\n    followers_count: number;\n    is_verified: boolean;\n  }>;\n  pricing: Array<{\n    platform_id: number;\n    platform_name: string;\n    content_type_id: number;\n    content_type_name: string;\n    price: number;\n    currency: string;\n  }>;\n  portfolio_urls: string[];\n  total_followers: number;\n  created_at: string;\n}\n\n/**\n * Pretraga influencera sa filterima\n */\nexport async function searchInfluencers(filters: SearchFilters = {}) {\n  try {\n    // Build the query using RPC function for better performance\n    let query = supabase\n      .rpc('get_influencers_with_details', {\n        search_term: filters.search || '',\n        min_followers: filters.minFollowers || 0,\n        max_followers: filters.maxFollowers || 999999999,\n        min_price: filters.minPrice || 0,\n        max_price: filters.maxPrice || 999999,\n        platform_filter: filters.platform || '',\n        category_filter: filters.category || '',\n        location_filter: filters.location || '',\n        limit_count: 12\n      });\n\n    const { data: profiles, error } = await query;\n\n    if (error) {\n      console.error('Supabase query error:', error);\n      throw error;\n    }\n\n    console.log('Raw profiles data:', profiles);\n    console.log('Profiles count:', profiles?.length || 0);\n\n    // Transform data to match InfluencerSearchResult interface\n    const influencers: InfluencerSearchResult[] = (profiles || [])\n      .map(profile => {\n        const totalFollowers = (profile.instagram_followers || 0) +\n                             (profile.tiktok_followers || 0) +\n                             (profile.youtube_subscribers || 0);\n\n      // Create platforms array based on available data\n      const platforms = [];\n      if (profile.instagram_followers > 0) {\n        platforms.push({\n          platform_id: 1,\n          platform_name: 'Instagram',\n          platform_icon: '📷',\n          handle: `@${profile.username}`,\n          followers_count: profile.instagram_followers,\n          is_verified: profile.is_verified\n        });\n      }\n      if (profile.tiktok_followers > 0) {\n        platforms.push({\n          platform_id: 2,\n          platform_name: 'TikTok',\n          platform_icon: '🎵',\n          handle: `@${profile.username}`,\n          followers_count: profile.tiktok_followers,\n          is_verified: false\n        });\n      }\n      if (profile.youtube_subscribers > 0) {\n        platforms.push({\n          platform_id: 3,\n          platform_name: 'YouTube',\n          platform_icon: '📺',\n          handle: `@${profile.username}`,\n          followers_count: profile.youtube_subscribers,\n          is_verified: false\n        });\n      }\n\n      // Create pricing array\n      const pricing = [];\n      if (profile.price_per_post) {\n        pricing.push({\n          platform_id: 1,\n          platform_name: 'Instagram',\n          content_type_id: 1,\n          content_type_name: 'Post',\n          price: Number(profile.price_per_post),\n          currency: 'KM'\n        });\n      }\n      if (profile.price_per_story) {\n        pricing.push({\n          platform_id: 1,\n          platform_name: 'Instagram',\n          content_type_id: 2,\n          content_type_name: 'Story',\n          price: Number(profile.price_per_story),\n          currency: 'KM'\n        });\n      }\n      if (profile.price_per_reel) {\n        pricing.push({\n          platform_id: 1,\n          platform_name: 'Instagram',\n          content_type_id: 3,\n          content_type_name: 'Reel',\n          price: Number(profile.price_per_reel),\n          currency: 'KM'\n        });\n      }\n\n      const minPrice = pricing.length > 0 ? Math.min(...pricing.map(p => p.price)) : 0;\n      const maxPrice = pricing.length > 0 ? Math.max(...pricing.map(p => p.price)) : 0;\n\n      return {\n        id: profile.id,\n        username: profile.username || '',\n        full_name: profile.full_name || profile.username || '',\n        avatar_url: profile.avatar_url || '',\n        bio: profile.bio || '',\n        location: profile.location || '',\n        gender: profile.gender || 'prefer_not_to_say',\n        age: profile.age || 0,\n        is_verified: profile.is_verified || false,\n        categories: [], // TODO: Add categories when implemented\n        platforms,\n        pricing,\n        min_price: minPrice,\n        max_price: maxPrice,\n        total_followers: totalFollowers,\n        relevance_score: 1.0\n      };\n    });\n\n    return { data: influencers, error: null };\n  } catch (error) {\n    console.error('Error in searchInfluencers:', error);\n    return { data: null, error };\n  }\n}\n\n/**\n * Dobijanje javnog profila influencera po username-u\n */\nexport async function getPublicInfluencerProfile(username: string) {\n  try {\n    // TODO: Zameniti sa pravim RPC pozivom kada se kreira get_public_influencer_profile funkcija\n    // const { data, error } = await supabase.rpc('get_public_influencer_profile', { influencer_username: username });\n\n    // Mock profili za testiranje\n    const mockProfiles: { [key: string]: PublicInfluencerProfile } = {\n      'ana_fitness': {\n        id: '1',\n        username: 'ana_fitness',\n        full_name: 'Ana Marković',\n        avatar_url: '',\n        bio: 'Fitness trener i nutricionista sa 5+ godina iskustva. Pomagam ljudima da postignu svoje ciljeve kroz zdrav način života, pravilnu ishranu i redovne vežbe. Specijalizovana sam za weight loss transformacije i muscle building programe.',\n        location: 'Sarajevo, BiH',\n        gender: 'female',\n        age: 28,\n        is_verified: true,\n        categories: ['Fitness', 'Zdravlje', 'Lifestyle'],\n        platforms: [\n          {\n            platform_id: 1,\n            platform_name: 'Instagram',\n            platform_icon: '📷',\n            handle: '@ana_fitness',\n            followers_count: 15000,\n            is_verified: true\n          },\n          {\n            platform_id: 3,\n            platform_name: 'TikTok',\n            platform_icon: '🎵',\n            handle: '@ana_fitness_tiktok',\n            followers_count: 8500,\n            is_verified: false\n          }\n        ],\n        pricing: [\n          {\n            platform_id: 1,\n            platform_name: 'Instagram',\n            platform_icon: '📷',\n            content_type_id: 1,\n            content_type_name: 'Post',\n            price: 150,\n            currency: 'KM'\n          },\n          {\n            platform_id: 1,\n            platform_name: 'Instagram',\n            platform_icon: '📷',\n            content_type_id: 2,\n            content_type_name: 'Story',\n            price: 80,\n            currency: 'KM'\n          },\n          {\n            platform_id: 1,\n            platform_name: 'Instagram',\n            platform_icon: '📷',\n            content_type_id: 3,\n            content_type_name: 'Reel',\n            price: 200,\n            currency: 'KM'\n          },\n          {\n            platform_id: 3,\n            platform_name: 'TikTok',\n            platform_icon: '🎵',\n            content_type_id: 6,\n            content_type_name: 'Video',\n            price: 120,\n            currency: 'KM'\n          }\n        ],\n        min_price: 80,\n        max_price: 200,\n        total_followers: 23500,\n        portfolio_items: [\n          {\n            id: '1',\n            platform_name: 'Instagram',\n            content_type: 'Post',\n            media_url: '',\n            description: 'Workout routine za početnice',\n            engagement_rate: 4.2,\n            created_at: '2024-01-15'\n          }\n        ]\n      },\n      'marko_tech': {\n        id: '2',\n        username: 'marko_tech',\n        full_name: 'Marko Petrović',\n        avatar_url: '',\n        bio: 'Tech reviewer i programer. Testiram najnovije gadgete, aplikacije i tehnologije. Radim kao senior developer i dijelim svoje znanje kroz video sadržaj. Specijalizovan za mobile development i AI tehnologije.',\n        location: 'Banja Luka, BiH',\n        gender: 'male',\n        age: 32,\n        is_verified: false,\n        categories: ['Tehnologija', 'Gaming', 'Edukacija'],\n        platforms: [\n          {\n            platform_id: 2,\n            platform_name: 'YouTube',\n            platform_icon: '📺',\n            handle: '@marko_tech',\n            followers_count: 8500,\n            is_verified: false\n          },\n          {\n            platform_id: 1,\n            platform_name: 'Instagram',\n            platform_icon: '📷',\n            handle: '@marko.tech.reviews',\n            followers_count: 5200,\n            is_verified: false\n          }\n        ],\n        pricing: [\n          {\n            platform_id: 2,\n            platform_name: 'YouTube',\n            platform_icon: '📺',\n            content_type_id: 4,\n            content_type_name: 'Video',\n            price: 300,\n            currency: 'KM'\n          },\n          {\n            platform_id: 2,\n            platform_name: 'YouTube',\n            platform_icon: '📺',\n            content_type_id: 5,\n            content_type_name: 'Short',\n            price: 150,\n            currency: 'KM'\n          },\n          {\n            platform_id: 1,\n            platform_name: 'Instagram',\n            platform_icon: '📷',\n            content_type_id: 1,\n            content_type_name: 'Post',\n            price: 100,\n            currency: 'KM'\n          }\n        ],\n        min_price: 100,\n        max_price: 300,\n        total_followers: 13700,\n        portfolio_items: [\n          {\n            id: '2',\n            platform_name: 'YouTube',\n            content_type: 'Video',\n            media_url: '',\n            description: 'iPhone 15 Pro Max review',\n            engagement_rate: 6.8,\n            created_at: '2024-01-10'\n          }\n        ]\n      }\n    };\n\n    const profile = mockProfiles[username];\n\n    if (!profile) {\n      return { data: null, error: { message: 'Profile not found' } };\n    }\n\n    return { data: profile, error: null };\n  } catch (error) {\n    console.error('Error in getPublicInfluencerProfile:', error);\n    return { data: null, error };\n  }\n}\n\n/**\n * Dobijanje svih kategorija za filter\n */\nexport async function getCategories() {\n  try {\n    // TODO: Zameniti sa pravim pozivom kada se kreira categories tabela\n    // const { data, error } = await supabase.from('categories').select('id, name, slug, icon').order('name');\n\n    // Mock kategorije\n    const mockCategories = [\n      { id: 1, name: 'Fitness', slug: 'fitness', icon: '💪' },\n      { id: 2, name: 'Moda', slug: 'moda', icon: '👗' },\n      { id: 3, name: 'Tehnologija', slug: 'tehnologija', icon: '📱' },\n      { id: 4, name: 'Hrana', slug: 'hrana', icon: '🍕' },\n      { id: 5, name: 'Putovanja', slug: 'putovanja', icon: '✈️' },\n      { id: 6, name: 'Gaming', slug: 'gaming', icon: '🎮' },\n      { id: 7, name: 'Ljepota', slug: 'ljepota', icon: '💄' },\n      { id: 8, name: 'Zdravlje', slug: 'zdravlje', icon: '🏥' }\n    ];\n\n    return { data: mockCategories, error: null };\n  } catch (error) {\n    console.error('Error in getCategories:', error);\n    return { data: null, error };\n  }\n}\n\n/**\n * Dobijanje svih platformi za filter\n */\nexport async function getPlatforms() {\n  try {\n    // TODO: Zameniti sa pravim pozivom kada se kreira platforms tabela\n    // const { data, error } = await supabase.from('platforms').select('id, name, slug, icon').eq('is_active', true).order('name');\n\n    // Mock platforme\n    const mockPlatforms = [\n      { id: 1, name: 'Instagram', slug: 'instagram', icon: '📷' },\n      { id: 2, name: 'YouTube', slug: 'youtube', icon: '📺' },\n      { id: 3, name: 'TikTok', slug: 'tiktok', icon: '🎵' },\n      { id: 4, name: 'Facebook', slug: 'facebook', icon: '📘' },\n      { id: 5, name: 'Twitter', slug: 'twitter', icon: '🐦' },\n      { id: 6, name: 'LinkedIn', slug: 'linkedin', icon: '💼' }\n    ];\n\n    return { data: mockPlatforms, error: null };\n  } catch (error) {\n    console.error('Error in getPlatforms:', error);\n    return { data: null, error };\n  }\n}\n\n/**\n * Dobijanje content tipova za određene platforme\n */\nexport async function getContentTypes(platformIds?: number[]) {\n  try {\n    // TODO: Zameniti sa pravim pozivom kada se kreira content_types tabela\n\n    // Mock content tipovi\n    const mockContentTypes = [\n      { id: 1, platform_id: 1, name: 'Post', slug: 'post', description: 'Obična objava', platforms: { name: 'Instagram', icon: '📷' } },\n      { id: 2, platform_id: 1, name: 'Story', slug: 'story', description: 'Instagram story', platforms: { name: 'Instagram', icon: '📷' } },\n      { id: 3, platform_id: 1, name: 'Reel', slug: 'reel', description: 'Instagram reel', platforms: { name: 'Instagram', icon: '📷' } },\n      { id: 4, platform_id: 2, name: 'Video', slug: 'video', description: 'YouTube video', platforms: { name: 'YouTube', icon: '📺' } },\n      { id: 5, platform_id: 2, name: 'Short', slug: 'short', description: 'YouTube short', platforms: { name: 'YouTube', icon: '📺' } },\n      { id: 6, platform_id: 3, name: 'Video', slug: 'tiktok-video', description: 'TikTok video', platforms: { name: 'TikTok', icon: '🎵' } }\n    ];\n\n    let filteredContentTypes = mockContentTypes;\n    if (platformIds && platformIds.length > 0) {\n      filteredContentTypes = mockContentTypes.filter(ct => platformIds.includes(ct.platform_id));\n    }\n\n    return { data: filteredContentTypes, error: null };\n  } catch (error) {\n    console.error('Error in getContentTypes:', error);\n    return { data: null, error };\n  }\n}\n\n/**\n * Refresh materialized view (admin funkcija)\n */\nexport async function refreshSearchView() {\n  try {\n    const { data, error } = await supabase.rpc('refresh_influencer_search_view');\n\n    if (error) {\n      console.error('Error refreshing search view:', error);\n      return { data: null, error };\n    }\n\n    return { data, error: null };\n  } catch (error) {\n    console.error('Error in refreshSearchView:', error);\n    return { data: null, error };\n  }\n}\n\n/**\n * Dobijanje statistika za marketplace (broj influencera, kategorija, itd.)\n */\nexport async function getMarketplaceStats() {\n  try {\n    // Broj influencera\n    const { count: influencersCount } = await supabase\n      .from('influencer_search_view')\n      .select('*', { count: 'exact', head: true });\n\n    // Broj kategorija\n    const { count: categoriesCount } = await supabase\n      .from('categories')\n      .select('*', { count: 'exact', head: true });\n\n    // Broj platformi\n    const { count: platformsCount } = await supabase\n      .from('platforms')\n      .select('*', { count: 'exact', head: true })\n      .eq('is_active', true);\n\n    // Prosečna cijena\n    const { data: avgPriceData } = await supabase\n      .from('influencer_platform_pricing')\n      .select('price')\n      .eq('is_available', true);\n\n    const avgPrice = avgPriceData && avgPriceData.length > 0\n      ? avgPriceData.reduce((sum, item) => sum + (item.price || 0), 0) / avgPriceData.length\n      : 0;\n\n    return {\n      data: {\n        influencersCount: influencersCount || 0,\n        categoriesCount: categoriesCount || 0,\n        platformsCount: platformsCount || 0,\n        averagePrice: Math.round(avgPrice)\n      },\n      error: null\n    };\n  } catch (error) {\n    console.error('Error in getMarketplaceStats:', error);\n    return { data: null, error };\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AA+FO,eAAe;QAAkB,UAAA,iEAAyB,CAAC;IAChE,IAAI;QACF,4DAA4D;QAC5D,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,GAAG,CAAC,gCAAgC;YACnC,aAAa,QAAQ,MAAM,IAAI;YAC/B,eAAe,QAAQ,YAAY,IAAI;YACvC,eAAe,QAAQ,YAAY,IAAI;YACvC,WAAW,QAAQ,QAAQ,IAAI;YAC/B,WAAW,QAAQ,QAAQ,IAAI;YAC/B,iBAAiB,QAAQ,QAAQ,IAAI;YACrC,iBAAiB,QAAQ,QAAQ,IAAI;YACrC,iBAAiB,QAAQ,QAAQ,IAAI;YACrC,aAAa;QACf;QAEF,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM;QAExC,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;QAEA,QAAQ,GAAG,CAAC,sBAAsB;QAClC,QAAQ,GAAG,CAAC,mBAAmB,CAAA,qBAAA,+BAAA,SAAU,MAAM,KAAI;QAEnD,2DAA2D;QAC3D,MAAM,cAAwC,CAAC,YAAY,EAAE,EAC1D,GAAG,CAAC,CAAA;YACH,MAAM,iBAAiB,CAAC,QAAQ,mBAAmB,IAAI,CAAC,IACnC,CAAC,QAAQ,gBAAgB,IAAI,CAAC,IAC9B,CAAC,QAAQ,mBAAmB,IAAI,CAAC;YAExD,iDAAiD;YACjD,MAAM,YAAY,EAAE;YACpB,IAAI,QAAQ,mBAAmB,GAAG,GAAG;gBACnC,UAAU,IAAI,CAAC;oBACb,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,QAAQ,AAAC,IAAoB,OAAjB,QAAQ,QAAQ;oBAC5B,iBAAiB,QAAQ,mBAAmB;oBAC5C,aAAa,QAAQ,WAAW;gBAClC;YACF;YACA,IAAI,QAAQ,gBAAgB,GAAG,GAAG;gBAChC,UAAU,IAAI,CAAC;oBACb,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,QAAQ,AAAC,IAAoB,OAAjB,QAAQ,QAAQ;oBAC5B,iBAAiB,QAAQ,gBAAgB;oBACzC,aAAa;gBACf;YACF;YACA,IAAI,QAAQ,mBAAmB,GAAG,GAAG;gBACnC,UAAU,IAAI,CAAC;oBACb,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,QAAQ,AAAC,IAAoB,OAAjB,QAAQ,QAAQ;oBAC5B,iBAAiB,QAAQ,mBAAmB;oBAC5C,aAAa;gBACf;YACF;YAEA,uBAAuB;YACvB,MAAM,UAAU,EAAE;YAClB,IAAI,QAAQ,cAAc,EAAE;gBAC1B,QAAQ,IAAI,CAAC;oBACX,aAAa;oBACb,eAAe;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,OAAO,OAAO,QAAQ,cAAc;oBACpC,UAAU;gBACZ;YACF;YACA,IAAI,QAAQ,eAAe,EAAE;gBAC3B,QAAQ,IAAI,CAAC;oBACX,aAAa;oBACb,eAAe;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,OAAO,OAAO,QAAQ,eAAe;oBACrC,UAAU;gBACZ;YACF;YACA,IAAI,QAAQ,cAAc,EAAE;gBAC1B,QAAQ,IAAI,CAAC;oBACX,aAAa;oBACb,eAAe;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,OAAO,OAAO,QAAQ,cAAc;oBACpC,UAAU;gBACZ;YACF;YAEA,MAAM,WAAW,QAAQ,MAAM,GAAG,IAAI,KAAK,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;YAC/E,MAAM,WAAW,QAAQ,MAAM,GAAG,IAAI,KAAK,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;YAE/E,OAAO;gBACL,IAAI,QAAQ,EAAE;gBACd,UAAU,QAAQ,QAAQ,IAAI;gBAC9B,WAAW,QAAQ,SAAS,IAAI,QAAQ,QAAQ,IAAI;gBACpD,YAAY,QAAQ,UAAU,IAAI;gBAClC,KAAK,QAAQ,GAAG,IAAI;gBACpB,UAAU,QAAQ,QAAQ,IAAI;gBAC9B,QAAQ,QAAQ,MAAM,IAAI;gBAC1B,KAAK,QAAQ,GAAG,IAAI;gBACpB,aAAa,QAAQ,WAAW,IAAI;gBACpC,YAAY,EAAE;gBACd;gBACA;gBACA,WAAW;gBACX,WAAW;gBACX,iBAAiB;gBACjB,iBAAiB;YACnB;QACF;QAEA,OAAO;YAAE,MAAM;YAAa,OAAO;QAAK;IAC1C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAKO,eAAe,2BAA2B,QAAgB;IAC/D,IAAI;QACF,6FAA6F;QAC7F,kHAAkH;QAElH,6BAA6B;QAC7B,MAAM,eAA2D;YAC/D,eAAe;gBACb,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,YAAY;gBACZ,KAAK;gBACL,UAAU;gBACV,QAAQ;gBACR,KAAK;gBACL,aAAa;gBACb,YAAY;oBAAC;oBAAW;oBAAY;iBAAY;gBAChD,WAAW;oBACT;wBACE,aAAa;wBACb,eAAe;wBACf,eAAe;wBACf,QAAQ;wBACR,iBAAiB;wBACjB,aAAa;oBACf;oBACA;wBACE,aAAa;wBACb,eAAe;wBACf,eAAe;wBACf,QAAQ;wBACR,iBAAiB;wBACjB,aAAa;oBACf;iBACD;gBACD,SAAS;oBACP;wBACE,aAAa;wBACb,eAAe;wBACf,eAAe;wBACf,iBAAiB;wBACjB,mBAAmB;wBACnB,OAAO;wBACP,UAAU;oBACZ;oBACA;wBACE,aAAa;wBACb,eAAe;wBACf,eAAe;wBACf,iBAAiB;wBACjB,mBAAmB;wBACnB,OAAO;wBACP,UAAU;oBACZ;oBACA;wBACE,aAAa;wBACb,eAAe;wBACf,eAAe;wBACf,iBAAiB;wBACjB,mBAAmB;wBACnB,OAAO;wBACP,UAAU;oBACZ;oBACA;wBACE,aAAa;wBACb,eAAe;wBACf,eAAe;wBACf,iBAAiB;wBACjB,mBAAmB;wBACnB,OAAO;wBACP,UAAU;oBACZ;iBACD;gBACD,WAAW;gBACX,WAAW;gBACX,iBAAiB;gBACjB,iBAAiB;oBACf;wBACE,IAAI;wBACJ,eAAe;wBACf,cAAc;wBACd,WAAW;wBACX,aAAa;wBACb,iBAAiB;wBACjB,YAAY;oBACd;iBACD;YACH;YACA,cAAc;gBACZ,IAAI;gBACJ,UAAU;gBACV,WAAW;gBACX,YAAY;gBACZ,KAAK;gBACL,UAAU;gBACV,QAAQ;gBACR,KAAK;gBACL,aAAa;gBACb,YAAY;oBAAC;oBAAe;oBAAU;iBAAY;gBAClD,WAAW;oBACT;wBACE,aAAa;wBACb,eAAe;wBACf,eAAe;wBACf,QAAQ;wBACR,iBAAiB;wBACjB,aAAa;oBACf;oBACA;wBACE,aAAa;wBACb,eAAe;wBACf,eAAe;wBACf,QAAQ;wBACR,iBAAiB;wBACjB,aAAa;oBACf;iBACD;gBACD,SAAS;oBACP;wBACE,aAAa;wBACb,eAAe;wBACf,eAAe;wBACf,iBAAiB;wBACjB,mBAAmB;wBACnB,OAAO;wBACP,UAAU;oBACZ;oBACA;wBACE,aAAa;wBACb,eAAe;wBACf,eAAe;wBACf,iBAAiB;wBACjB,mBAAmB;wBACnB,OAAO;wBACP,UAAU;oBACZ;oBACA;wBACE,aAAa;wBACb,eAAe;wBACf,eAAe;wBACf,iBAAiB;wBACjB,mBAAmB;wBACnB,OAAO;wBACP,UAAU;oBACZ;iBACD;gBACD,WAAW;gBACX,WAAW;gBACX,iBAAiB;gBACjB,iBAAiB;oBACf;wBACE,IAAI;wBACJ,eAAe;wBACf,cAAc;wBACd,WAAW;wBACX,aAAa;wBACb,iBAAiB;wBACjB,YAAY;oBACd;iBACD;YACH;QACF;QAEA,MAAM,UAAU,YAAY,CAAC,SAAS;QAEtC,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAE,MAAM;gBAAM,OAAO;oBAAE,SAAS;gBAAoB;YAAE;QAC/D;QAEA,OAAO;YAAE,MAAM;YAAS,OAAO;QAAK;IACtC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAKO,eAAe;IACpB,IAAI;QACF,oEAAoE;QACpE,0GAA0G;QAE1G,kBAAkB;QAClB,MAAM,iBAAiB;YACrB;gBAAE,IAAI;gBAAG,MAAM;gBAAW,MAAM;gBAAW,MAAM;YAAK;YACtD;gBAAE,IAAI;gBAAG,MAAM;gBAAQ,MAAM;gBAAQ,MAAM;YAAK;YAChD;gBAAE,IAAI;gBAAG,MAAM;gBAAe,MAAM;gBAAe,MAAM;YAAK;YAC9D;gBAAE,IAAI;gBAAG,MAAM;gBAAS,MAAM;gBAAS,MAAM;YAAK;YAClD;gBAAE,IAAI;gBAAG,MAAM;gBAAa,MAAM;gBAAa,MAAM;YAAK;YAC1D;gBAAE,IAAI;gBAAG,MAAM;gBAAU,MAAM;gBAAU,MAAM;YAAK;YACpD;gBAAE,IAAI;gBAAG,MAAM;gBAAW,MAAM;gBAAW,MAAM;YAAK;YACtD;gBAAE,IAAI;gBAAG,MAAM;gBAAY,MAAM;gBAAY,MAAM;YAAK;SACzD;QAED,OAAO;YAAE,MAAM;YAAgB,OAAO;QAAK;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAKO,eAAe;IACpB,IAAI;QACF,mEAAmE;QACnE,+HAA+H;QAE/H,iBAAiB;QACjB,MAAM,gBAAgB;YACpB;gBAAE,IAAI;gBAAG,MAAM;gBAAa,MAAM;gBAAa,MAAM;YAAK;YAC1D;gBAAE,IAAI;gBAAG,MAAM;gBAAW,MAAM;gBAAW,MAAM;YAAK;YACtD;gBAAE,IAAI;gBAAG,MAAM;gBAAU,MAAM;gBAAU,MAAM;YAAK;YACpD;gBAAE,IAAI;gBAAG,MAAM;gBAAY,MAAM;gBAAY,MAAM;YAAK;YACxD;gBAAE,IAAI;gBAAG,MAAM;gBAAW,MAAM;gBAAW,MAAM;YAAK;YACtD;gBAAE,IAAI;gBAAG,MAAM;gBAAY,MAAM;gBAAY,MAAM;YAAK;SACzD;QAED,OAAO;YAAE,MAAM;YAAe,OAAO;QAAK;IAC5C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAKO,eAAe,gBAAgB,WAAsB;IAC1D,IAAI;QACF,uEAAuE;QAEvE,sBAAsB;QACtB,MAAM,mBAAmB;YACvB;gBAAE,IAAI;gBAAG,aAAa;gBAAG,MAAM;gBAAQ,MAAM;gBAAQ,aAAa;gBAAiB,WAAW;oBAAE,MAAM;oBAAa,MAAM;gBAAK;YAAE;YAChI;gBAAE,IAAI;gBAAG,aAAa;gBAAG,MAAM;gBAAS,MAAM;gBAAS,aAAa;gBAAmB,WAAW;oBAAE,MAAM;oBAAa,MAAM;gBAAK;YAAE;YACpI;gBAAE,IAAI;gBAAG,aAAa;gBAAG,MAAM;gBAAQ,MAAM;gBAAQ,aAAa;gBAAkB,WAAW;oBAAE,MAAM;oBAAa,MAAM;gBAAK;YAAE;YACjI;gBAAE,IAAI;gBAAG,aAAa;gBAAG,MAAM;gBAAS,MAAM;gBAAS,aAAa;gBAAiB,WAAW;oBAAE,MAAM;oBAAW,MAAM;gBAAK;YAAE;YAChI;gBAAE,IAAI;gBAAG,aAAa;gBAAG,MAAM;gBAAS,MAAM;gBAAS,aAAa;gBAAiB,WAAW;oBAAE,MAAM;oBAAW,MAAM;gBAAK;YAAE;YAChI;gBAAE,IAAI;gBAAG,aAAa;gBAAG,MAAM;gBAAS,MAAM;gBAAgB,aAAa;gBAAgB,WAAW;oBAAE,MAAM;oBAAU,MAAM;gBAAK;YAAE;SACtI;QAED,IAAI,uBAAuB;QAC3B,IAAI,eAAe,YAAY,MAAM,GAAG,GAAG;YACzC,uBAAuB,iBAAiB,MAAM,CAAC,CAAA,KAAM,YAAY,QAAQ,CAAC,GAAG,WAAW;QAC1F;QAEA,OAAO;YAAE,MAAM;YAAsB,OAAO;QAAK;IACnD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAKO,eAAe;IACpB,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC;QAE3C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;gBAAE,MAAM;gBAAM;YAAM;QAC7B;QAEA,OAAO;YAAE;YAAM,OAAO;QAAK;IAC7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF;AAKO,eAAe;IACpB,IAAI;QACF,mBAAmB;QACnB,MAAM,EAAE,OAAO,gBAAgB,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC/C,IAAI,CAAC,0BACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK;QAE5C,kBAAkB;QAClB,MAAM,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC9C,IAAI,CAAC,cACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK;QAE5C,iBAAiB;QACjB,MAAM,EAAE,OAAO,cAAc,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7C,IAAI,CAAC,aACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK,GACzC,EAAE,CAAC,aAAa;QAEnB,kBAAkB;QAClB,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC1C,IAAI,CAAC,+BACL,MAAM,CAAC,SACP,EAAE,CAAC,gBAAgB;QAEtB,MAAM,WAAW,gBAAgB,aAAa,MAAM,GAAG,IACnD,aAAa,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC,GAAG,KAAK,aAAa,MAAM,GACpF;QAEJ,OAAO;YACL,MAAM;gBACJ,kBAAkB,oBAAoB;gBACtC,iBAAiB,mBAAmB;gBACpC,gBAAgB,kBAAkB;gBAClC,cAAc,KAAK,KAAK,CAAC;YAC3B;YACA,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 976, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,6JAAA,CAAA,aAAgB,MAGpC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,6JAAA,CAAA,aAAgB,CAG3C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,6JAAA,CAAA,aAAgB,CAG7C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,6JAAA,CAAA,aAAgB,OAGpC,QAAyD;QAAxD,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO;yBACvD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,QAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1212, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Slider = React.forwardRef<\n  React.ElementRef<typeof SliderPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <SliderPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex w-full touch-none select-none items-center\",\n      className\n    )}\n    {...props}\n  >\n    <SliderPrimitive.Track className=\"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary\">\n      <SliderPrimitive.Range className=\"absolute h-full bg-primary\" />\n    </SliderPrimitive.Track>\n    <SliderPrimitive.Thumb className=\"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\" />\n  </SliderPrimitive.Root>\n))\nSlider.displayName = SliderPrimitive.Root.displayName\n\nexport { Slider }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAG7B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;0BAET,6LAAC,qKAAA,CAAA,QAAqB;gBAAC,WAAU;0BAC/B,cAAA,6LAAC,qKAAA,CAAA,QAAqB;oBAAC,WAAU;;;;;;;;;;;0BAEnC,6LAAC,qKAAA,CAAA,QAAqB;gBAAC,WAAU;;;;;;;;;;;;;;AAGrC,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1274, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,kBAAkB,sKAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,sKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,sKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,6JAAA,CAAA,aAAgB,MAGrC,QAA0C;QAAzC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO;yBACxC,6LAAC,sKAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAED,GAAG,KAAK;;;;;;;;AAGb,eAAe,WAAW,GAAG,sKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1320, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Popover({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\n}\n\nfunction PopoverTrigger({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\n}\n\nfunction PopoverContent({\n  className,\n  align = \"center\",\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\n  return (\n    <PopoverPrimitive.Portal>\n      <PopoverPrimitive.Content\n        data-slot=\"popover-content\"\n        align={align}\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\n          className\n        )}\n        {...props}\n      />\n    </PopoverPrimitive.Portal>\n  )\n}\n\nfunction PopoverAnchor({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\n}\n\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,KAEoC;QAFpC,EACf,GAAG,OACgD,GAFpC;IAGf,qBAAO,6LAAC,sKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;KAJS;AAMT,SAAS,eAAe,KAEgC;QAFhC,EACtB,GAAG,OACmD,GAFhC;IAGtB,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,KAKgC;QALhC,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD,GALhC;IAMtB,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,cAAc,KAEgC;QAFhC,EACrB,GAAG,OACkD,GAFhC;IAGrB,qBAAO,6LAAC,sKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS", "debugId": null}}, {"offset": {"line": 1405, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { Check } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\n      className\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      <Check className=\"h-4 w-4\" />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,yBAAW,6JAAA,CAAA,aAAgB,MAG/B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kTACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1460, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/marketplace/horizontal-filters.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Badge } from '@/components/ui/badge';\nimport { Slider } from '@/components/ui/slider';\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\nimport { Checkbox } from '@/components/ui/checkbox';\nimport { X, Info, ChevronDown } from 'lucide-react';\nimport { SearchFilters } from '@/lib/marketplace';\n\ninterface HorizontalFiltersProps {\n  filters: SearchFilters;\n  onFiltersChange: (filters: SearchFilters) => void;\n  onClearFilters: () => void;\n}\n\nexport default function HorizontalFilters({ filters, onFiltersChange, onClearFilters }: HorizontalFiltersProps) {\n  const [priceRange, setPriceRange] = useState([filters.min_price || 0, filters.max_price || 1000]);\n  const [ageRange, setAgeRange] = useState([filters.min_age || 18, filters.max_age || 65]);\n\n  // Mock data - ovo će biti zamenjen sa API pozivima\n  const platforms = [\n    { id: 1, name: 'Instagram', icon: '📷' },\n    { id: 2, name: 'YouTube', icon: '📺' },\n    { id: 3, name: 'TikTok', icon: '🎵' },\n    { id: 4, name: 'Facebook', icon: '📘' },\n    { id: 5, name: 'Twitter', icon: '🐦' },\n    { id: 6, name: 'LinkedIn', icon: '💼' },\n  ];\n\n  const contentTypes = [\n    { id: 1, name: 'Post', platforms: ['Instagram', 'Facebook', 'LinkedIn'] },\n    { id: 2, name: 'Story', platforms: ['Instagram', 'Facebook'] },\n    { id: 3, name: 'Reel', platforms: ['Instagram'] },\n    { id: 4, name: 'Video', platforms: ['YouTube', 'TikTok', 'Facebook'] },\n    { id: 5, name: 'Short', platforms: ['YouTube', 'TikTok'] },\n    { id: 6, name: 'Live', platforms: ['Instagram', 'YouTube', 'TikTok', 'Facebook'] },\n  ];\n\n  const categories = [\n    { id: 1, name: 'Fashion', icon: '👗' },\n    { id: 2, name: 'Beauty', icon: '💄' },\n    { id: 3, name: 'Fitness', icon: '💪' },\n    { id: 4, name: 'Food', icon: '🍕' },\n    { id: 5, name: 'Travel', icon: '✈️' },\n    { id: 6, name: 'Tech', icon: '📱' },\n    { id: 7, name: 'Lifestyle', icon: '🌟' },\n    { id: 8, name: 'Gaming', icon: '🎮' },\n  ];\n\n  const updateFilters = (key: keyof SearchFilters, value: any) => {\n    onFiltersChange({\n      ...filters,\n      [key]: value\n    });\n  };\n\n  const toggleArrayFilter = (key: keyof SearchFilters, value: string) => {\n    const currentArray = (filters[key] as string[]) || [];\n    const newArray = currentArray.includes(value)\n      ? currentArray.filter(item => item !== value)\n      : [...currentArray, value];\n    \n    updateFilters(key, newArray);\n  };\n\n  const handlePriceChange = (values: number[]) => {\n    setPriceRange(values);\n    updateFilters('min_price', values[0]);\n    updateFilters('max_price', values[1]);\n  };\n\n  const handleAgeChange = (values: number[]) => {\n    setAgeRange(values);\n    updateFilters('min_age', values[0]);\n    updateFilters('max_age', values[1]);\n  };\n\n  // Računanje broja aktivnih filtera\n  const getActiveFiltersCount = () => {\n    let count = 0;\n    if (filters.categories?.length) count += filters.categories.length;\n    if (filters.platforms?.length) count += filters.platforms.length;\n    if (filters.content_types?.length) count += filters.content_types.length;\n    if (filters.min_price !== undefined || filters.max_price !== undefined) count += 1;\n    if (filters.gender) count += 1;\n    if (filters.min_age !== undefined || filters.max_age !== undefined) count += 1;\n    return count;\n  };\n\n  const activeFiltersCount = getActiveFiltersCount();\n\n  return (\n    <TooltipProvider>\n      <div className=\"bg-card border rounded-lg p-4 mb-6\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"flex items-center gap-2\">\n            <h3 className=\"font-medium\">Gdje želite da se objavi vaš sadržaj?</h3>\n            <Tooltip>\n              <TooltipTrigger>\n                <Info className=\"h-4 w-4 text-muted-foreground\" />\n              </TooltipTrigger>\n              <TooltipContent>\n                <p>Odaberite platforme i tip sadržaja za vašu kampanju</p>\n              </TooltipContent>\n            </Tooltip>\n          </div>\n          {activeFiltersCount > 0 && (\n            <Button variant=\"ghost\" size=\"sm\" onClick={onClearFilters}>\n              <X className=\"h-4 w-4 mr-1\" />\n              Obriši sve ({activeFiltersCount})\n            </Button>\n          )}\n        </div>\n\n        {/* Filters Row */}\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3\">\n          {/* Platform Filter */}\n          <Popover>\n            <PopoverTrigger asChild>\n              <div className=\"inline-flex items-center justify-between h-9 px-3 py-2 text-sm font-medium border border-input bg-background rounded-md cursor-pointer hover:bg-accent hover:text-accent-foreground\">\n                <span>Platforma</span>\n                <div className=\"flex items-center gap-2\">\n                  {filters.platforms?.length ? (\n                    <Badge variant=\"secondary\" className=\"text-xs\">\n                      {filters.platforms.length}\n                    </Badge>\n                  ) : null}\n                  <ChevronDown className=\"h-4 w-4\" />\n                </div>\n              </div>\n            </PopoverTrigger>\n            <PopoverContent className=\"w-64\" align=\"start\">\n              <div className=\"space-y-2\">\n                <h4 className=\"font-medium text-sm\">Odaberite platforme</h4>\n                {platforms.map((platform) => (\n                  <div key={platform.id} className=\"flex items-center space-x-2\">\n                    <Checkbox\n                      id={`platform-${platform.id}`}\n                      checked={filters.platforms?.includes(platform.name) || false}\n                      onCheckedChange={() => toggleArrayFilter('platforms', platform.name)}\n                    />\n                    <label\n                      htmlFor={`platform-${platform.id}`}\n                      className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center gap-2\"\n                    >\n                      <span>{platform.icon}</span>\n                      {platform.name}\n                    </label>\n                  </div>\n                ))}\n              </div>\n            </PopoverContent>\n          </Popover>\n\n          {/* Content Type Filter */}\n          <Popover>\n            <PopoverTrigger asChild>\n              <div className=\"inline-flex items-center justify-between h-9 px-3 py-2 text-sm font-medium border border-input bg-background rounded-md cursor-pointer hover:bg-accent hover:text-accent-foreground\">\n                <div className=\"flex items-center gap-2\">\n                  <span>Tip sadržaja</span>\n                  <Tooltip>\n                    <TooltipTrigger asChild>\n                      <Info className=\"h-3 w-3 text-muted-foreground\" />\n                    </TooltipTrigger>\n                    <TooltipContent>\n                      <p>Post, Story, Reel, Video - odaberite format sadržaja</p>\n                    </TooltipContent>\n                  </Tooltip>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  {filters.content_types?.length ? (\n                    <Badge variant=\"secondary\" className=\"text-xs\">\n                      {filters.content_types.length}\n                    </Badge>\n                  ) : null}\n                  <ChevronDown className=\"h-4 w-4\" />\n                </div>\n              </div>\n            </PopoverTrigger>\n            <PopoverContent className=\"w-64\" align=\"start\">\n              <div className=\"space-y-2\">\n                <h4 className=\"font-medium text-sm\">Tip sadržaja</h4>\n                <p className=\"text-xs text-muted-foreground mb-3\">\n                  Kakav format sadržaja želite za vašu kampanju?\n                </p>\n                {contentTypes.map((contentType) => (\n                  <div key={contentType.id} className=\"flex items-center space-x-2\">\n                    <Checkbox\n                      id={`content-${contentType.id}`}\n                      checked={filters.content_types?.includes(contentType.name) || false}\n                      onCheckedChange={() => toggleArrayFilter('content_types', contentType.name)}\n                    />\n                    <label\n                      htmlFor={`content-${contentType.id}`}\n                      className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center justify-between w-full\"\n                    >\n                      <span>{contentType.name}</span>\n                      <span className=\"text-xs text-muted-foreground\">\n                        {contentType.platforms.slice(0, 2).join(', ')}\n                        {contentType.platforms.length > 2 && '...'}\n                      </span>\n                    </label>\n                  </div>\n                ))}\n              </div>\n            </PopoverContent>\n          </Popover>\n\n          {/* Category Filter */}\n          <Popover>\n            <PopoverTrigger asChild>\n              <div className=\"inline-flex items-center justify-between h-9 px-3 py-2 text-sm font-medium border border-input bg-background rounded-md cursor-pointer hover:bg-accent hover:text-accent-foreground\">\n                <div className=\"flex items-center gap-2\">\n                  <span>Kategorija</span>\n                  <Tooltip>\n                    <TooltipTrigger asChild>\n                      <Info className=\"h-3 w-3 text-muted-foreground\" />\n                    </TooltipTrigger>\n                    <TooltipContent>\n                      <p>Odaberite niše koje odgovaraju vašoj kampanji</p>\n                    </TooltipContent>\n                  </Tooltip>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  {filters.categories?.length ? (\n                    <Badge variant=\"secondary\" className=\"text-xs\">\n                      {filters.categories.length}\n                    </Badge>\n                  ) : null}\n                  <ChevronDown className=\"h-4 w-4\" />\n                </div>\n              </div>\n            </PopoverTrigger>\n            <PopoverContent className=\"w-64\" align=\"start\">\n              <div className=\"space-y-2\">\n                <h4 className=\"font-medium text-sm\">Niša/Kategorija</h4>\n                <p className=\"text-xs text-muted-foreground mb-3\">\n                  Odaberite kategorije koje najbolje opisuju vašu kampanju\n                </p>\n                {categories.map((category) => (\n                  <div key={category.id} className=\"flex items-center space-x-2\">\n                    <Checkbox\n                      id={`category-${category.id}`}\n                      checked={filters.categories?.includes(category.name) || false}\n                      onCheckedChange={() => toggleArrayFilter('categories', category.name)}\n                    />\n                    <label\n                      htmlFor={`category-${category.id}`}\n                      className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center gap-2\"\n                    >\n                      <span>{category.icon}</span>\n                      {category.name}\n                    </label>\n                  </div>\n                ))}\n              </div>\n            </PopoverContent>\n          </Popover>\n\n          {/* Price Range Filter */}\n          <Popover>\n            <PopoverTrigger asChild>\n              <div className=\"inline-flex items-center justify-between h-9 px-3 py-2 text-sm font-medium border border-input bg-background rounded-md cursor-pointer hover:bg-accent hover:text-accent-foreground\">\n                <span>Cijena</span>\n                <div className=\"flex items-center gap-2\">\n                  {(filters.min_price || filters.max_price) ? (\n                    <Badge variant=\"secondary\" className=\"text-xs\">\n                      {filters.min_price || 0}-{filters.max_price || 1000} KM\n                    </Badge>\n                  ) : null}\n                  <ChevronDown className=\"h-4 w-4\" />\n                </div>\n              </div>\n            </PopoverTrigger>\n            <PopoverContent className=\"w-80\" align=\"start\">\n              <div className=\"space-y-4\">\n                <h4 className=\"font-medium text-sm\">Budžet po objavi</h4>\n                <div className=\"px-2\">\n                  <Slider\n                    value={priceRange}\n                    onValueChange={handlePriceChange}\n                    max={1000}\n                    min={0}\n                    step={10}\n                    className=\"w-full\"\n                  />\n                  <div className=\"flex justify-between text-sm text-muted-foreground mt-2\">\n                    <span>{priceRange[0]} KM</span>\n                    <span>{priceRange[1]} KM</span>\n                  </div>\n                </div>\n              </div>\n            </PopoverContent>\n          </Popover>\n\n          {/* Gender Filter */}\n          <Select value={filters.gender || 'all'} onValueChange={(value) => updateFilters('gender', value === 'all' ? undefined : value)}>\n            <SelectTrigger>\n              <SelectValue placeholder=\"Pol\" />\n            </SelectTrigger>\n            <SelectContent>\n              <SelectItem value=\"all\">Svi</SelectItem>\n              <SelectItem value=\"male\">Muški</SelectItem>\n              <SelectItem value=\"female\">Ženski</SelectItem>\n            </SelectContent>\n          </Select>\n\n          {/* Age Range Filter */}\n          <Popover>\n            <PopoverTrigger asChild>\n              <div className=\"inline-flex items-center justify-between h-9 px-3 py-2 text-sm font-medium border border-input bg-background rounded-md cursor-pointer hover:bg-accent hover:text-accent-foreground\">\n                <span>Uzrast</span>\n                <div className=\"flex items-center gap-2\">\n                  {(filters.min_age || filters.max_age) ? (\n                    <Badge variant=\"secondary\" className=\"text-xs\">\n                      {filters.min_age || 18}-{filters.max_age || 65}\n                    </Badge>\n                  ) : null}\n                  <ChevronDown className=\"h-4 w-4\" />\n                </div>\n              </div>\n            </PopoverTrigger>\n            <PopoverContent className=\"w-80\" align=\"start\">\n              <div className=\"space-y-4\">\n                <h4 className=\"font-medium text-sm\">Uzrast influencera</h4>\n                <div className=\"px-2\">\n                  <Slider\n                    value={ageRange}\n                    onValueChange={handleAgeChange}\n                    max={65}\n                    min={18}\n                    step={1}\n                    className=\"w-full\"\n                  />\n                  <div className=\"flex justify-between text-sm text-muted-foreground mt-2\">\n                    <span>{ageRange[0]} godina</span>\n                    <span>{ageRange[1]} godina</span>\n                  </div>\n                </div>\n              </div>\n            </PopoverContent>\n          </Popover>\n        </div>\n\n        {/* Active Filters */}\n        {activeFiltersCount > 0 && (\n          <div className=\"flex flex-wrap gap-2 mt-4 pt-4 border-t\">\n            {filters.platforms?.map((platform) => (\n              <Badge key={platform} variant=\"secondary\" className=\"gap-1\">\n                {platforms.find(p => p.name === platform)?.icon} {platform}\n                <X \n                  className=\"h-3 w-3 cursor-pointer\" \n                  onClick={() => toggleArrayFilter('platforms', platform)}\n                />\n              </Badge>\n            ))}\n            {filters.content_types?.map((contentType) => (\n              <Badge key={contentType} variant=\"secondary\" className=\"gap-1\">\n                {contentType}\n                <X \n                  className=\"h-3 w-3 cursor-pointer\" \n                  onClick={() => toggleArrayFilter('content_types', contentType)}\n                />\n              </Badge>\n            ))}\n            {filters.categories?.map((category) => (\n              <Badge key={category} variant=\"secondary\" className=\"gap-1\">\n                {categories.find(c => c.name === category)?.icon} {category}\n                <X \n                  className=\"h-3 w-3 cursor-pointer\" \n                  onClick={() => toggleArrayFilter('categories', category)}\n                />\n              </Badge>\n            ))}\n            {filters.gender && (\n              <Badge variant=\"secondary\" className=\"gap-1\">\n                {filters.gender === 'male' ? 'Muški' : 'Ženski'}\n                <X \n                  className=\"h-3 w-3 cursor-pointer\" \n                  onClick={() => updateFilters('gender', undefined)}\n                />\n              </Badge>\n            )}\n          </div>\n        )}\n      </div>\n    </TooltipProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;AAVA;;;;;;;;;;AAmBe,SAAS,kBAAkB,KAAoE;QAApE,EAAE,OAAO,EAAE,eAAe,EAAE,cAAc,EAA0B,GAApE;QA4GvB,oBAgDA,wBAsDA,qBA2HN,qBASA,yBASA;;IA9VX,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAC,QAAQ,SAAS,IAAI;QAAG,QAAQ,SAAS,IAAI;KAAK;IAChG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAC,QAAQ,OAAO,IAAI;QAAI,QAAQ,OAAO,IAAI;KAAG;IAEvF,mDAAmD;IACnD,MAAM,YAAY;QAChB;YAAE,IAAI;YAAG,MAAM;YAAa,MAAM;QAAK;QACvC;YAAE,IAAI;YAAG,MAAM;YAAW,MAAM;QAAK;QACrC;YAAE,IAAI;YAAG,MAAM;YAAU,MAAM;QAAK;QACpC;YAAE,IAAI;YAAG,MAAM;YAAY,MAAM;QAAK;QACtC;YAAE,IAAI;YAAG,MAAM;YAAW,MAAM;QAAK;QACrC;YAAE,IAAI;YAAG,MAAM;YAAY,MAAM;QAAK;KACvC;IAED,MAAM,eAAe;QACnB;YAAE,IAAI;YAAG,MAAM;YAAQ,WAAW;gBAAC;gBAAa;gBAAY;aAAW;QAAC;QACxE;YAAE,IAAI;YAAG,MAAM;YAAS,WAAW;gBAAC;gBAAa;aAAW;QAAC;QAC7D;YAAE,IAAI;YAAG,MAAM;YAAQ,WAAW;gBAAC;aAAY;QAAC;QAChD;YAAE,IAAI;YAAG,MAAM;YAAS,WAAW;gBAAC;gBAAW;gBAAU;aAAW;QAAC;QACrE;YAAE,IAAI;YAAG,MAAM;YAAS,WAAW;gBAAC;gBAAW;aAAS;QAAC;QACzD;YAAE,IAAI;YAAG,MAAM;YAAQ,WAAW;gBAAC;gBAAa;gBAAW;gBAAU;aAAW;QAAC;KAClF;IAED,MAAM,aAAa;QACjB;YAAE,IAAI;YAAG,MAAM;YAAW,MAAM;QAAK;QACrC;YAAE,IAAI;YAAG,MAAM;YAAU,MAAM;QAAK;QACpC;YAAE,IAAI;YAAG,MAAM;YAAW,MAAM;QAAK;QACrC;YAAE,IAAI;YAAG,MAAM;YAAQ,MAAM;QAAK;QAClC;YAAE,IAAI;YAAG,MAAM;YAAU,MAAM;QAAK;QACpC;YAAE,IAAI;YAAG,MAAM;YAAQ,MAAM;QAAK;QAClC;YAAE,IAAI;YAAG,MAAM;YAAa,MAAM;QAAK;QACvC;YAAE,IAAI;YAAG,MAAM;YAAU,MAAM;QAAK;KACrC;IAED,MAAM,gBAAgB,CAAC,KAA0B;QAC/C,gBAAgB;YACd,GAAG,OAAO;YACV,CAAC,IAAI,EAAE;QACT;IACF;IAEA,MAAM,oBAAoB,CAAC,KAA0B;QACnD,MAAM,eAAe,AAAC,OAAO,CAAC,IAAI,IAAiB,EAAE;QACrD,MAAM,WAAW,aAAa,QAAQ,CAAC,SACnC,aAAa,MAAM,CAAC,CAAA,OAAQ,SAAS,SACrC;eAAI;YAAc;SAAM;QAE5B,cAAc,KAAK;IACrB;IAEA,MAAM,oBAAoB,CAAC;QACzB,cAAc;QACd,cAAc,aAAa,MAAM,CAAC,EAAE;QACpC,cAAc,aAAa,MAAM,CAAC,EAAE;IACtC;IAEA,MAAM,kBAAkB,CAAC;QACvB,YAAY;QACZ,cAAc,WAAW,MAAM,CAAC,EAAE;QAClC,cAAc,WAAW,MAAM,CAAC,EAAE;IACpC;IAEA,mCAAmC;IACnC,MAAM,wBAAwB;YAExB,qBACA,oBACA;QAHJ,IAAI,QAAQ;QACZ,KAAI,sBAAA,QAAQ,UAAU,cAAlB,0CAAA,oBAAoB,MAAM,EAAE,SAAS,QAAQ,UAAU,CAAC,MAAM;QAClE,KAAI,qBAAA,QAAQ,SAAS,cAAjB,yCAAA,mBAAmB,MAAM,EAAE,SAAS,QAAQ,SAAS,CAAC,MAAM;QAChE,KAAI,yBAAA,QAAQ,aAAa,cAArB,6CAAA,uBAAuB,MAAM,EAAE,SAAS,QAAQ,aAAa,CAAC,MAAM;QACxE,IAAI,QAAQ,SAAS,KAAK,aAAa,QAAQ,SAAS,KAAK,WAAW,SAAS;QACjF,IAAI,QAAQ,MAAM,EAAE,SAAS;QAC7B,IAAI,QAAQ,OAAO,KAAK,aAAa,QAAQ,OAAO,KAAK,WAAW,SAAS;QAC7E,OAAO;IACT;IAEA,MAAM,qBAAqB;IAE3B,qBACE,6LAAC,sIAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAc;;;;;;8CAC5B,6LAAC,sIAAA,CAAA,UAAO;;sDACN,6LAAC,sIAAA,CAAA,iBAAc;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6LAAC,sIAAA,CAAA,iBAAc;sDACb,cAAA,6LAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;wBAIR,qBAAqB,mBACpB,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,SAAS;;8CACzC,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;gCAAiB;gCACjB;gCAAmB;;;;;;;;;;;;;8BAMtC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,sIAAA,CAAA,UAAO;;8CACN,6LAAC,sIAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;gDAAI,WAAU;;oDACZ,EAAA,qBAAA,QAAQ,SAAS,cAAjB,yCAAA,mBAAmB,MAAM,kBACxB,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;kEAClC,QAAQ,SAAS,CAAC,MAAM;;;;;+DAEzB;kEACJ,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAI7B,6LAAC,sIAAA,CAAA,iBAAc;oCAAC,WAAU;oCAAO,OAAM;8CACrC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAsB;;;;;;4CACnC,UAAU,GAAG,CAAC,CAAC;oDAID;qEAHb,6LAAC;oDAAsB,WAAU;;sEAC/B,6LAAC,uIAAA,CAAA,WAAQ;4DACP,IAAI,AAAC,YAAuB,OAAZ,SAAS,EAAE;4DAC3B,SAAS,EAAA,qBAAA,QAAQ,SAAS,cAAjB,yCAAA,mBAAmB,QAAQ,CAAC,SAAS,IAAI,MAAK;4DACvD,iBAAiB,IAAM,kBAAkB,aAAa,SAAS,IAAI;;;;;;sEAErE,6LAAC;4DACC,SAAS,AAAC,YAAuB,OAAZ,SAAS,EAAE;4DAChC,WAAU;;8EAEV,6LAAC;8EAAM,SAAS,IAAI;;;;;;gEACnB,SAAS,IAAI;;;;;;;;mDAXR,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;sCAoB7B,6LAAC,sIAAA,CAAA,UAAO;;8CACN,6LAAC,sIAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC,sIAAA,CAAA,UAAO;;0EACN,6LAAC,sIAAA,CAAA,iBAAc;gEAAC,OAAO;0EACrB,cAAA,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,6LAAC,sIAAA,CAAA,iBAAc;0EACb,cAAA,6LAAC;8EAAE;;;;;;;;;;;;;;;;;;;;;;;0DAIT,6LAAC;gDAAI,WAAU;;oDACZ,EAAA,yBAAA,QAAQ,aAAa,cAArB,6CAAA,uBAAuB,MAAM,kBAC5B,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;kEAClC,QAAQ,aAAa,CAAC,MAAM;;;;;+DAE7B;kEACJ,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAI7B,6LAAC,sIAAA,CAAA,iBAAc;oCAAC,WAAU;oCAAO,OAAM;8CACrC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAsB;;;;;;0DACpC,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;4CAGjD,aAAa,GAAG,CAAC,CAAC;oDAIJ;qEAHb,6LAAC;oDAAyB,WAAU;;sEAClC,6LAAC,uIAAA,CAAA,WAAQ;4DACP,IAAI,AAAC,WAAyB,OAAf,YAAY,EAAE;4DAC7B,SAAS,EAAA,yBAAA,QAAQ,aAAa,cAArB,6CAAA,uBAAuB,QAAQ,CAAC,YAAY,IAAI,MAAK;4DAC9D,iBAAiB,IAAM,kBAAkB,iBAAiB,YAAY,IAAI;;;;;;sEAE5E,6LAAC;4DACC,SAAS,AAAC,WAAyB,OAAf,YAAY,EAAE;4DAClC,WAAU;;8EAEV,6LAAC;8EAAM,YAAY,IAAI;;;;;;8EACvB,6LAAC;oEAAK,WAAU;;wEACb,YAAY,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;wEACvC,YAAY,SAAS,CAAC,MAAM,GAAG,KAAK;;;;;;;;;;;;;;mDAbjC,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;sCAuBhC,6LAAC,sIAAA,CAAA,UAAO;;8CACN,6LAAC,sIAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC,sIAAA,CAAA,UAAO;;0EACN,6LAAC,sIAAA,CAAA,iBAAc;gEAAC,OAAO;0EACrB,cAAA,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,6LAAC,sIAAA,CAAA,iBAAc;0EACb,cAAA,6LAAC;8EAAE;;;;;;;;;;;;;;;;;;;;;;;0DAIT,6LAAC;gDAAI,WAAU;;oDACZ,EAAA,sBAAA,QAAQ,UAAU,cAAlB,0CAAA,oBAAoB,MAAM,kBACzB,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;kEAClC,QAAQ,UAAU,CAAC,MAAM;;;;;+DAE1B;kEACJ,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAI7B,6LAAC,sIAAA,CAAA,iBAAc;oCAAC,WAAU;oCAAO,OAAM;8CACrC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAsB;;;;;;0DACpC,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;4CAGjD,WAAW,GAAG,CAAC,CAAC;oDAIF;qEAHb,6LAAC;oDAAsB,WAAU;;sEAC/B,6LAAC,uIAAA,CAAA,WAAQ;4DACP,IAAI,AAAC,YAAuB,OAAZ,SAAS,EAAE;4DAC3B,SAAS,EAAA,sBAAA,QAAQ,UAAU,cAAlB,0CAAA,oBAAoB,QAAQ,CAAC,SAAS,IAAI,MAAK;4DACxD,iBAAiB,IAAM,kBAAkB,cAAc,SAAS,IAAI;;;;;;sEAEtE,6LAAC;4DACC,SAAS,AAAC,YAAuB,OAAZ,SAAS,EAAE;4DAChC,WAAU;;8EAEV,6LAAC;8EAAM,SAAS,IAAI;;;;;;gEACnB,SAAS,IAAI;;;;;;;;mDAXR,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;sCAoB7B,6LAAC,sIAAA,CAAA,UAAO;;8CACN,6LAAC,sIAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;gDAAI,WAAU;;oDACX,QAAQ,SAAS,IAAI,QAAQ,SAAS,iBACtC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;;4DAClC,QAAQ,SAAS,IAAI;4DAAE;4DAAE,QAAQ,SAAS,IAAI;4DAAK;;;;;;+DAEpD;kEACJ,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAI7B,6LAAC,sIAAA,CAAA,iBAAc;oCAAC,WAAU;oCAAO,OAAM;8CACrC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAsB;;;;;;0DACpC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,OAAO;wDACP,eAAe;wDACf,KAAK;wDACL,KAAK;wDACL,MAAM;wDACN,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAM,UAAU,CAAC,EAAE;oEAAC;;;;;;;0EACrB,6LAAC;;oEAAM,UAAU,CAAC,EAAE;oEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ/B,6LAAC,qIAAA,CAAA,SAAM;4BAAC,OAAO,QAAQ,MAAM,IAAI;4BAAO,eAAe,CAAC,QAAU,cAAc,UAAU,UAAU,QAAQ,YAAY;;8CACtH,6LAAC,qIAAA,CAAA,gBAAa;8CACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wCAAC,aAAY;;;;;;;;;;;8CAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sDACZ,6LAAC,qIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAM;;;;;;sDACxB,6LAAC,qIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAO;;;;;;sDACzB,6LAAC,qIAAA,CAAA,aAAU;4CAAC,OAAM;sDAAS;;;;;;;;;;;;;;;;;;sCAK/B,6LAAC,sIAAA,CAAA,UAAO;;8CACN,6LAAC,sIAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;gDAAI,WAAU;;oDACX,QAAQ,OAAO,IAAI,QAAQ,OAAO,iBAClC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;;4DAClC,QAAQ,OAAO,IAAI;4DAAG;4DAAE,QAAQ,OAAO,IAAI;;;;;;+DAE5C;kEACJ,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAI7B,6LAAC,sIAAA,CAAA,iBAAc;oCAAC,WAAU;oCAAO,OAAM;8CACrC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAsB;;;;;;0DACpC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,OAAO;wDACP,eAAe;wDACf,KAAK;wDACL,KAAK;wDACL,MAAM;wDACN,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAM,QAAQ,CAAC,EAAE;oEAAC;;;;;;;0EACnB,6LAAC;;oEAAM,QAAQ,CAAC,EAAE;oEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAS9B,qBAAqB,mBACpB,6LAAC;oBAAI,WAAU;;yBACZ,sBAAA,QAAQ,SAAS,cAAjB,0CAAA,oBAAmB,GAAG,CAAC,CAAC;gCAEpB;iDADH,6LAAC,oIAAA,CAAA,QAAK;gCAAgB,SAAQ;gCAAY,WAAU;;qCACjD,kBAAA,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,uBAA/B,sCAAA,gBAA0C,IAAI;oCAAC;oCAAE;kDAClD,6LAAC,+LAAA,CAAA,IAAC;wCACA,WAAU;wCACV,SAAS,IAAM,kBAAkB,aAAa;;;;;;;+BAJtC;;;;;;yBAQb,0BAAA,QAAQ,aAAa,cAArB,8CAAA,wBAAuB,GAAG,CAAC,CAAC,4BAC3B,6LAAC,oIAAA,CAAA,QAAK;gCAAmB,SAAQ;gCAAY,WAAU;;oCACpD;kDACD,6LAAC,+LAAA,CAAA,IAAC;wCACA,WAAU;wCACV,SAAS,IAAM,kBAAkB,iBAAiB;;;;;;;+BAJ1C;;;;;yBAQb,uBAAA,QAAQ,UAAU,cAAlB,2CAAA,qBAAoB,GAAG,CAAC,CAAC;gCAErB;iDADH,6LAAC,oIAAA,CAAA,QAAK;gCAAgB,SAAQ;gCAAY,WAAU;;qCACjD,mBAAA,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,uBAAhC,uCAAA,iBAA2C,IAAI;oCAAC;oCAAE;kDACnD,6LAAC,+LAAA,CAAA,IAAC;wCACA,WAAU;wCACV,SAAS,IAAM,kBAAkB,cAAc;;;;;;;+BAJvC;;;;;;wBAQb,QAAQ,MAAM,kBACb,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAY,WAAU;;gCAClC,QAAQ,MAAM,KAAK,SAAS,UAAU;8CACvC,6LAAC,+LAAA,CAAA,IAAC;oCACA,WAAU;oCACV,SAAS,IAAM,cAAc,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD;GAtXwB;KAAA", "debugId": null}}, {"offset": {"line": 2678, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/profiles.ts"], "sourcesContent": ["import { supabase } from './supabase';\nimport { Database } from './database.types';\n\ntype Profile = Database['public']['Tables']['profiles']['Row'];\ntype ProfileInsert = Database['public']['Tables']['profiles']['Insert'];\ntype ProfileUpdate = Database['public']['Tables']['profiles']['Update'];\n\ntype Influencer = Database['public']['Tables']['influencers']['Row'];\ntype InfluencerInsert = Database['public']['Tables']['influencers']['Insert'];\ntype InfluencerUpdate = Database['public']['Tables']['influencers']['Update'];\n\ntype Business = Database['public']['Tables']['businesses']['Row'];\ntype BusinessInsert = Database['public']['Tables']['businesses']['Insert'];\ntype BusinessUpdate = Database['public']['Tables']['businesses']['Update'];\n\n// Profile functions\nexport const getProfile = async (userId: string) => {\n  const { data, error } = await supabase\n    .from('profiles')\n    .select('*')\n    .eq('id', userId)\n    .single();\n  \n  return { data, error };\n};\n\nexport const updateProfile = async (userId: string, updates: ProfileUpdate) => {\n  const { data, error } = await supabase\n    .from('profiles')\n    .update(updates)\n    .eq('id', userId)\n    .select()\n    .single();\n\n  return { data, error };\n};\n\nexport const createProfile = async (profileData: ProfileInsert) => {\n  const { data, error } = await supabase\n    .from('profiles')\n    .insert(profileData)\n    .select()\n    .single();\n\n  return { data, error };\n};\n\n// Get public influencer profile by username\nexport const getPublicInfluencerProfile = async (username: string) => {\n  // Use RPC function to get influencer data\n  const { data: influencerData, error: influencerError } = await supabase\n    .rpc('get_influencers_with_details', {\n      search_term: username,\n      min_followers: 0,\n      max_followers: 999999999,\n      min_price: 0,\n      max_price: 999999,\n      platform_filter: '',\n      category_filter: '',\n      location_filter: '',\n      limit_count: 10\n    });\n\n  if (influencerError || !influencerData || influencerData.length === 0) {\n    return { data: null, error: influencerError || { message: 'Influencer not found' } };\n  }\n\n  // Find the exact username match\n  const exactMatch = influencerData.find(item => item.username === username);\n  if (!exactMatch) {\n    return { data: null, error: { message: 'Influencer not found' } };\n  }\n\n  const data = exactMatch;\n\n  // Transform data to match expected structure\n  const transformedData = {\n    id: data.id,\n    username: data.username,\n    full_name: data.full_name,\n    avatar_url: data.avatar_url,\n    bio: data.bio,\n    location: data.location,\n    created_at: data.created_at,\n    gender: data.gender,\n    age: data.age,\n    is_verified: data.is_verified,\n    platforms: [\n      ...(data.instagram_followers > 0 ? [{\n        platform_id: 1,\n        platform_name: 'Instagram',\n        platform_icon: '📷',\n        handle: `@${data.username}`,\n        followers_count: data.instagram_followers,\n        is_verified: data.is_verified\n      }] : []),\n      ...(data.tiktok_followers > 0 ? [{\n        platform_id: 2,\n        platform_name: 'TikTok',\n        platform_icon: '🎵',\n        handle: `@${data.username}`,\n        followers_count: data.tiktok_followers,\n        is_verified: false\n      }] : []),\n      ...(data.youtube_subscribers > 0 ? [{\n        platform_id: 3,\n        platform_name: 'YouTube',\n        platform_icon: '📺',\n        handle: `@${data.username}`,\n        followers_count: data.youtube_subscribers,\n        is_verified: false\n      }] : [])\n    ],\n    categories: [], // TODO: Add categories when implemented\n    pricing: [\n      ...(data.price_per_post ? [{\n        platform_id: 1,\n        platform_name: 'Instagram',\n        platform_icon: '📷',\n        content_type_id: 1,\n        content_type_name: 'Post',\n        price: Number(data.price_per_post),\n        currency: 'KM'\n      }] : []),\n      ...(data.price_per_story ? [{\n        platform_id: 1,\n        platform_name: 'Instagram',\n        platform_icon: '📷',\n        content_type_id: 2,\n        content_type_name: 'Story',\n        price: Number(data.price_per_story),\n        currency: 'KM'\n      }] : []),\n      ...(data.price_per_reel ? [{\n        platform_id: 1,\n        platform_name: 'Instagram',\n        platform_icon: '📷',\n        content_type_id: 3,\n        content_type_name: 'Reel',\n        price: Number(data.price_per_reel),\n        currency: 'KM'\n      }] : [])\n    ],\n    portfolio_items: [], // TODO: Add portfolio items when implemented\n    total_followers: (data.instagram_followers || 0) +\n                    (data.tiktok_followers || 0) +\n                    (data.youtube_subscribers || 0),\n    min_price: Math.min(\n      ...[data.price_per_post, data.price_per_story, data.price_per_reel]\n        .filter(Boolean)\n        .map(Number)\n    ) || 0,\n    max_price: Math.max(\n      ...[data.price_per_post, data.price_per_story, data.price_per_reel]\n        .filter(Boolean)\n        .map(Number)\n    ) || 0\n  };\n\n  return { data: transformedData, error: null };\n};\n\nexport const upsertProfile = async (userId: string, updates: ProfileUpdate) => {\n  // First try to get existing profile\n  const { data: existingProfile } = await getProfile(userId);\n\n  if (existingProfile) {\n    // Profile exists, update it\n    return updateProfile(userId, updates);\n  } else {\n    // Profile doesn't exist, create it\n    const profileData: ProfileInsert = {\n      id: userId,\n      user_type: updates.user_type || 'influencer',\n      username: updates.username || null,\n      full_name: updates.full_name || null,\n      avatar_url: updates.avatar_url || null,\n      bio: updates.bio || null,\n      website_url: updates.website_url || null,\n      location: updates.location || null,\n    };\n    return createProfile(profileData);\n  }\n};\n\nexport const checkUsernameAvailable = async (username: string, excludeUserId?: string) => {\n  let query = supabase\n    .from('profiles')\n    .select('id')\n    .eq('username', username);\n  \n  if (excludeUserId) {\n    query = query.neq('id', excludeUserId);\n  }\n  \n  const { data, error } = await query;\n  \n  if (error) return { available: false, error };\n  return { available: data.length === 0, error: null };\n};\n\n// Influencer functions\nexport const getInfluencer = async (userId: string) => {\n  const { data, error } = await supabase\n    .from('influencers')\n    .select(`\n      *,\n      profiles (*)\n    `)\n    .eq('id', userId)\n    .single();\n  \n  return { data, error };\n};\n\nexport const createInfluencer = async (influencerData: InfluencerInsert) => {\n  const { data, error } = await supabase\n    .from('influencers')\n    .insert(influencerData)\n    .select()\n    .single();\n  \n  return { data, error };\n};\n\nexport const updateInfluencer = async (userId: string, updates: InfluencerUpdate) => {\n  const { data, error } = await supabase\n    .from('influencers')\n    .update(updates)\n    .eq('id', userId)\n    .select()\n    .single();\n\n  return { data, error };\n};\n\nexport const getInfluencers = async (filters?: {\n  niche?: string;\n  minFollowers?: number;\n  maxFollowers?: number;\n  location?: string;\n  limit?: number;\n  offset?: number;\n}) => {\n  let query = supabase\n    .from('influencers')\n    .select(`\n      *,\n      profiles (*)\n    `);\n\n  if (filters?.niche) {\n    query = query.ilike('niche', `%${filters.niche}%`);\n  }\n\n  if (filters?.minFollowers) {\n    query = query.gte('instagram_followers', filters.minFollowers);\n  }\n\n  if (filters?.maxFollowers) {\n    query = query.lte('instagram_followers', filters.maxFollowers);\n  }\n\n  if (filters?.location) {\n    query = query.eq('profiles.location', filters.location);\n  }\n\n  if (filters?.limit) {\n    query = query.limit(filters.limit);\n  }\n\n  if (filters?.offset) {\n    query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);\n  }\n\n  const { data, error } = await query;\n  return { data, error };\n};\n\n// Business functions\nexport const getBusiness = async (userId: string) => {\n  const { data, error } = await supabase\n    .from('businesses')\n    .select(`\n      *,\n      profiles (*)\n    `)\n    .eq('id', userId)\n    .single();\n  \n  return { data, error };\n};\n\nexport const createBusiness = async (businessData: BusinessInsert) => {\n  const { data, error } = await supabase\n    .from('businesses')\n    .insert(businessData)\n    .select()\n    .single();\n  \n  return { data, error };\n};\n\nexport const updateBusiness = async (userId: string, updates: BusinessUpdate) => {\n  const { data, error } = await supabase\n    .from('businesses')\n    .update(updates)\n    .eq('id', userId)\n    .select()\n    .single();\n\n  return { data, error };\n};\n\n// Category functions\nexport const getCategories = async () => {\n  const { data, error } = await supabase\n    .from('categories')\n    .select('*')\n    .order('name');\n\n  return { data, error };\n};\n\nexport const getInfluencerCategories = async (influencerId: string) => {\n  const { data, error } = await supabase\n    .from('influencer_categories')\n    .select(`\n      category_id,\n      is_primary,\n      categories (*)\n    `)\n    .eq('influencer_id', influencerId);\n\n  return { data, error };\n};\n\nexport const updateInfluencerCategories = async (influencerId: string, categoryIds: number[]) => {\n  // First, delete existing categories\n  await supabase\n    .from('influencer_categories')\n    .delete()\n    .eq('influencer_id', influencerId);\n\n  // Then insert new categories\n  if (categoryIds.length > 0) {\n    const categoryData = categoryIds.map((categoryId, index) => ({\n      influencer_id: influencerId,\n      category_id: categoryId,\n      is_primary: index === 0 // First category is primary\n    }));\n\n    const { data, error } = await supabase\n      .from('influencer_categories')\n      .insert(categoryData)\n      .select();\n\n    return { data, error };\n  }\n\n  return { data: [], error: null };\n};\n\nexport const getBusinessTargetCategories = async (businessId: string) => {\n  const { data, error } = await supabase\n    .from('business_target_categories')\n    .select(`\n      category_id,\n      categories (*)\n    `)\n    .eq('business_id', businessId);\n\n  return { data, error };\n};\n\nexport const updateBusinessTargetCategories = async (businessId: string, categoryIds: number[]) => {\n  // First, delete existing categories\n  await supabase\n    .from('business_target_categories')\n    .delete()\n    .eq('business_id', businessId);\n\n  // Then insert new categories\n  if (categoryIds.length > 0) {\n    const categoryData = categoryIds.map(categoryId => ({\n      business_id: businessId,\n      category_id: categoryId\n    }));\n\n    const { data, error } = await supabase\n      .from('business_target_categories')\n      .insert(categoryData)\n      .select();\n\n    return { data, error };\n  }\n\n  return { data: [], error: null };\n};\n\n// Combined profile functions\nexport const getFullProfile = async (userId: string) => {\n  const { data: profile, error: profileError } = await getProfile(userId);\n  \n  if (profileError || !profile) {\n    return { data: null, error: profileError };\n  }\n\n  if (profile.user_type === 'influencer') {\n    const { data: influencer, error: influencerError } = await getInfluencer(userId);\n    return { \n      data: influencer ? { ...profile, influencer } : profile, \n      error: influencerError \n    };\n  } else if (profile.user_type === 'business') {\n    const { data: business, error: businessError } = await getBusiness(userId);\n    return { \n      data: business ? { ...profile, business } : profile, \n      error: businessError \n    };\n  }\n\n  return { data: profile, error: null };\n};\n\n// Upload avatar function\nexport const uploadAvatar = async (userId: string, file: File) => {\n  const fileExt = file.name.split('.').pop();\n  const fileName = `${userId}-${Math.random()}.${fileExt}`;\n  const filePath = `avatars/${fileName}`;\n\n  const { error: uploadError } = await supabase.storage\n    .from('avatars')\n    .upload(filePath, file);\n\n  if (uploadError) {\n    return { data: null, error: uploadError };\n  }\n\n  const { data } = supabase.storage\n    .from('avatars')\n    .getPublicUrl(filePath);\n\n  // Update profile with new avatar URL\n  const { error: updateError } = await updateProfile(userId, {\n    avatar_url: data.publicUrl,\n  });\n\n  if (updateError) {\n    return { data: null, error: updateError };\n  }\n\n  return { data: data.publicUrl, error: null };\n};\n\n// Platform and Pricing functions\nexport const getInfluencerPlatforms = async (influencerId: string) => {\n  const { data, error } = await supabase\n    .from('influencer_platforms')\n    .select(`\n      *,\n      platforms (*)\n    `)\n    .eq('influencer_id', influencerId)\n    .eq('is_active', true);\n\n  return { data, error };\n};\n\nexport const getInfluencerPricing = async (influencerId: string) => {\n  const { data, error } = await supabase\n    .from('influencer_platform_pricing')\n    .select(`\n      *,\n      platforms (*),\n      content_types (*)\n    `)\n    .eq('influencer_id', influencerId)\n    .eq('is_available', true);\n\n  return { data, error };\n};\n\nexport const updateInfluencerPlatforms = async (influencerId: string, platforms: any[]) => {\n  // First, delete existing platforms\n  await supabase\n    .from('influencer_platforms')\n    .delete()\n    .eq('influencer_id', influencerId);\n\n  // Then insert new platforms\n  if (platforms.length > 0) {\n    const platformData = platforms.map(platform => ({\n      influencer_id: influencerId,\n      platform_id: platform.platform_id,\n      handle: platform.handle || null,\n      followers_count: platform.followers_count || 0,\n      is_verified: false,\n      is_active: true\n    }));\n\n    const { data, error } = await supabase\n      .from('influencer_platforms')\n      .insert(platformData)\n      .select();\n\n    return { data, error };\n  }\n\n  return { data: [], error: null };\n};\n\nexport const updateInfluencerPricing = async (influencerId: string, pricing: any[]) => {\n  // First, delete existing pricing\n  await supabase\n    .from('influencer_platform_pricing')\n    .delete()\n    .eq('influencer_id', influencerId);\n\n  // Then insert new pricing\n  if (pricing.length > 0) {\n    const pricingData = pricing.map(price => ({\n      influencer_id: influencerId,\n      platform_id: price.platform_id,\n      content_type_id: price.content_type_id,\n      price: price.price,\n      currency: 'KM',\n      is_available: price.is_available !== false\n    }));\n\n    const { data, error } = await supabase\n      .from('influencer_platform_pricing')\n      .insert(pricingData)\n      .select();\n\n    return { data, error };\n  }\n\n  return { data: [], error: null };\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAgBO,MAAM,aAAa,OAAO;IAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,gBAAgB,OAAO,QAAgB;IAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,gBAAgB,OAAO;IAClC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,aACP,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,MAAM,6BAA6B,OAAO;IAC/C,0CAA0C;IAC1C,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACpE,GAAG,CAAC,gCAAgC;QACnC,aAAa;QACb,eAAe;QACf,eAAe;QACf,WAAW;QACX,WAAW;QACX,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,aAAa;IACf;IAEF,IAAI,mBAAmB,CAAC,kBAAkB,eAAe,MAAM,KAAK,GAAG;QACrE,OAAO;YAAE,MAAM;YAAM,OAAO,mBAAmB;gBAAE,SAAS;YAAuB;QAAE;IACrF;IAEA,gCAAgC;IAChC,MAAM,aAAa,eAAe,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;IACjE,IAAI,CAAC,YAAY;QACf,OAAO;YAAE,MAAM;YAAM,OAAO;gBAAE,SAAS;YAAuB;QAAE;IAClE;IAEA,MAAM,OAAO;IAEb,6CAA6C;IAC7C,MAAM,kBAAkB;QACtB,IAAI,KAAK,EAAE;QACX,UAAU,KAAK,QAAQ;QACvB,WAAW,KAAK,SAAS;QACzB,YAAY,KAAK,UAAU;QAC3B,KAAK,KAAK,GAAG;QACb,UAAU,KAAK,QAAQ;QACvB,YAAY,KAAK,UAAU;QAC3B,QAAQ,KAAK,MAAM;QACnB,KAAK,KAAK,GAAG;QACb,aAAa,KAAK,WAAW;QAC7B,WAAW;eACL,KAAK,mBAAmB,GAAG,IAAI;gBAAC;oBAClC,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,QAAQ,AAAC,IAAiB,OAAd,KAAK,QAAQ;oBACzB,iBAAiB,KAAK,mBAAmB;oBACzC,aAAa,KAAK,WAAW;gBAC/B;aAAE,GAAG,EAAE;eACH,KAAK,gBAAgB,GAAG,IAAI;gBAAC;oBAC/B,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,QAAQ,AAAC,IAAiB,OAAd,KAAK,QAAQ;oBACzB,iBAAiB,KAAK,gBAAgB;oBACtC,aAAa;gBACf;aAAE,GAAG,EAAE;eACH,KAAK,mBAAmB,GAAG,IAAI;gBAAC;oBAClC,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,QAAQ,AAAC,IAAiB,OAAd,KAAK,QAAQ;oBACzB,iBAAiB,KAAK,mBAAmB;oBACzC,aAAa;gBACf;aAAE,GAAG,EAAE;SACR;QACD,YAAY,EAAE;QACd,SAAS;eACH,KAAK,cAAc,GAAG;gBAAC;oBACzB,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,OAAO,OAAO,KAAK,cAAc;oBACjC,UAAU;gBACZ;aAAE,GAAG,EAAE;eACH,KAAK,eAAe,GAAG;gBAAC;oBAC1B,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,OAAO,OAAO,KAAK,eAAe;oBAClC,UAAU;gBACZ;aAAE,GAAG,EAAE;eACH,KAAK,cAAc,GAAG;gBAAC;oBACzB,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,OAAO,OAAO,KAAK,cAAc;oBACjC,UAAU;gBACZ;aAAE,GAAG,EAAE;SACR;QACD,iBAAiB,EAAE;QACnB,iBAAiB,CAAC,KAAK,mBAAmB,IAAI,CAAC,IAC/B,CAAC,KAAK,gBAAgB,IAAI,CAAC,IAC3B,CAAC,KAAK,mBAAmB,IAAI,CAAC;QAC9C,WAAW,KAAK,GAAG,IACd;YAAC,KAAK,cAAc;YAAE,KAAK,eAAe;YAAE,KAAK,cAAc;SAAC,CAChE,MAAM,CAAC,SACP,GAAG,CAAC,YACJ;QACL,WAAW,KAAK,GAAG,IACd;YAAC,KAAK,cAAc;YAAE,KAAK,eAAe;YAAE,KAAK,cAAc;SAAC,CAChE,MAAM,CAAC,SACP,GAAG,CAAC,YACJ;IACP;IAEA,OAAO;QAAE,MAAM;QAAiB,OAAO;IAAK;AAC9C;AAEO,MAAM,gBAAgB,OAAO,QAAgB;IAClD,oCAAoC;IACpC,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,WAAW;IAEnD,IAAI,iBAAiB;QACnB,4BAA4B;QAC5B,OAAO,cAAc,QAAQ;IAC/B,OAAO;QACL,mCAAmC;QACnC,MAAM,cAA6B;YACjC,IAAI;YACJ,WAAW,QAAQ,SAAS,IAAI;YAChC,UAAU,QAAQ,QAAQ,IAAI;YAC9B,WAAW,QAAQ,SAAS,IAAI;YAChC,YAAY,QAAQ,UAAU,IAAI;YAClC,KAAK,QAAQ,GAAG,IAAI;YACpB,aAAa,QAAQ,WAAW,IAAI;YACpC,UAAU,QAAQ,QAAQ,IAAI;QAChC;QACA,OAAO,cAAc;IACvB;AACF;AAEO,MAAM,yBAAyB,OAAO,UAAkB;IAC7D,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,YACL,MAAM,CAAC,MACP,EAAE,CAAC,YAAY;IAElB,IAAI,eAAe;QACjB,QAAQ,MAAM,GAAG,CAAC,MAAM;IAC1B;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAE9B,IAAI,OAAO,OAAO;QAAE,WAAW;QAAO;IAAM;IAC5C,OAAO;QAAE,WAAW,KAAK,MAAM,KAAK;QAAG,OAAO;IAAK;AACrD;AAGO,MAAM,gBAAgB,OAAO;IAClC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAE,wCAIR,EAAE,CAAC,MAAM,QACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,mBAAmB,OAAO;IACrC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,gBACP,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,mBAAmB,OAAO,QAAgB;IACrD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,iBAAiB,OAAO;IAQnC,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,eACL,MAAM,CAAE;IAKX,IAAI,oBAAA,8BAAA,QAAS,KAAK,EAAE;QAClB,QAAQ,MAAM,KAAK,CAAC,SAAS,AAAC,IAAiB,OAAd,QAAQ,KAAK,EAAC;IACjD;IAEA,IAAI,oBAAA,8BAAA,QAAS,YAAY,EAAE;QACzB,QAAQ,MAAM,GAAG,CAAC,uBAAuB,QAAQ,YAAY;IAC/D;IAEA,IAAI,oBAAA,8BAAA,QAAS,YAAY,EAAE;QACzB,QAAQ,MAAM,GAAG,CAAC,uBAAuB,QAAQ,YAAY;IAC/D;IAEA,IAAI,oBAAA,8BAAA,QAAS,QAAQ,EAAE;QACrB,QAAQ,MAAM,EAAE,CAAC,qBAAqB,QAAQ,QAAQ;IACxD;IAEA,IAAI,oBAAA,8BAAA,QAAS,KAAK,EAAE;QAClB,QAAQ,MAAM,KAAK,CAAC,QAAQ,KAAK;IACnC;IAEA,IAAI,oBAAA,8BAAA,QAAS,MAAM,EAAE;QACnB,QAAQ,MAAM,KAAK,CAAC,QAAQ,MAAM,EAAE,QAAQ,MAAM,GAAG,CAAC,QAAQ,KAAK,IAAI,EAAE,IAAI;IAC/E;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAC9B,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,MAAM,cAAc,OAAO;IAChC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAE,wCAIR,EAAE,CAAC,MAAM,QACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,iBAAiB,OAAO;IACnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,cACP,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,iBAAiB,OAAO,QAAgB;IACnD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,MAAM,gBAAgB;IAC3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,KACP,KAAK,CAAC;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yBACL,MAAM,CAAE,uEAKR,EAAE,CAAC,iBAAiB;IAEvB,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,6BAA6B,OAAO,cAAsB;IACrE,oCAAoC;IACpC,MAAM,yHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,yBACL,MAAM,GACN,EAAE,CAAC,iBAAiB;IAEvB,6BAA6B;IAC7B,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,MAAM,eAAe,YAAY,GAAG,CAAC,CAAC,YAAY,QAAU,CAAC;gBAC3D,eAAe;gBACf,aAAa;gBACb,YAAY,UAAU,EAAE,4BAA4B;YACtD,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yBACL,MAAM,CAAC,cACP,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,OAAO;QAAE,MAAM,EAAE;QAAE,OAAO;IAAK;AACjC;AAEO,MAAM,8BAA8B,OAAO;IAChD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,8BACL,MAAM,CAAE,oDAIR,EAAE,CAAC,eAAe;IAErB,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,iCAAiC,OAAO,YAAoB;IACvE,oCAAoC;IACpC,MAAM,yHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,8BACL,MAAM,GACN,EAAE,CAAC,eAAe;IAErB,6BAA6B;IAC7B,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,MAAM,eAAe,YAAY,GAAG,CAAC,CAAA,aAAc,CAAC;gBAClD,aAAa;gBACb,aAAa;YACf,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,8BACL,MAAM,CAAC,cACP,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,OAAO;QAAE,MAAM,EAAE;QAAE,OAAO;IAAK;AACjC;AAGO,MAAM,iBAAiB,OAAO;IACnC,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,WAAW;IAEhE,IAAI,gBAAgB,CAAC,SAAS;QAC5B,OAAO;YAAE,MAAM;YAAM,OAAO;QAAa;IAC3C;IAEA,IAAI,QAAQ,SAAS,KAAK,cAAc;QACtC,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,cAAc;QACzE,OAAO;YACL,MAAM,aAAa;gBAAE,GAAG,OAAO;gBAAE;YAAW,IAAI;YAChD,OAAO;QACT;IACF,OAAO,IAAI,QAAQ,SAAS,KAAK,YAAY;QAC3C,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,YAAY;QACnE,OAAO;YACL,MAAM,WAAW;gBAAE,GAAG,OAAO;gBAAE;YAAS,IAAI;YAC5C,OAAO;QACT;IACF;IAEA,OAAO;QAAE,MAAM;QAAS,OAAO;IAAK;AACtC;AAGO,MAAM,eAAe,OAAO,QAAgB;IACjD,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;IACxC,MAAM,WAAW,AAAC,GAAY,OAAV,QAAO,KAAoB,OAAjB,KAAK,MAAM,IAAG,KAAW,OAAR;IAC/C,MAAM,WAAW,AAAC,WAAmB,OAAT;IAE5B,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,OAAO,CAClD,IAAI,CAAC,WACL,MAAM,CAAC,UAAU;IAEpB,IAAI,aAAa;QACf,OAAO;YAAE,MAAM;YAAM,OAAO;QAAY;IAC1C;IAEA,MAAM,EAAE,IAAI,EAAE,GAAG,yHAAA,CAAA,WAAQ,CAAC,OAAO,CAC9B,IAAI,CAAC,WACL,YAAY,CAAC;IAEhB,qCAAqC;IACrC,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,cAAc,QAAQ;QACzD,YAAY,KAAK,SAAS;IAC5B;IAEA,IAAI,aAAa;QACf,OAAO;YAAE,MAAM;YAAM,OAAO;QAAY;IAC1C;IAEA,OAAO;QAAE,MAAM,KAAK,SAAS;QAAE,OAAO;IAAK;AAC7C;AAGO,MAAM,yBAAyB,OAAO;IAC3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wBACL,MAAM,CAAE,yCAIR,EAAE,CAAC,iBAAiB,cACpB,EAAE,CAAC,aAAa;IAEnB,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,uBAAuB,OAAO;IACzC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,+BACL,MAAM,CAAE,mEAKR,EAAE,CAAC,iBAAiB,cACpB,EAAE,CAAC,gBAAgB;IAEtB,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,4BAA4B,OAAO,cAAsB;IACpE,mCAAmC;IACnC,MAAM,yHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,wBACL,MAAM,GACN,EAAE,CAAC,iBAAiB;IAEvB,4BAA4B;IAC5B,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,MAAM,eAAe,UAAU,GAAG,CAAC,CAAA,WAAY,CAAC;gBAC9C,eAAe;gBACf,aAAa,SAAS,WAAW;gBACjC,QAAQ,SAAS,MAAM,IAAI;gBAC3B,iBAAiB,SAAS,eAAe,IAAI;gBAC7C,aAAa;gBACb,WAAW;YACb,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wBACL,MAAM,CAAC,cACP,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,OAAO;QAAE,MAAM,EAAE;QAAE,OAAO;IAAK;AACjC;AAEO,MAAM,0BAA0B,OAAO,cAAsB;IAClE,iCAAiC;IACjC,MAAM,yHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,+BACL,MAAM,GACN,EAAE,CAAC,iBAAiB;IAEvB,0BAA0B;IAC1B,IAAI,QAAQ,MAAM,GAAG,GAAG;QACtB,MAAM,cAAc,QAAQ,GAAG,CAAC,CAAA,QAAS,CAAC;gBACxC,eAAe;gBACf,aAAa,MAAM,WAAW;gBAC9B,iBAAiB,MAAM,eAAe;gBACtC,OAAO,MAAM,KAAK;gBAClB,UAAU;gBACV,cAAc,MAAM,YAAY,KAAK;YACvC,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,+BACL,MAAM,CAAC,aACP,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,OAAO;QAAE,MAAM,EAAE;QAAE,OAAO;IAAK;AACjC", "debugId": null}}, {"offset": {"line": 3153, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,KAA8D;QAA9D,EAAE,GAAG,OAAyD,GAA9D;IACb,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,KAEgC;QAFhC,EACpB,GAAG,OACiD,GAFhC;IAGpB,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,KAEgC;QAFhC,EAClB,GAAG,OAC+C,GAFhC;IAGlB,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,KAGgC;QAHhC,EACpB,SAAS,EACT,GAAG,OACiD,GAHhC;IAIpB,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,KAOrB;QAPqB,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ,GAPqB;IAQpB,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAGgC;QAHhC,EAClB,SAAS,EACT,GAAG,OAC+C,GAHhC;IAIlB,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,KAGgC;QAHhC,EACxB,SAAS,EACT,GAAG,OACqD,GAHhC;IAIxB,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 3359, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/navigation/DesktopNavigation.tsx"], "sourcesContent": ["'use client';\n\nimport { usePathname, useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Button } from '@/components/ui/button';\nimport { cn } from '@/lib/utils';\nimport {\n  LayoutDashboard,\n  User,\n  Settings,\n  Building2,\n  Users,\n  FileText,\n  MessageCircle,\n  DollarSign,\n  LogOut,\n  Menu,\n  Send,\n  Inbox\n} from 'lucide-react';\nimport { useState } from 'react';\nimport {\n  She<PERSON>,\n  SheetContent,\n  SheetHeader,\n  SheetTitle,\n  SheetTrigger,\n} from '@/components/ui/sheet';\n\ninterface SidebarItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  description?: string;\n}\n\ninterface DesktopNavigationProps {\n  userType: 'influencer' | 'business';\n  className?: string;\n}\n\nexport function DesktopNavigation({ userType, className }: DesktopNavigationProps) {\n  const pathname = usePathname();\n  const { signOut } = useAuth();\n  const router = useRouter();\n  const [isOpen, setIsOpen] = useState(false);\n\n  // Navigacija za influencer korisnike\n  const influencerNavigation: SidebarItem[] = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard/influencer',\n      icon: LayoutDashboard,\n      description: 'Pregled aktivnosti i statistika'\n    },\n    {\n      name: 'Moj račun',\n      href: '/dashboard/influencer/account',\n      icon: User,\n      description: 'Lični podaci i sigurnost'\n    },\n    {\n      name: 'Postavke profila',\n      href: '/dashboard/influencer/profile',\n      icon: Settings,\n      description: 'Javni profil i cijene'\n    },\n    {\n      name: 'Kampanje',\n      href: '/marketplace/campaigns',\n      icon: FileText,\n      description: 'Dostupne kampanje'\n    },\n    {\n      name: 'Ponude i aplikacije',\n      href: '/dashboard/influencer/offers',\n      icon: Inbox,\n      description: 'Direktne ponude i aplikacije'\n    },\n    {\n      name: 'Poruke',\n      href: '/dashboard/chat',\n      icon: MessageCircle,\n      description: 'Komunikacija sa brendovima'\n    },\n    {\n      name: 'Zarada',\n      href: '/dashboard/influencer/earnings',\n      icon: DollarSign,\n      description: 'Pregled zarade i isplate'\n    }\n  ];\n\n  // Navigacija za biznis korisnike\n  const businessNavigation: SidebarItem[] = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard/biznis',\n      icon: LayoutDashboard,\n      description: 'Pregled kampanja i statistika'\n    },\n    {\n      name: 'Moj račun',\n      href: '/dashboard/biznis/account',\n      icon: Building2,\n      description: 'Podaci o firmi i sigurnost'\n    },\n    {\n      name: 'Postavke profila',\n      href: '/dashboard/biznis/profile',\n      icon: Settings,\n      description: 'Javni profil firme'\n    },\n    {\n      name: 'Moje kampanje',\n      href: '/dashboard/campaigns',\n      icon: FileText,\n      description: 'Upravljanje kampanjama'\n    },\n    {\n      name: 'Aplikacije',\n      href: '/dashboard/biznis/applications',\n      icon: FileText,\n      description: 'Aplikacije na kampanje'\n    },\n    {\n      name: 'Moje ponude',\n      href: '/dashboard/biznis/offers',\n      icon: Send,\n      description: 'Direktne ponude influencerima'\n    },\n    {\n      name: 'Influenceri',\n      href: '/marketplace/influencers',\n      icon: Users,\n      description: 'Pronađi influencere'\n    },\n    {\n      name: 'Poruke',\n      href: '/dashboard/chat',\n      icon: MessageCircle,\n      description: 'Komunikacija sa influencerima'\n    }\n  ];\n\n  const navigation = userType === 'influencer' ? influencerNavigation : businessNavigation;\n\n  const handleSignOut = async () => {\n    try {\n      await signOut();\n      router.push('/');\n      setIsOpen(false);\n    } catch (error) {\n      console.error('Error signing out:', error);\n    }\n  };\n\n  return (\n    <div className={cn(\"hidden md:block\", className)}>\n      {/* Desktop Navigation Trigger */}\n      <Sheet open={isOpen} onOpenChange={setIsOpen}>\n        <SheetTrigger asChild>\n          <Button\n            variant=\"outline\"\n            size=\"icon\"\n            className=\"fixed top-4 left-4 z-40 bg-card border-border shadow-lg hover:bg-accent\"\n          >\n            <Menu className=\"h-4 w-4\" />\n            <span className=\"sr-only\">Otvori navigaciju</span>\n          </Button>\n        </SheetTrigger>\n        \n        <SheetContent side=\"left\" className=\"w-80 p-0\">\n          <div className=\"flex flex-col h-full\">\n            {/* Header */}\n            <SheetHeader className=\"p-6 border-b border-border\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-primary rounded-lg flex items-center justify-center\">\n                  <span className=\"text-primary-foreground font-bold text-lg\">🔗</span>\n                </div>\n                <SheetTitle className=\"text-lg font-bold text-foreground\">\n                  InfluConnect\n                </SheetTitle>\n              </div>\n            </SheetHeader>\n\n            {/* Navigation */}\n            <nav className=\"flex-1 px-4 py-6 space-y-2 overflow-y-auto\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href || pathname.startsWith(item.href + '/');\n                \n                return (\n                  <Link \n                    key={item.name} \n                    href={item.href}\n                    onClick={() => setIsOpen(false)}\n                  >\n                    <div className={cn(\n                      \"group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-colors\",\n                      isActive\n                        ? \"bg-primary text-primary-foreground\"\n                        : \"text-muted-foreground hover:bg-accent hover:text-accent-foreground\"\n                    )}>\n                      <item.icon className=\"flex-shrink-0 h-5 w-5 mr-3\" />\n                      <div className=\"flex-1\">\n                        <div className=\"font-medium\">{item.name}</div>\n                        {item.description && (\n                          <div className=\"text-xs opacity-75 mt-0.5\">\n                            {item.description}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </Link>\n                );\n              })}\n            </nav>\n\n            {/* Footer */}\n            <div className=\"p-4 border-t border-border\">\n              <Button\n                variant=\"ghost\"\n                onClick={handleSignOut}\n                className=\"w-full justify-start text-destructive hover:text-destructive hover:bg-destructive/10\"\n              >\n                <LogOut className=\"h-4 w-4 mr-2\" />\n                Odjava\n              </Button>\n            </div>\n          </div>\n        </SheetContent>\n      </Sheet>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;;;AAtBA;;;;;;;;;AA0CO,SAAS,kBAAkB,KAA+C;QAA/C,EAAE,QAAQ,EAAE,SAAS,EAA0B,GAA/C;;IAChC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qCAAqC;IACrC,MAAM,uBAAsC;QAC1C;YACE,MAAM;YACN,MAAM;YACN,MAAM,+NAAA,CAAA,kBAAe;YACrB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,qMAAA,CAAA,OAAI;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,iNAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,uMAAA,CAAA,QAAK;YACX,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,2NAAA,CAAA,gBAAa;YACnB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,qNAAA,CAAA,aAAU;YAChB,aAAa;QACf;KACD;IAED,iCAAiC;IACjC,MAAM,qBAAoC;QACxC;YACE,MAAM;YACN,MAAM;YACN,MAAM,+NAAA,CAAA,kBAAe;YACrB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,mNAAA,CAAA,YAAS;YACf,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,iNAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,iNAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,qMAAA,CAAA,OAAI;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,uMAAA,CAAA,QAAK;YACX,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,2NAAA,CAAA,gBAAa;YACnB,aAAa;QACf;KACD;IAED,MAAM,aAAa,aAAa,eAAe,uBAAuB;IAEtE,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;YACZ,UAAU;QACZ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;kBAEpC,cAAA,6LAAC,oIAAA,CAAA,QAAK;YAAC,MAAM;YAAQ,cAAc;;8BACjC,6LAAC,oIAAA,CAAA,eAAY;oBAAC,OAAO;8BACnB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;8BAI9B,6LAAC,oIAAA,CAAA,eAAY;oBAAC,MAAK;oBAAO,WAAU;8BAClC,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,oIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;sDAE9D,6LAAC,oIAAA,CAAA,aAAU;4CAAC,WAAU;sDAAoC;;;;;;;;;;;;;;;;;0CAO9D,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,WAAW,aAAa,KAAK,IAAI,IAAI,SAAS,UAAU,CAAC,KAAK,IAAI,GAAG;oCAE3E,qBACE,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,SAAS,IAAM,UAAU;kDAEzB,cAAA,6LAAC;4CAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,sFACA,WACI,uCACA;;8DAEJ,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;8DACrB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAe,KAAK,IAAI;;;;;;wDACtC,KAAK,WAAW,kBACf,6LAAC;4DAAI,WAAU;sEACZ,KAAK,WAAW;;;;;;;;;;;;;;;;;;uCAfpB,KAAK,IAAI;;;;;gCAsBpB;;;;;;0CAIF,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD;GAjMgB;;QACG,qIAAA,CAAA,cAAW;QACR,kIAAA,CAAA,UAAO;QACZ,qIAAA,CAAA,YAAS;;;KAHV", "debugId": null}}, {"offset": {"line": 3717, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/navigation/MobileBottomNavigation.tsx"], "sourcesContent": ["'use client';\n\nimport { usePathname } from 'next/navigation';\nimport Link from 'next/link';\nimport { cn } from '@/lib/utils';\nimport {\n  LayoutDashboard,\n  FileText,\n  Inbox,\n  MessageCircle,\n  Menu,\n  User,\n  Settings,\n  DollarSign,\n  LogOut\n} from 'lucide-react';\nimport { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport {\n  Sheet,\n  <PERSON>et<PERSON>ontent,\n  SheetHeader,\n  SheetTitle,\n  SheetTrigger,\n} from '@/components/ui/sheet';\nimport { Button } from '@/components/ui/button';\n\ninterface MobileBottomNavigationProps {\n  userType: 'influencer' | 'business';\n}\n\ninterface NavItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  label: string;\n}\n\ninterface MenuNavItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  description?: string;\n}\n\nexport function MobileBottomNavigation({ userType }: MobileBottomNavigationProps) {\n  const pathname = usePathname();\n  const { signOut } = useAuth();\n  const router = useRouter();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  // Glavne 4 ikonice za influencere\n  const influencerMainNav: NavItem[] = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard/influencer',\n      icon: LayoutDashboard,\n      label: 'Home'\n    },\n    {\n      name: 'Kampanje',\n      href: '/marketplace/campaigns',\n      icon: FileText,\n      label: 'Prilike'\n    },\n    {\n      name: 'Ponude',\n      href: '/dashboard/influencer/offers',\n      icon: Inbox,\n      label: 'Ponude'\n    },\n    {\n      name: 'Poruke',\n      href: '/dashboard/chat',\n      icon: MessageCircle,\n      label: 'Poruke'\n    }\n  ];\n\n  // Glavne 4 ikonice za biznis korisnike\n  const businessMainNav: NavItem[] = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard/biznis',\n      icon: LayoutDashboard,\n      label: 'Home'\n    },\n    {\n      name: 'Kampanje',\n      href: '/dashboard/campaigns',\n      icon: FileText,\n      label: 'Kampanje'\n    },\n    {\n      name: 'Aplikacije',\n      href: '/dashboard/biznis/applications',\n      icon: Inbox,\n      label: 'Aplikacije'\n    },\n    {\n      name: 'Poruke',\n      href: '/dashboard/chat',\n      icon: MessageCircle,\n      label: 'Poruke'\n    }\n  ];\n\n  // Ostali elementi za hamburger meni - influenceri\n  const influencerMenuNav: MenuNavItem[] = [\n    {\n      name: 'Moj račun',\n      href: '/dashboard/influencer/account',\n      icon: User,\n      description: 'Lični podaci i sigurnost'\n    },\n    {\n      name: 'Postavke profila',\n      href: '/dashboard/influencer/profile',\n      icon: Settings,\n      description: 'Javni profil i cijene'\n    },\n    {\n      name: 'Zarada',\n      href: '/dashboard/influencer/earnings',\n      icon: DollarSign,\n      description: 'Pregled zarade i isplate'\n    }\n  ];\n\n  // Ostali elementi za hamburger meni - biznis\n  const businessMenuNav: MenuNavItem[] = [\n    {\n      name: 'Moj račun',\n      href: '/dashboard/biznis/account',\n      icon: User,\n      description: 'Podaci o firmi i sigurnost'\n    },\n    {\n      name: 'Postavke profila',\n      href: '/dashboard/biznis/profile',\n      icon: Settings,\n      description: 'Javni profil firme'\n    },\n    {\n      name: 'Influenceri',\n      href: '/marketplace/influencers',\n      icon: User,\n      description: 'Pronađi influencere'\n    }\n  ];\n\n  const mainNavigation = userType === 'influencer' ? influencerMainNav : businessMainNav;\n  const menuNavigation = userType === 'influencer' ? influencerMenuNav : businessMenuNav;\n\n  const handleSignOut = async () => {\n    try {\n      await signOut();\n      router.push('/');\n      setIsMenuOpen(false);\n    } catch (error) {\n      console.error('Error signing out:', error);\n    }\n  };\n\n  const isActive = (href: string) => {\n    return pathname === href || pathname.startsWith(href + '/');\n  };\n\n  return (\n    <>\n      {/* Mobile Bottom Navigation */}\n      <div className=\"fixed bottom-0 left-0 right-0 z-50 bg-card border-t border-border md:hidden\">\n        <div className=\"flex items-center justify-around py-2\">\n          {/* Glavne 4 ikonice */}\n          {mainNavigation.map((item) => (\n            <Link key={item.name} href={item.href}>\n              <div className={cn(\n                \"flex flex-col items-center justify-center p-2 min-w-[60px] rounded-lg transition-colors\",\n                isActive(item.href)\n                  ? \"text-primary bg-primary/10\"\n                  : \"text-muted-foreground hover:text-foreground hover:bg-accent\"\n              )}>\n                <item.icon className=\"h-5 w-5 mb-1\" />\n                <span className=\"text-xs font-medium\">{item.label}</span>\n              </div>\n            </Link>\n          ))}\n\n          {/* Hamburger Menu */}\n          <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>\n            <SheetTrigger asChild>\n              <div className=\"flex flex-col items-center justify-center p-2 min-w-[60px] rounded-lg transition-colors text-muted-foreground hover:text-foreground hover:bg-accent cursor-pointer\">\n                <Menu className=\"h-5 w-5 mb-1\" />\n                <span className=\"text-xs font-medium\">Više</span>\n              </div>\n            </SheetTrigger>\n            <SheetContent side=\"bottom\" className=\"h-auto max-h-[80vh]\">\n              <SheetHeader>\n                <SheetTitle>Meni</SheetTitle>\n              </SheetHeader>\n              \n              <div className=\"grid gap-4 py-4\">\n                {menuNavigation.map((item) => (\n                  <Link \n                    key={item.name} \n                    href={item.href}\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    <div className={cn(\n                      \"flex items-center space-x-3 p-3 rounded-lg transition-colors\",\n                      isActive(item.href)\n                        ? \"bg-primary text-primary-foreground\"\n                        : \"hover:bg-accent\"\n                    )}>\n                      <item.icon className=\"h-5 w-5\" />\n                      <div className=\"flex-1\">\n                        <div className=\"font-medium\">{item.name}</div>\n                        {item.description && (\n                          <div className=\"text-sm opacity-75 mt-0.5\">\n                            {item.description}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </Link>\n                ))}\n                \n                {/* Odjava */}\n                <Button\n                  variant=\"ghost\"\n                  onClick={handleSignOut}\n                  className=\"flex items-center justify-start space-x-3 p-3 w-full text-destructive hover:text-destructive hover:bg-destructive/10\"\n                >\n                  <LogOut className=\"h-5 w-5\" />\n                  <span className=\"font-medium\">Odjava</span>\n                </Button>\n              </div>\n            </SheetContent>\n          </Sheet>\n        </div>\n      </div>\n\n      {/* Spacer za bottom navigation */}\n      <div className=\"h-16 md:hidden\" />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AAEA;AAOA;;;AA1BA;;;;;;;;;;AA8CO,SAAS,uBAAuB,KAAyC;QAAzC,EAAE,QAAQ,EAA+B,GAAzC;;IACrC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,kCAAkC;IAClC,MAAM,oBAA+B;QACnC;YACE,MAAM;YACN,MAAM;YACN,MAAM,+NAAA,CAAA,kBAAe;YACrB,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,iNAAA,CAAA,WAAQ;YACd,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,2NAAA,CAAA,gBAAa;YACnB,OAAO;QACT;KACD;IAED,uCAAuC;IACvC,MAAM,kBAA6B;QACjC;YACE,MAAM;YACN,MAAM;YACN,MAAM,+NAAA,CAAA,kBAAe;YACrB,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,iNAAA,CAAA,WAAQ;YACd,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,2NAAA,CAAA,gBAAa;YACnB,OAAO;QACT;KACD;IAED,kDAAkD;IAClD,MAAM,oBAAmC;QACvC;YACE,MAAM;YACN,MAAM;YACN,MAAM,qMAAA,CAAA,OAAI;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,qNAAA,CAAA,aAAU;YAChB,aAAa;QACf;KACD;IAED,6CAA6C;IAC7C,MAAM,kBAAiC;QACrC;YACE,MAAM;YACN,MAAM;YACN,MAAM,qMAAA,CAAA,OAAI;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,qMAAA,CAAA,OAAI;YACV,aAAa;QACf;KACD;IAED,MAAM,iBAAiB,aAAa,eAAe,oBAAoB;IACvE,MAAM,iBAAiB,aAAa,eAAe,oBAAoB;IAEvE,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;YACZ,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,OAAO,aAAa,QAAQ,SAAS,UAAU,CAAC,OAAO;IACzD;IAEA,qBACE;;0BAEE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBAEZ,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC,+JAAA,CAAA,UAAI;gCAAiB,MAAM,KAAK,IAAI;0CACnC,cAAA,6LAAC;oCAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,2FACA,SAAS,KAAK,IAAI,IACd,+BACA;;sDAEJ,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAK,WAAU;sDAAuB,KAAK,KAAK;;;;;;;;;;;;+BAR1C,KAAK,IAAI;;;;;sCActB,6LAAC,oIAAA,CAAA,QAAK;4BAAC,MAAM;4BAAY,cAAc;;8CACrC,6LAAC,oIAAA,CAAA,eAAY;oCAAC,OAAO;8CACnB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;;;;;;8CAG1C,6LAAC,oIAAA,CAAA,eAAY;oCAAC,MAAK;oCAAS,WAAU;;sDACpC,6LAAC,oIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC,oIAAA,CAAA,aAAU;0DAAC;;;;;;;;;;;sDAGd,6LAAC;4CAAI,WAAU;;gDACZ,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC,+JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,SAAS,IAAM,cAAc;kEAE7B,cAAA,6LAAC;4DAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,gEACA,SAAS,KAAK,IAAI,IACd,uCACA;;8EAEJ,6LAAC,KAAK,IAAI;oEAAC,WAAU;;;;;;8EACrB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFAAe,KAAK,IAAI;;;;;;wEACtC,KAAK,WAAW,kBACf,6LAAC;4EAAI,WAAU;sFACZ,KAAK,WAAW;;;;;;;;;;;;;;;;;;uDAfpB,KAAK,IAAI;;;;;8DAwBlB,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS;oDACT,WAAU;;sEAEV,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS1C,6LAAC;gBAAI,WAAU;;;;;;;;AAGrB;GAzMgB;;QACG,qIAAA,CAAA,cAAW;QACR,kIAAA,CAAA,UAAO;QACZ,qIAAA,CAAA,YAAS;;;KAHV", "debugId": null}}, {"offset": {"line": 4092, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/navigation/ResponsiveNavigation.tsx"], "sourcesContent": ["'use client';\n\nimport { DesktopNavigation } from './DesktopNavigation';\nimport { MobileBottomNavigation } from './MobileBottomNavigation';\n\ninterface ResponsiveNavigationProps {\n  userType: 'influencer' | 'business';\n}\n\nexport function ResponsiveNavigation({ userType }: ResponsiveNavigationProps) {\n  return (\n    <>\n      {/* Desktop Navigation - prikazuje se samo na desktop uređajima */}\n      <DesktopNavigation userType={userType} />\n      \n      {/* Mobile Bottom Navigation - prikazuje se samo na mobile uređajima */}\n      <MobileBottomNavigation userType={userType} />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASO,SAAS,qBAAqB,KAAuC;QAAvC,EAAE,QAAQ,EAA6B,GAAvC;IACnC,qBACE;;0BAEE,6LAAC,wJAAA,CAAA,oBAAiB;gBAAC,UAAU;;;;;;0BAG7B,6LAAC,6JAAA,CAAA,yBAAsB;gBAAC,UAAU;;;;;;;;AAGxC;KAVgB", "debugId": null}}, {"offset": {"line": 4135, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction ScrollArea({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\n  return (\n    <ScrollAreaPrimitive.Root\n      data-slot=\"scroll-area\"\n      className={cn(\"relative\", className)}\n      {...props}\n    >\n      <ScrollAreaPrimitive.Viewport\n        data-slot=\"scroll-area-viewport\"\n        className=\"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1\"\n      >\n        {children}\n      </ScrollAreaPrimitive.Viewport>\n      <ScrollBar />\n      <ScrollAreaPrimitive.Corner />\n    </ScrollAreaPrimitive.Root>\n  )\n}\n\nfunction ScrollBar({\n  className,\n  orientation = \"vertical\",\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\n  return (\n    <ScrollAreaPrimitive.ScrollAreaScrollbar\n      data-slot=\"scroll-area-scrollbar\"\n      orientation={orientation}\n      className={cn(\n        \"flex touch-none p-px transition-colors select-none\",\n        orientation === \"vertical\" &&\n          \"h-full w-2.5 border-l border-l-transparent\",\n        orientation === \"horizontal\" &&\n          \"h-2.5 flex-col border-t border-t-transparent\",\n        className\n      )}\n      {...props}\n    >\n      <ScrollAreaPrimitive.ScrollAreaThumb\n        data-slot=\"scroll-area-thumb\"\n        className=\"bg-border relative flex-1 rounded-full\"\n      />\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>\n  )\n}\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,WAAW,KAIoC;QAJpC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OACmD,GAJpC;IAKlB,qBACE,6LAAC,6KAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;0BAET,6LAAC,6KAAA,CAAA,WAA4B;gBAC3B,aAAU;gBACV,WAAU;0BAET;;;;;;0BAEH,6LAAC;;;;;0BACD,6LAAC,6KAAA,CAAA,SAA0B;;;;;;;;;;;AAGjC;KArBS;AAuBT,SAAS,UAAU,KAIoD;QAJpD,EACjB,SAAS,EACT,cAAc,UAAU,EACxB,GAAG,OACkE,GAJpD;IAKjB,qBACE,6LAAC,6KAAA,CAAA,sBAAuC;QACtC,aAAU;QACV,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA,gBAAgB,cACd,8CACF,gBAAgB,gBACd,gDACF;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,kBAAmC;YAClC,aAAU;YACV,WAAU;;;;;;;;;;;AAIlB;MAzBS", "debugId": null}}, {"offset": {"line": 4215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,KAEoC;QAFpC,EACpB,GAAG,OACqD,GAFpC;IAGpB,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,KAEgC;QAFhC,EAC1B,GAAG,OACuD,GAFhC;IAG1B,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,KAEgC;QAFhC,EAC3B,GAAG,OACwD,GAFhC;IAG3B,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,KAIgC;QAJhC,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD,GAJhC;IAK3B,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,KAEgC;QAFhC,EACzB,GAAG,OACsD,GAFhC;IAGzB,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,KAQzB;QARyB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ,GARyB;IASxB,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,KAKgC;QALhC,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D,GALhC;IAMhC,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,KAEgC;QAFhC,EAC9B,GAAG,OAC2D,GAFhC;IAG9B,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,KAIgC;QAJhC,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D,GAJhC;IAK7B,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,KAM1B;QAN0B,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ,GAN0B;IAOzB,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,KAGgC;QAHhC,EAC7B,SAAS,EACT,GAAG,OAC0D,GAHhC;IAI7B,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,KAGC;QAHD,EAC5B,SAAS,EACT,GAAG,OAC0B,GAHD;IAI5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,KAEgC;QAFhC,EACvB,GAAG,OACoD,GAFhC;IAGvB,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,KAO/B;QAP+B,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ,GAP+B;IAQ9B,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 4526, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/notifications.ts"], "sourcesContent": ["import { supabase } from './supabase';\n\nexport interface Notification {\n  id: string;\n  user_id: string;\n  type: string;\n  title: string;\n  message: string;\n  data: Record<string, any>;\n  read: boolean;\n  created_at: string;\n  updated_at: string;\n}\n\nexport type NotificationType = \n  | 'offer_received'\n  | 'offer_accepted'\n  | 'offer_rejected'\n  | 'campaign_application'\n  | 'campaign_accepted'\n  | 'campaign_rejected'\n  | 'message_received'\n  | 'payment_received';\n\n// Create a new notification\nexport async function createNotification(\n  userId: string,\n  type: NotificationType,\n  title: string,\n  message: string,\n  data: Record<string, any> = {}\n) {\n  const { data: notification, error } = await supabase.rpc('create_notification', {\n    p_user_id: userId,\n    p_type: type,\n    p_title: title,\n    p_message: message,\n    p_data: data\n  });\n\n  if (error) {\n    console.error('Error creating notification:', error);\n    return { data: null, error };\n  }\n\n  return { data: notification, error: null };\n}\n\n// Get user notifications\nexport async function getUserNotifications(userId?: string, limit = 50) {\n  let query = supabase\n    .from('notifications')\n    .select('*')\n    .order('created_at', { ascending: false })\n    .limit(limit);\n\n  if (userId) {\n    query = query.eq('user_id', userId);\n  }\n\n  const { data, error } = await query;\n\n  if (error) {\n    console.error('Error fetching notifications:', error);\n    return { data: null, error };\n  }\n\n  return { data, error: null };\n}\n\n// Mark notification as read\nexport async function markNotificationAsRead(notificationId: string) {\n  const { data, error } = await supabase\n    .from('notifications')\n    .update({ read: true })\n    .eq('id', notificationId)\n    .select()\n    .single();\n\n  if (error) {\n    console.error('Error marking notification as read:', error);\n    return { data: null, error };\n  }\n\n  return { data, error: null };\n}\n\n// Mark all notifications as read for user\nexport async function markAllNotificationsAsRead(userId: string) {\n  const { data, error } = await supabase\n    .from('notifications')\n    .update({ read: true })\n    .eq('user_id', userId)\n    .eq('read', false);\n\n  if (error) {\n    console.error('Error marking all notifications as read:', error);\n    return { data: null, error };\n  }\n\n  return { data, error: null };\n}\n\n// Get unread notification count\nexport async function getUnreadNotificationCount(userId: string) {\n  const { count, error } = await supabase\n    .from('notifications')\n    .select('*', { count: 'exact', head: true })\n    .eq('user_id', userId)\n    .eq('read', false);\n\n  if (error) {\n    console.error('Error getting unread count:', error);\n    return { count: 0, error };\n  }\n\n  return { count: count || 0, error: null };\n}\n\n// Delete notification\nexport async function deleteNotification(notificationId: string) {\n  const { error } = await supabase\n    .from('notifications')\n    .delete()\n    .eq('id', notificationId);\n\n  if (error) {\n    console.error('Error deleting notification:', error);\n    return { error };\n  }\n\n  return { error: null };\n}\n\n// Helper functions for specific notification types\n\nexport async function notifyOfferReceived(\n  influencerId: string,\n  businessName: string,\n  offerTitle: string,\n  offerId: string\n) {\n  return createNotification(\n    influencerId,\n    'offer_received',\n    'Nova direktna ponuda',\n    `${businessName} vam je poslao ponudu: \"${offerTitle}\"`,\n    { offer_id: offerId, business_name: businessName }\n  );\n}\n\nexport async function notifyOfferAccepted(\n  businessId: string,\n  influencerName: string,\n  offerTitle: string,\n  offerId: string\n) {\n  return createNotification(\n    businessId,\n    'offer_accepted',\n    'Ponuda prihvaćena',\n    `${influencerName} je prihvatio vašu ponudu: \"${offerTitle}\"`,\n    { offer_id: offerId, influencer_name: influencerName }\n  );\n}\n\nexport async function notifyOfferRejected(\n  businessId: string,\n  influencerName: string,\n  offerTitle: string,\n  offerId: string\n) {\n  return createNotification(\n    businessId,\n    'offer_rejected',\n    'Ponuda odbijena',\n    `${influencerName} je odbio vašu ponudu: \"${offerTitle}\"`,\n    { offer_id: offerId, influencer_name: influencerName }\n  );\n}\n\nexport async function notifyCampaignApplication(\n  businessId: string,\n  influencerName: string,\n  campaignTitle: string,\n  applicationId: string\n) {\n  return createNotification(\n    businessId,\n    'campaign_application',\n    'Nova aplikacija na kampanju',\n    `${influencerName} se prijavio na kampanju: \"${campaignTitle}\"`,\n    { application_id: applicationId, influencer_name: influencerName }\n  );\n}\n\nexport async function notifyCampaignAccepted(\n  influencerId: string,\n  businessName: string,\n  campaignTitle: string,\n  applicationId: string\n) {\n  return createNotification(\n    influencerId,\n    'campaign_accepted',\n    'Aplikacija prihvaćena',\n    `${businessName} je prihvatio vašu aplikaciju za kampanju: \"${campaignTitle}\"`,\n    { application_id: applicationId, business_name: businessName }\n  );\n}\n\nexport async function notifyCampaignRejected(\n  influencerId: string,\n  businessName: string,\n  campaignTitle: string,\n  applicationId: string\n) {\n  return createNotification(\n    influencerId,\n    'campaign_rejected',\n    'Aplikacija odbijena',\n    `${businessName} je odbio vašu aplikaciju za kampanju: \"${campaignTitle}\"`,\n    { application_id: applicationId, business_name: businessName }\n  );\n}\n\nexport async function notifyMessageReceived(\n  userId: string,\n  senderName: string,\n  conversationId: string\n) {\n  return createNotification(\n    userId,\n    'message_received',\n    'Nova poruka',\n    `${senderName} vam je poslao novu poruku`,\n    { conversation_id: conversationId, sender_name: senderName }\n  );\n}\n\nexport async function notifyPaymentReceived(\n  influencerId: string,\n  amount: number,\n  currency: string,\n  campaignTitle: string\n) {\n  return createNotification(\n    influencerId,\n    'payment_received',\n    'Plaćanje primljeno',\n    `Primili ste plaćanje od ${amount} ${currency} za kampanju: \"${campaignTitle}\"`,\n    { amount, currency, campaign_title: campaignTitle }\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;;AAyBO,eAAe,mBACpB,MAAc,EACd,IAAsB,EACtB,KAAa,EACb,OAAe;QACf,OAAA,iEAA4B,CAAC;IAE7B,MAAM,EAAE,MAAM,YAAY,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,uBAAuB;QAC9E,WAAW;QACX,QAAQ;QACR,SAAS;QACT,WAAW;QACX,QAAQ;IACV;IAEA,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,OAAO;QAAE,MAAM;QAAc,OAAO;IAAK;AAC3C;AAGO,eAAe,qBAAqB,MAAe;QAAE,QAAA,iEAAQ;IAClE,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM,GACvC,KAAK,CAAC;IAET,IAAI,QAAQ;QACV,QAAQ,MAAM,EAAE,CAAC,WAAW;IAC9B;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAE9B,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,OAAO;QAAE;QAAM,OAAO;IAAK;AAC7B;AAGO,eAAe,uBAAuB,cAAsB;IACjE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC;QAAE,MAAM;IAAK,GACpB,EAAE,CAAC,MAAM,gBACT,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,OAAO;QAAE;QAAM,OAAO;IAAK;AAC7B;AAGO,eAAe,2BAA2B,MAAc;IAC7D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC;QAAE,MAAM;IAAK,GACpB,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,QAAQ;IAEd,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,OAAO;QAAE;QAAM,OAAO;IAAK;AAC7B;AAGO,eAAe,2BAA2B,MAAc;IAC7D,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACpC,IAAI,CAAC,iBACL,MAAM,CAAC,KAAK;QAAE,OAAO;QAAS,MAAM;IAAK,GACzC,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,QAAQ;IAEd,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YAAE,OAAO;YAAG;QAAM;IAC3B;IAEA,OAAO;QAAE,OAAO,SAAS;QAAG,OAAO;IAAK;AAC1C;AAGO,eAAe,mBAAmB,cAAsB;IAC7D,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,iBACL,MAAM,GACN,EAAE,CAAC,MAAM;IAEZ,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YAAE;QAAM;IACjB;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB;AAIO,eAAe,oBACpB,YAAoB,EACpB,YAAoB,EACpB,UAAkB,EAClB,OAAe;IAEf,OAAO,mBACL,cACA,kBACA,wBACA,AAAC,GAAyC,OAAvC,cAAa,4BAAqC,OAAX,YAAW,MACrD;QAAE,UAAU;QAAS,eAAe;IAAa;AAErD;AAEO,eAAe,oBACpB,UAAkB,EAClB,cAAsB,EACtB,UAAkB,EAClB,OAAe;IAEf,OAAO,mBACL,YACA,kBACA,qBACA,AAAC,GAA+C,OAA7C,gBAAe,gCAAyC,OAAX,YAAW,MAC3D;QAAE,UAAU;QAAS,iBAAiB;IAAe;AAEzD;AAEO,eAAe,oBACpB,UAAkB,EAClB,cAAsB,EACtB,UAAkB,EAClB,OAAe;IAEf,OAAO,mBACL,YACA,kBACA,mBACA,AAAC,GAA2C,OAAzC,gBAAe,4BAAqC,OAAX,YAAW,MACvD;QAAE,UAAU;QAAS,iBAAiB;IAAe;AAEzD;AAEO,eAAe,0BACpB,UAAkB,EAClB,cAAsB,EACtB,aAAqB,EACrB,aAAqB;IAErB,OAAO,mBACL,YACA,wBACA,+BACA,AAAC,GAA8C,OAA5C,gBAAe,+BAA2C,OAAd,eAAc,MAC7D;QAAE,gBAAgB;QAAe,iBAAiB;IAAe;AAErE;AAEO,eAAe,uBACpB,YAAoB,EACpB,YAAoB,EACpB,aAAqB,EACrB,aAAqB;IAErB,OAAO,mBACL,cACA,qBACA,yBACA,AAAC,GAA6D,OAA3D,cAAa,gDAA4D,OAAd,eAAc,MAC5E;QAAE,gBAAgB;QAAe,eAAe;IAAa;AAEjE;AAEO,eAAe,uBACpB,YAAoB,EACpB,YAAoB,EACpB,aAAqB,EACrB,aAAqB;IAErB,OAAO,mBACL,cACA,qBACA,uBACA,AAAC,GAAyD,OAAvD,cAAa,4CAAwD,OAAd,eAAc,MACxE;QAAE,gBAAgB;QAAe,eAAe;IAAa;AAEjE;AAEO,eAAe,sBACpB,MAAc,EACd,UAAkB,EAClB,cAAsB;IAEtB,OAAO,mBACL,QACA,oBACA,eACA,AAAC,GAAa,OAAX,YAAW,+BACd;QAAE,iBAAiB;QAAgB,aAAa;IAAW;AAE/D;AAEO,eAAe,sBACpB,YAAoB,EACpB,MAAc,EACd,QAAgB,EAChB,aAAqB;IAErB,OAAO,mBACL,cACA,oBACA,sBACA,AAAC,2BAAoC,OAAV,QAAO,KAA6B,OAA1B,UAAS,mBAA+B,OAAd,eAAc,MAC7E;QAAE;QAAQ;QAAU,gBAAgB;IAAc;AAEtD", "debugId": null}}, {"offset": {"line": 4705, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/notifications/NotificationDropdown.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuLabel,\n  DropdownMenuTrigger,\n  DropdownMenuSeparator,\n  DropdownMenuItem,\n} from '@/components/ui/dropdown-menu';\nimport { \n  Bell, \n  Check, \n  CheckCheck,\n  Inbox,\n  MessageCircle,\n  DollarSign,\n  FileText,\n  Building2,\n  User\n} from 'lucide-react';\nimport { \n  getUserNotifications, \n  markNotificationAsRead, \n  markAllNotificationsAsRead,\n  getUnreadNotificationCount,\n  type Notification \n} from '@/lib/notifications';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { formatDistanceToNow } from 'date-fns';\nimport { hr } from 'date-fns/locale';\nimport { toast } from 'sonner';\nimport Link from 'next/link';\n\nexport function NotificationDropdown() {\n  const { user } = useAuth();\n  const [notifications, setNotifications] = useState<Notification[]>([]);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isOpen, setIsOpen] = useState(false);\n\n  useEffect(() => {\n    if (user) {\n      loadNotifications();\n      loadUnreadCount();\n    }\n  }, [user]);\n\n  const loadNotifications = async () => {\n    if (!user) return;\n    \n    setIsLoading(true);\n    try {\n      const { data, error } = await getUserNotifications(user.id, 20);\n      if (error) {\n        console.error('Error loading notifications:', error);\n      } else {\n        setNotifications(data || []);\n      }\n    } catch (error) {\n      console.error('Error loading notifications:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loadUnreadCount = async () => {\n    if (!user) return;\n    \n    try {\n      const { count, error } = await getUnreadNotificationCount(user.id);\n      if (error) {\n        console.error('Error loading unread count:', error);\n      } else {\n        setUnreadCount(count);\n      }\n    } catch (error) {\n      console.error('Error loading unread count:', error);\n    }\n  };\n\n  const handleMarkAsRead = async (notificationId: string) => {\n    try {\n      const { error } = await markNotificationAsRead(notificationId);\n      if (error) {\n        toast.error('Greška pri označavanju notifikacije');\n      } else {\n        setNotifications(prev => \n          prev.map(n => n.id === notificationId ? { ...n, read: true } : n)\n        );\n        setUnreadCount(prev => Math.max(0, prev - 1));\n      }\n    } catch (error) {\n      console.error('Error marking notification as read:', error);\n      toast.error('Greška pri označavanju notifikacije');\n    }\n  };\n\n  const handleMarkAllAsRead = async () => {\n    if (!user) return;\n    \n    try {\n      const { error } = await markAllNotificationsAsRead(user.id);\n      if (error) {\n        toast.error('Greška pri označavanju notifikacija');\n      } else {\n        setNotifications(prev => prev.map(n => ({ ...n, read: true })));\n        setUnreadCount(0);\n        toast.success('Sve notifikacije su označene kao pročitane');\n      }\n    } catch (error) {\n      console.error('Error marking all notifications as read:', error);\n      toast.error('Greška pri označavanju notifikacija');\n    }\n  };\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'offer_received':\n      case 'offer_accepted':\n      case 'offer_rejected':\n        return <Inbox className=\"h-4 w-4\" />;\n      case 'campaign_application':\n      case 'campaign_accepted':\n      case 'campaign_rejected':\n        return <FileText className=\"h-4 w-4\" />;\n      case 'message_received':\n        return <MessageCircle className=\"h-4 w-4\" />;\n      case 'payment_received':\n        return <DollarSign className=\"h-4 w-4\" />;\n      default:\n        return <Bell className=\"h-4 w-4\" />;\n    }\n  };\n\n  const getNotificationLink = (notification: Notification) => {\n    switch (notification.type) {\n      case 'offer_received':\n      case 'offer_accepted':\n      case 'offer_rejected':\n        if (notification.data.offer_id) {\n          return `/dashboard/influencer/offers/${notification.data.offer_id}`;\n        }\n        return '/dashboard/influencer/offers';\n      case 'campaign_application':\n      case 'campaign_accepted':\n      case 'campaign_rejected':\n        return '/dashboard/campaigns';\n      case 'message_received':\n        return '/dashboard/messages';\n      default:\n        return '/dashboard';\n    }\n  };\n\n  if (!user) return null;\n\n  return (\n    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n          <Bell className=\"h-5 w-5\" />\n          {unreadCount > 0 && (\n            <Badge \n              variant=\"destructive\" \n              className=\"absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n            >\n              {unreadCount > 99 ? '99+' : unreadCount}\n            </Badge>\n          )}\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\" className=\"w-80\">\n        <DropdownMenuLabel className=\"flex items-center justify-between p-4\">\n          <h3 className=\"font-semibold\">Notifikacije</h3>\n          {unreadCount > 0 && (\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={handleMarkAllAsRead}\n              className=\"text-xs\"\n            >\n              <CheckCheck className=\"h-3 w-3 mr-1\" />\n              Označi sve\n            </Button>\n          )}\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        \n        <ScrollArea className=\"h-96\">\n          {isLoading ? (\n            <div className=\"flex items-center justify-center p-8\">\n              <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-primary\"></div>\n            </div>\n          ) : notifications.length === 0 ? (\n            <div className=\"flex flex-col items-center justify-center p-8 text-center\">\n              <Bell className=\"h-8 w-8 text-muted-foreground mb-2\" />\n              <p className=\"text-sm text-muted-foreground\">Nema novih notifikacija</p>\n            </div>\n          ) : (\n            <div className=\"space-y-1\">\n              {notifications.map((notification) => (\n                <div\n                  key={notification.id}\n                  className={`p-3 hover:bg-muted/50 transition-colors border-l-2 ${\n                    notification.read ? 'border-transparent' : 'border-primary'\n                  }`}\n                >\n                  <Link \n                    href={getNotificationLink(notification)}\n                    onClick={() => {\n                      if (!notification.read) {\n                        handleMarkAsRead(notification.id);\n                      }\n                      setIsOpen(false);\n                    }}\n                    className=\"block\"\n                  >\n                    <div className=\"flex items-start gap-3\">\n                      <div className=\"flex-shrink-0 mt-0.5\">\n                        {getNotificationIcon(notification.type)}\n                      </div>\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-start justify-between\">\n                          <h4 className={`text-sm font-medium ${\n                            notification.read ? 'text-muted-foreground' : 'text-foreground'\n                          }`}>\n                            {notification.title}\n                          </h4>\n                          {!notification.read && (\n                            <Button\n                              variant=\"ghost\"\n                              size=\"sm\"\n                              onClick={(e) => {\n                                e.preventDefault();\n                                e.stopPropagation();\n                                handleMarkAsRead(notification.id);\n                              }}\n                              className=\"h-6 w-6 p-0 ml-2\"\n                            >\n                              <Check className=\"h-3 w-3\" />\n                            </Button>\n                          )}\n                        </div>\n                        <p className={`text-xs mt-1 ${\n                          notification.read ? 'text-muted-foreground' : 'text-muted-foreground'\n                        }`}>\n                          {notification.message}\n                        </p>\n                        <p className=\"text-xs text-muted-foreground mt-1\">\n                          {formatDistanceToNow(new Date(notification.created_at), { \n                            addSuffix: true, \n                            locale: hr \n                          })}\n                        </p>\n                      </div>\n                    </div>\n                  </Link>\n                </div>\n              ))}\n            </div>\n          )}\n        </ScrollArea>\n        \n        {notifications.length > 0 && (\n          <>\n            <DropdownMenuSeparator />\n            <div className=\"p-2\">\n              <Button variant=\"ghost\" size=\"sm\" className=\"w-full\" asChild>\n                <Link href=\"/dashboard/notifications\">\n                  Pogledaj sve notifikacije\n                </Link>\n              </Button>\n            </div>\n          </>\n        )}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAOA;AACA;AACA;AACA;AACA;;;AApCA;;;;;;;;;;;;;AAsCO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,MAAM;gBACR;gBACA;YACF;QACF;yCAAG;QAAC;KAAK;IAET,MAAM,oBAAoB;QACxB,IAAI,CAAC,MAAM;QAEX,aAAa;QACb,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,8HAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,EAAE,EAAE;YAC5D,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,gCAAgC;YAChD,OAAO;gBACL,iBAAiB,QAAQ,EAAE;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,KAAK,EAAE;YACjE,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,+BAA+B;YAC/C,OAAO;gBACL,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,8HAAA,CAAA,yBAAsB,AAAD,EAAE;YAC/C,IAAI,OAAO;gBACT,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,iBAAiB;4BAAE,GAAG,CAAC;4BAAE,MAAM;wBAAK,IAAI;gBAEjE,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,8HAAA,CAAA,6BAA0B,AAAD,EAAE,KAAK,EAAE;YAC1D,IAAI,OAAO;gBACT,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,iBAAiB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC;4BAAE,GAAG,CAAC;4BAAE,MAAM;wBAAK,CAAC;gBAC5D,eAAe;gBACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B;gBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ,aAAa,IAAI;YACvB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,IAAI,aAAa,IAAI,CAAC,QAAQ,EAAE;oBAC9B,OAAO,AAAC,gCAA0D,OAA3B,aAAa,IAAI,CAAC,QAAQ;gBACnE;gBACA,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,6LAAC,+IAAA,CAAA,eAAY;QAAC,MAAM;QAAQ,cAAc;;0BACxC,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAK,WAAU;;sCAC1C,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACf,cAAc,mBACb,6LAAC,oIAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,WAAU;sCAET,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAKpC,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAM;gBAAM,WAAU;;kCACzC,6LAAC,+IAAA,CAAA,oBAAiB;wBAAC,WAAU;;0CAC3B,6LAAC;gCAAG,WAAU;0CAAgB;;;;;;4BAC7B,cAAc,mBACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAK7C,6LAAC,+IAAA,CAAA,wBAAqB;;;;;kCAEtB,6LAAC,6IAAA,CAAA,aAAU;wBAAC,WAAU;kCACnB,0BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;mCAEf,cAAc,MAAM,KAAK,kBAC3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;iDAG/C,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,6LAAC;oCAEC,WAAW,AAAC,sDAEX,OADC,aAAa,IAAI,GAAG,uBAAuB;8CAG7C,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,oBAAoB;wCAC1B,SAAS;4CACP,IAAI,CAAC,aAAa,IAAI,EAAE;gDACtB,iBAAiB,aAAa,EAAE;4CAClC;4CACA,UAAU;wCACZ;wCACA,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,oBAAoB,aAAa,IAAI;;;;;;8DAExC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAW,AAAC,uBAEf,OADC,aAAa,IAAI,GAAG,0BAA0B;8EAE7C,aAAa,KAAK;;;;;;gEAEpB,CAAC,aAAa,IAAI,kBACjB,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,CAAC;wEACR,EAAE,cAAc;wEAChB,EAAE,eAAe;wEACjB,iBAAiB,aAAa,EAAE;oEAClC;oEACA,WAAU;8EAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAIvB,6LAAC;4DAAE,WAAW,AAAC,gBAEd,OADC,aAAa,IAAI,GAAG,0BAA0B;sEAE7C,aAAa,OAAO;;;;;;sEAEvB,6LAAC;4DAAE,WAAU;sEACV,CAAA,GAAA,qJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,aAAa,UAAU,GAAG;gEACtD,WAAW;gEACX,QAAQ,8IAAA,CAAA,KAAE;4DACZ;;;;;;;;;;;;;;;;;;;;;;;mCAlDH,aAAa,EAAE;;;;;;;;;;;;;;;oBA6D7B,cAAc,MAAM,GAAG,mBACtB;;0CACE,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0CACtB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAS,OAAO;8CAC1D,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtD;GArPgB;;QACG,kIAAA,CAAA,UAAO;;;KADV", "debugId": null}}, {"offset": {"line": 5194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/dashboard/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { getProfile } from '@/lib/profiles';\nimport { ResponsiveNavigation } from '@/components/navigation/ResponsiveNavigation';\nimport { NotificationDropdown } from '@/components/notifications/NotificationDropdown';\nimport { Loader2 } from 'lucide-react';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n  requiredUserType?: 'influencer' | 'business';\n}\n\nexport function DashboardLayout({ children, requiredUserType }: DashboardLayoutProps) {\n  const { user, loading: authLoading } = useAuth();\n  const router = useRouter();\n  const [profile, setProfile] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (authLoading) return;\n\n    if (!user) {\n      router.push('/prijava');\n      return;\n    }\n\n    loadProfile();\n  }, [user, authLoading, router]);\n\n  const loadProfile = async () => {\n    try {\n      setLoading(true);\n      const { data, error } = await getProfile(user!.id);\n\n      if (error) {\n        console.error('Profile loading error:', error);\n        if (error.message && error.message.includes('No rows')) {\n          router.push('/profil/kreiranje');\n          return;\n        }\n        setError('Greška pri učitavanju profila');\n        return;\n      }\n\n      if (!data) {\n        router.push('/profil/kreiranje');\n        return;\n      }\n\n      setProfile(data);\n\n      // Provjeri da li korisnik ima pravo pristupa ovoj stranici\n      if (requiredUserType && data.user_type !== requiredUserType) {\n        // Preusmjeri na odgovarajući dashboard\n        if (data.user_type === 'influencer') {\n          router.push('/dashboard/influencer');\n        } else if (data.user_type === 'business') {\n          router.push('/dashboard/biznis');\n        }\n        return;\n      }\n    } catch (err) {\n      console.error('Unexpected error in loadProfile:', err);\n      setError('Neočekivana greška');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Loading state\n  if (authLoading || loading) {\n    return (\n      <div className=\"min-h-screen bg-background flex items-center justify-center\">\n        <div className=\"flex flex-col items-center space-y-4\">\n          <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\n          <p className=\"text-muted-foreground\">Učitavanje...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-background flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-foreground mb-2\">Greška</h2>\n          <p className=\"text-muted-foreground mb-4\">{error}</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90\"\n          >\n            Pokušaj ponovo\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // No profile state\n  if (!profile) {\n    return null; // Router redirect will handle this\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Responsive Navigation */}\n      <ResponsiveNavigation userType={profile.user_type} />\n\n      {/* Main Content - full width without sidebar */}\n      <div className=\"flex flex-col min-h-screen\">\n        {/* Header */}\n        <header className=\"bg-card border-b border-border px-6 py-4 md:pl-20\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-foreground\">\n                {profile.user_type === 'influencer' ? 'Influencer Dashboard' : 'Biznis Dashboard'}\n              </h1>\n              <p className=\"text-muted-foreground\">\n                Dobrodošli, {profile.full_name || profile.username}\n              </p>\n            </div>\n\n            {/* User Info */}\n            <div className=\"flex items-center space-x-3\">\n              <NotificationDropdown />\n              {profile.avatar_url && (\n                <img\n                  src={profile.avatar_url}\n                  alt={profile.username}\n                  className=\"w-8 h-8 rounded-full object-cover\"\n                />\n              )}\n              <div className=\"text-right hidden sm:block\">\n                <p className=\"text-sm font-medium text-foreground\">\n                  {profile.full_name || profile.username}\n                </p>\n                <p className=\"text-xs text-muted-foreground\">\n                  @{profile.username}\n                </p>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        {/* Page Content */}\n        <main className=\"flex-1 overflow-auto pb-16 md:pb-0\">\n          <div className=\"p-6\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAeO,SAAS,gBAAgB,KAAoD;QAApD,EAAE,QAAQ,EAAE,gBAAgB,EAAwB,GAApD;;IAC9B,MAAM,EAAE,IAAI,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC5C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,aAAa;YAEjB,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA;QACF;oCAAG;QAAC;QAAM;QAAa;KAAO;IAE9B,MAAM,cAAc;QAClB,IAAI;YACF,WAAW;YACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE,KAAM,EAAE;YAEjD,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;oBACtD,OAAO,IAAI,CAAC;oBACZ;gBACF;gBACA,SAAS;gBACT;YACF;YAEA,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,WAAW;YAEX,2DAA2D;YAC3D,IAAI,oBAAoB,KAAK,SAAS,KAAK,kBAAkB;gBAC3D,uCAAuC;gBACvC,IAAI,KAAK,SAAS,KAAK,cAAc;oBACnC,OAAO,IAAI,CAAC;gBACd,OAAO,IAAI,KAAK,SAAS,KAAK,YAAY;oBACxC,OAAO,IAAI,CAAC;gBACd;gBACA;YACF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oCAAoC;YAClD,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,gBAAgB;IAChB,IAAI,eAAe,SAAS;QAC1B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0C;;;;;;kCACxD,6LAAC;wBAAE,WAAU;kCAA8B;;;;;;kCAC3C,6LAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,mBAAmB;IACnB,IAAI,CAAC,SAAS;QACZ,OAAO,MAAM,mCAAmC;IAClD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,2JAAA,CAAA,uBAAoB;gBAAC,UAAU,QAAQ,SAAS;;;;;;0BAGjD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDACX,QAAQ,SAAS,KAAK,eAAe,yBAAyB;;;;;;sDAEjE,6LAAC;4CAAE,WAAU;;gDAAwB;gDACtB,QAAQ,SAAS,IAAI,QAAQ,QAAQ;;;;;;;;;;;;;8CAKtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8JAAA,CAAA,uBAAoB;;;;;wCACpB,QAAQ,UAAU,kBACjB,6LAAC;4CACC,KAAK,QAAQ,UAAU;4CACvB,KAAK,QAAQ,QAAQ;4CACrB,WAAU;;;;;;sDAGd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACV,QAAQ,SAAS,IAAI,QAAQ,QAAQ;;;;;;8DAExC,6LAAC;oDAAE,WAAU;;wDAAgC;wDACzC,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ5B,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GA/IgB;;QACyB,kIAAA,CAAA,UAAO;QAC/B,qIAAA,CAAA,YAAS;;;KAFV", "debugId": null}}, {"offset": {"line": 5507, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/app/marketplace/influencers/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport { Search, Filter, MapPin, Users, Star, Verified } from 'lucide-react';\nimport { searchInfluencers, getMarketplaceStats, type InfluencerSearchResult, type SearchFilters } from '@/lib/marketplace';\nimport HorizontalFilters from '@/components/marketplace/horizontal-filters';\nimport { TooltipProvider } from '@/components/ui/tooltip';\nimport Link from 'next/link';\nimport { DashboardLayout } from '@/components/dashboard/DashboardLayout';\n\nexport default function InfluencerMarketplacePage() {\n  const [influencers, setInfluencers] = useState<InfluencerSearchResult[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const [filters, setFilters] = useState<SearchFilters>({\n    search: '',\n    sortBy: 'relevance',\n    limit: 12,\n    offset: 0\n  });\n\n  // Load initial data\n  useEffect(() => {\n    loadInfluencers();\n  }, []);\n\n  const loadInfluencers = async (newFilters?: SearchFilters) => {\n    setLoading(true);\n    try {\n      const filtersToUse = newFilters || filters;\n      const { data, error } = await searchInfluencers(filtersToUse);\n\n      console.log('Search result:', { data, error });\n\n      if (error) {\n        console.error('Error loading influencers:', error);\n        return;\n      }\n\n      console.log('Setting influencers:', data?.length || 0);\n      setInfluencers(data || []);\n    } catch (error) {\n      console.error('Error in loadInfluencers:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n\n\n  const handleSearch = () => {\n    const newFilters = {\n      ...filters,\n      search: searchQuery,\n      offset: 0\n    };\n    setFilters(newFilters);\n    loadInfluencers(newFilters);\n  };\n\n  const handleSortChange = (sortBy: string) => {\n    const newFilters = {\n      ...filters,\n      sortBy: sortBy as any,\n      offset: 0\n    };\n    setFilters(newFilters);\n    loadInfluencers(newFilters);\n  };\n\n  const formatPrice = (price: number) => {\n    return `${price} KM`;\n  };\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  return (\n    <DashboardLayout requiredUserType=\"business\">\n      <TooltipProvider>\n        <div className=\"space-y-6\">\n          {/* Header */}\n          <div>\n            <h1 className=\"text-2xl md:text-3xl font-bold\">Marketplace Influencera</h1>\n            <p className=\"text-muted-foreground mt-1\">\n              Pronađite savršenog influencera za vašu kampanju\n            </p>\n          </div>\n\n          <div>\n        {/* Horizontal Filters */}\n        <HorizontalFilters\n          filters={filters}\n          onFiltersChange={setFilters}\n          onClearFilters={() => setFilters({})}\n        />\n\n        {/* Search and Sort Bar */}\n        <div className=\"flex flex-col sm:flex-row gap-4 mb-6\">\n          <div className=\"flex-1 relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4\" />\n            <Input\n              placeholder=\"Pretražite influencere po imenu ili username...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"pl-10\"\n              onKeyDown={(e) => e.key === 'Enter' && handleSearch()}\n            />\n          </div>\n          <div className=\"flex gap-2\">\n            <select\n              value={filters.sortBy}\n              onChange={(e) => handleSortChange(e.target.value)}\n              className=\"px-3 py-2 border rounded-md bg-background min-w-[180px]\"\n            >\n              <option value=\"relevance\">Relevantnost</option>\n              <option value=\"price_asc\">Cijena (najniža)</option>\n              <option value=\"price_desc\">Cijena (najviša)</option>\n              <option value=\"followers_desc\">Broj pratilaca</option>\n              <option value=\"newest\">Najnoviji</option>\n            </select>\n            <Button onClick={handleSearch}>\n              <Search className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n\n        {/* Results */}\n        <div className=\"w-full\">\n            {loading ? (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6\">\n                {[...Array(6)].map((_, i) => (\n                  <Card key={i} className=\"animate-pulse\">\n                    <CardContent className=\"p-6\">\n                      <div className=\"flex items-center gap-4 mb-4\">\n                        <div className=\"w-16 h-16 bg-muted rounded-full\"></div>\n                        <div className=\"flex-1\">\n                          <div className=\"h-4 bg-muted rounded mb-2\"></div>\n                          <div className=\"h-3 bg-muted rounded w-2/3\"></div>\n                        </div>\n                      </div>\n                      <div className=\"space-y-2\">\n                        <div className=\"h-3 bg-muted rounded\"></div>\n                        <div className=\"h-3 bg-muted rounded w-3/4\"></div>\n                      </div>\n                    </CardContent>\n                  </Card>\n                ))}\n              </div>\n            ) : influencers.length === 0 ? (\n              <Card>\n                <CardContent className=\"p-12 text-center\">\n                  <Search className=\"h-12 w-12 mx-auto text-muted-foreground mb-4\" />\n                  <h3 className=\"text-lg font-semibold mb-2\">Nema rezultata</h3>\n                  <p className=\"text-muted-foreground\">\n                    Pokušajte sa drugačijim kriterijumima pretrage.\n                  </p>\n                </CardContent>\n              </Card>\n            ) : (\n              <>\n                <div className=\"flex items-center justify-between mb-6\">\n                  <h2 className=\"text-xl font-semibold\">\n                    {influencers.length} influencer{influencers.length !== 1 ? 'a' : ''}\n                  </h2>\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6\">\n                  {influencers.map((influencer) => (\n                    <Link key={influencer.id} href={`/influencer/${influencer.username}`}>\n                      <Card className=\"hover:shadow-lg transition-shadow cursor-pointer\">\n                        <CardContent className=\"p-6\">\n                          {/* Header */}\n                          <div className=\"flex items-center gap-4 mb-4\">\n                            <Avatar className=\"h-16 w-16\">\n                              <AvatarImage src={influencer.avatar_url} />\n                              <AvatarFallback>\n                                {getInitials(influencer.full_name || influencer.username)}\n                              </AvatarFallback>\n                            </Avatar>\n                            <div className=\"flex-1 min-w-0\">\n                              <div className=\"flex items-center gap-2\">\n                                <h3 className=\"font-semibold truncate\">\n                                  {influencer.full_name || influencer.username}\n                                </h3>\n                                {influencer.is_verified && (\n                                  <Verified className=\"h-4 w-4 text-blue-500\" />\n                                )}\n                              </div>\n                              <p className=\"text-sm text-muted-foreground\">\n                                @{influencer.username}\n                              </p>\n                              {influencer.location && (\n                                <div className=\"flex items-center gap-1 text-xs text-muted-foreground mt-1\">\n                                  <MapPin className=\"h-3 w-3\" />\n                                  {influencer.location}\n                                </div>\n                              )}\n                            </div>\n                          </div>\n\n                          {/* Bio */}\n                          {influencer.bio && (\n                            <p className=\"text-sm text-muted-foreground mb-4 line-clamp-2\">\n                              {influencer.bio}\n                            </p>\n                          )}\n\n                          {/* Categories */}\n                          {influencer.categories.length > 0 && (\n                            <div className=\"flex flex-wrap gap-1 mb-4\">\n                              {influencer.categories.slice(0, 3).map((category, index) => (\n                                <Badge key={index} variant=\"secondary\" className=\"text-xs\">\n                                  {category}\n                                </Badge>\n                              ))}\n                              {influencer.categories.length > 3 && (\n                                <Badge variant=\"outline\" className=\"text-xs\">\n                                  +{influencer.categories.length - 3}\n                                </Badge>\n                              )}\n                            </div>\n                          )}\n\n                          {/* Stats */}\n                          <div className=\"flex items-center justify-between text-sm\">\n                            <div className=\"flex items-center gap-1 text-muted-foreground\">\n                              <Users className=\"h-4 w-4\" />\n                              {influencer.total_followers.toLocaleString()}\n                            </div>\n                            {influencer.min_price > 0 && (\n                              <div className=\"font-semibold text-primary\">\n                                od {formatPrice(influencer.min_price)}\n                              </div>\n                            )}\n                          </div>\n\n                          {/* Platforms */}\n                          {influencer.platforms.length > 0 && (\n                            <div className=\"flex gap-1 mt-3\">\n                              {influencer.platforms.slice(0, 4).map((platform, index) => (\n                                <span key={index} className=\"text-lg\" title={platform.platform_name}>\n                                  {platform.platform_icon}\n                                </span>\n                              ))}\n                              {influencer.platforms.length > 4 && (\n                                <span className=\"text-xs text-muted-foreground\">\n                                  +{influencer.platforms.length - 4}\n                                </span>\n                              )}\n                            </div>\n                          )}\n                        </CardContent>\n                      </Card>\n                    </Link>\n                  ))}\n                </div>\n              </>\n            )}\n          </div>\n        </div>\n        </div>\n      </TooltipProvider>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAbA;;;;;;;;;;;;;AAee,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B,EAAE;IAC3E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QACpD,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IAEA,oBAAoB;IACpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+CAAE;YACR;QACF;8CAAG,EAAE;IAEL,MAAM,kBAAkB,OAAO;QAC7B,WAAW;QACX,IAAI;YACF,MAAM,eAAe,cAAc;YACnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE;YAEhD,QAAQ,GAAG,CAAC,kBAAkB;gBAAE;gBAAM;YAAM;YAE5C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C;YACF;YAEA,QAAQ,GAAG,CAAC,wBAAwB,CAAA,iBAAA,2BAAA,KAAM,MAAM,KAAI;YACpD,eAAe,QAAQ,EAAE;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAIA,MAAM,eAAe;QACnB,MAAM,aAAa;YACjB,GAAG,OAAO;YACV,QAAQ;YACR,QAAQ;QACV;QACA,WAAW;QACX,gBAAgB;IAClB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,aAAa;YACjB,GAAG,OAAO;YACV,QAAQ;YACR,QAAQ;QACV;QACA,WAAW;QACX,gBAAgB;IAClB;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,AAAC,GAAQ,OAAN,OAAM;IAClB;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,qBACE,6LAAC,qJAAA,CAAA,kBAAe;QAAC,kBAAiB;kBAChC,cAAA,6LAAC,sIAAA,CAAA,kBAAe;sBACd,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAK5C,6LAAC;;0CAEH,6LAAC,6JAAA,CAAA,UAAiB;gCAChB,SAAS;gCACT,iBAAiB;gCACjB,gBAAgB,IAAM,WAAW,CAAC;;;;;;0CAIpC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;gDACV,WAAW,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;;;;;;;kDAG3C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,OAAO,QAAQ,MAAM;gDACrB,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAChD,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAY;;;;;;kEAC1B,6LAAC;wDAAO,OAAM;kEAAY;;;;;;kEAC1B,6LAAC;wDAAO,OAAM;kEAAa;;;;;;kEAC3B,6LAAC;wDAAO,OAAM;kEAAiB;;;;;;kEAC/B,6LAAC;wDAAO,OAAM;kEAAS;;;;;;;;;;;;0DAEzB,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAS;0DACf,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAMxB,6LAAC;gCAAI,WAAU;0CACV,wBACC,6LAAC;oCAAI,WAAU;8CACZ;2CAAI,MAAM;qCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,mIAAA,CAAA,OAAI;4CAAS,WAAU;sDACtB,cAAA,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;wEAAI,WAAU;;;;;;;;;;;;;;;;;;kEAGnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;2CAXV;;;;;;;;;2CAiBb,YAAY,MAAM,KAAK,kBACzB,6LAAC,mIAAA,CAAA,OAAI;8CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;yDAMzC;;sDACE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAG,WAAU;;oDACX,YAAY,MAAM;oDAAC;oDAAY,YAAY,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;sDAIrE,6LAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,6LAAC,+JAAA,CAAA,UAAI;oDAAqB,MAAM,AAAC,eAAkC,OAApB,WAAW,QAAQ;8DAChE,cAAA,6LAAC,mIAAA,CAAA,OAAI;wDAAC,WAAU;kEACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4DAAC,WAAU;;8EAErB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qIAAA,CAAA,SAAM;4EAAC,WAAU;;8FAChB,6LAAC,qIAAA,CAAA,cAAW;oFAAC,KAAK,WAAW,UAAU;;;;;;8FACvC,6LAAC,qIAAA,CAAA,iBAAc;8FACZ,YAAY,WAAW,SAAS,IAAI,WAAW,QAAQ;;;;;;;;;;;;sFAG5D,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;;sGACb,6LAAC;4FAAG,WAAU;sGACX,WAAW,SAAS,IAAI,WAAW,QAAQ;;;;;;wFAE7C,WAAW,WAAW,kBACrB,6LAAC,mNAAA,CAAA,WAAQ;4FAAC,WAAU;;;;;;;;;;;;8FAGxB,6LAAC;oFAAE,WAAU;;wFAAgC;wFACzC,WAAW,QAAQ;;;;;;;gFAEtB,WAAW,QAAQ,kBAClB,6LAAC;oFAAI,WAAU;;sGACb,6LAAC,6MAAA,CAAA,SAAM;4FAAC,WAAU;;;;;;wFACjB,WAAW,QAAQ;;;;;;;;;;;;;;;;;;;gEAO3B,WAAW,GAAG,kBACb,6LAAC;oEAAE,WAAU;8EACV,WAAW,GAAG;;;;;;gEAKlB,WAAW,UAAU,CAAC,MAAM,GAAG,mBAC9B,6LAAC;oEAAI,WAAU;;wEACZ,WAAW,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,UAAU,sBAChD,6LAAC,oIAAA,CAAA,QAAK;gFAAa,SAAQ;gFAAY,WAAU;0FAC9C;+EADS;;;;;wEAIb,WAAW,UAAU,CAAC,MAAM,GAAG,mBAC9B,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;;gFAAU;gFACzC,WAAW,UAAU,CAAC,MAAM,GAAG;;;;;;;;;;;;;8EAOzC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,uMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;gFAChB,WAAW,eAAe,CAAC,cAAc;;;;;;;wEAE3C,WAAW,SAAS,GAAG,mBACtB,6LAAC;4EAAI,WAAU;;gFAA6B;gFACtC,YAAY,WAAW,SAAS;;;;;;;;;;;;;gEAMzC,WAAW,SAAS,CAAC,MAAM,GAAG,mBAC7B,6LAAC;oEAAI,WAAU;;wEACZ,WAAW,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,UAAU,sBAC/C,6LAAC;gFAAiB,WAAU;gFAAU,OAAO,SAAS,aAAa;0FAChE,SAAS,aAAa;+EADd;;;;;wEAIZ,WAAW,SAAS,CAAC,MAAM,GAAG,mBAC7B,6LAAC;4EAAK,WAAU;;gFAAgC;gFAC5C,WAAW,SAAS,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;mDA9EnC,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgG5C;GAtQwB;KAAA", "debugId": null}}]}