'use client';

import { useSearchParams } from 'next/navigation';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Chat } from '@/components/chat/Chat';
import { MessageCircle, Users, Sparkles } from 'lucide-react';

export default function ChatPage() {
  const searchParams = useSearchParams();
  const roomId = searchParams.get('room');
  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Enhanced Header */}
        <div className="relative overflow-hidden rounded-lg bg-gradient-to-r from-green-600 to-blue-600 p-6 text-white">
          <div className="relative z-10">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 bg-white/20 rounded-lg">
                <MessageCircle className="h-6 w-6" />
              </div>
              <h1 className="text-3xl font-bold tracking-tight">Poruke</h1>
            </div>
            <p className="text-green-100 mb-4">
              Komunicirajte sa partnerima o kampanjama i ponudama
            </p>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 text-sm bg-white/20 px-3 py-1 rounded-full">
                <Users className="h-4 w-4" />
                <span>Sigurna komunikacija</span>
              </div>
              <div className="flex items-center gap-2 text-sm bg-white/20 px-3 py-1 rounded-full">
                <Sparkles className="h-4 w-4" />
                <span>Trenutne poruke</span>
              </div>
            </div>
          </div>
          <div className="absolute top-0 right-0 -mt-4 -mr-4 h-24 w-24 rounded-full bg-white/10" />
          <div className="absolute bottom-0 left-0 -mb-8 -ml-8 h-32 w-32 rounded-full bg-white/5" />
        </div>

        <Chat initialRoomId={roomId || undefined} />
      </div>
    </DashboardLayout>
  );
}
