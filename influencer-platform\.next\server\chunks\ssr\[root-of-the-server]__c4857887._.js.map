{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/profiles.ts"], "sourcesContent": ["import { supabase } from './supabase';\nimport { Database } from './database.types';\n\ntype Profile = Database['public']['Tables']['profiles']['Row'];\ntype ProfileInsert = Database['public']['Tables']['profiles']['Insert'];\ntype ProfileUpdate = Database['public']['Tables']['profiles']['Update'];\n\ntype Influencer = Database['public']['Tables']['influencers']['Row'];\ntype InfluencerInsert = Database['public']['Tables']['influencers']['Insert'];\ntype InfluencerUpdate = Database['public']['Tables']['influencers']['Update'];\n\ntype Business = Database['public']['Tables']['businesses']['Row'];\ntype BusinessInsert = Database['public']['Tables']['businesses']['Insert'];\ntype BusinessUpdate = Database['public']['Tables']['businesses']['Update'];\n\n// Profile functions\nexport const getProfile = async (userId: string) => {\n  const { data, error } = await supabase\n    .from('profiles')\n    .select('*')\n    .eq('id', userId)\n    .single();\n  \n  return { data, error };\n};\n\nexport const updateProfile = async (userId: string, updates: ProfileUpdate) => {\n  const { data, error } = await supabase\n    .from('profiles')\n    .update(updates)\n    .eq('id', userId)\n    .select()\n    .single();\n\n  return { data, error };\n};\n\nexport const createProfile = async (profileData: ProfileInsert) => {\n  const { data, error } = await supabase\n    .from('profiles')\n    .insert(profileData)\n    .select()\n    .single();\n\n  return { data, error };\n};\n\n// Get public influencer profile by username\nexport const getPublicInfluencerProfile = async (username: string) => {\n  // Use RPC function to get influencer data\n  const { data: influencerData, error: influencerError } = await supabase\n    .rpc('get_influencers_with_details', {\n      search_term: username,\n      min_followers: 0,\n      max_followers: 999999999,\n      min_price: 0,\n      max_price: 999999,\n      platform_filter: '',\n      category_filter: '',\n      location_filter: '',\n      limit_count: 10\n    });\n\n  if (influencerError || !influencerData || influencerData.length === 0) {\n    return { data: null, error: influencerError || { message: 'Influencer not found' } };\n  }\n\n  // Find the exact username match\n  const exactMatch = influencerData.find(item => item.username === username);\n  if (!exactMatch) {\n    return { data: null, error: { message: 'Influencer not found' } };\n  }\n\n  const data = exactMatch;\n\n  // Transform data to match expected structure\n  const transformedData = {\n    id: data.id,\n    username: data.username,\n    full_name: data.full_name,\n    avatar_url: data.avatar_url,\n    bio: data.bio,\n    location: data.location,\n    created_at: data.created_at,\n    gender: data.gender,\n    age: data.age,\n    is_verified: data.is_verified,\n    platforms: [\n      ...(data.instagram_followers > 0 ? [{\n        platform_id: 1,\n        platform_name: 'Instagram',\n        platform_icon: '📷',\n        handle: `@${data.username}`,\n        followers_count: data.instagram_followers,\n        is_verified: data.is_verified\n      }] : []),\n      ...(data.tiktok_followers > 0 ? [{\n        platform_id: 2,\n        platform_name: 'TikTok',\n        platform_icon: '🎵',\n        handle: `@${data.username}`,\n        followers_count: data.tiktok_followers,\n        is_verified: false\n      }] : []),\n      ...(data.youtube_subscribers > 0 ? [{\n        platform_id: 3,\n        platform_name: 'YouTube',\n        platform_icon: '📺',\n        handle: `@${data.username}`,\n        followers_count: data.youtube_subscribers,\n        is_verified: false\n      }] : [])\n    ],\n    categories: [], // TODO: Add categories when implemented\n    pricing: [\n      ...(data.price_per_post ? [{\n        platform_id: 1,\n        platform_name: 'Instagram',\n        platform_icon: '📷',\n        content_type_id: 1,\n        content_type_name: 'Post',\n        price: Number(data.price_per_post),\n        currency: 'KM'\n      }] : []),\n      ...(data.price_per_story ? [{\n        platform_id: 1,\n        platform_name: 'Instagram',\n        platform_icon: '📷',\n        content_type_id: 2,\n        content_type_name: 'Story',\n        price: Number(data.price_per_story),\n        currency: 'KM'\n      }] : []),\n      ...(data.price_per_reel ? [{\n        platform_id: 1,\n        platform_name: 'Instagram',\n        platform_icon: '📷',\n        content_type_id: 3,\n        content_type_name: 'Reel',\n        price: Number(data.price_per_reel),\n        currency: 'KM'\n      }] : [])\n    ],\n    portfolio_items: [], // TODO: Add portfolio items when implemented\n    total_followers: (data.instagram_followers || 0) +\n                    (data.tiktok_followers || 0) +\n                    (data.youtube_subscribers || 0),\n    min_price: Math.min(\n      ...[data.price_per_post, data.price_per_story, data.price_per_reel]\n        .filter(Boolean)\n        .map(Number)\n    ) || 0,\n    max_price: Math.max(\n      ...[data.price_per_post, data.price_per_story, data.price_per_reel]\n        .filter(Boolean)\n        .map(Number)\n    ) || 0\n  };\n\n  return { data: transformedData, error: null };\n};\n\nexport const upsertProfile = async (userId: string, updates: ProfileUpdate) => {\n  // First try to get existing profile\n  const { data: existingProfile } = await getProfile(userId);\n\n  if (existingProfile) {\n    // Profile exists, update it\n    return updateProfile(userId, updates);\n  } else {\n    // Profile doesn't exist, create it\n    const profileData: ProfileInsert = {\n      id: userId,\n      user_type: updates.user_type || 'influencer',\n      username: updates.username || null,\n      full_name: updates.full_name || null,\n      avatar_url: updates.avatar_url || null,\n      bio: updates.bio || null,\n      website_url: updates.website_url || null,\n      location: updates.location || null,\n    };\n    return createProfile(profileData);\n  }\n};\n\nexport const checkUsernameAvailable = async (username: string, excludeUserId?: string) => {\n  let query = supabase\n    .from('profiles')\n    .select('id')\n    .eq('username', username);\n  \n  if (excludeUserId) {\n    query = query.neq('id', excludeUserId);\n  }\n  \n  const { data, error } = await query;\n  \n  if (error) return { available: false, error };\n  return { available: data.length === 0, error: null };\n};\n\n// Influencer functions\nexport const getInfluencer = async (userId: string) => {\n  const { data, error } = await supabase\n    .from('influencers')\n    .select(`\n      *,\n      profiles (*)\n    `)\n    .eq('id', userId)\n    .single();\n  \n  return { data, error };\n};\n\nexport const createInfluencer = async (influencerData: InfluencerInsert) => {\n  const { data, error } = await supabase\n    .from('influencers')\n    .insert(influencerData)\n    .select()\n    .single();\n  \n  return { data, error };\n};\n\nexport const updateInfluencer = async (userId: string, updates: InfluencerUpdate) => {\n  const { data, error } = await supabase\n    .from('influencers')\n    .update(updates)\n    .eq('id', userId)\n    .select()\n    .single();\n\n  return { data, error };\n};\n\nexport const getInfluencers = async (filters?: {\n  niche?: string;\n  minFollowers?: number;\n  maxFollowers?: number;\n  location?: string;\n  limit?: number;\n  offset?: number;\n}) => {\n  let query = supabase\n    .from('influencers')\n    .select(`\n      *,\n      profiles (*)\n    `);\n\n  if (filters?.niche) {\n    query = query.ilike('niche', `%${filters.niche}%`);\n  }\n\n  if (filters?.minFollowers) {\n    query = query.gte('instagram_followers', filters.minFollowers);\n  }\n\n  if (filters?.maxFollowers) {\n    query = query.lte('instagram_followers', filters.maxFollowers);\n  }\n\n  if (filters?.location) {\n    query = query.eq('profiles.location', filters.location);\n  }\n\n  if (filters?.limit) {\n    query = query.limit(filters.limit);\n  }\n\n  if (filters?.offset) {\n    query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);\n  }\n\n  const { data, error } = await query;\n  return { data, error };\n};\n\n// Business functions\nexport const getBusiness = async (userId: string) => {\n  const { data, error } = await supabase\n    .from('businesses')\n    .select(`\n      *,\n      profiles (*)\n    `)\n    .eq('id', userId)\n    .single();\n  \n  return { data, error };\n};\n\nexport const createBusiness = async (businessData: BusinessInsert) => {\n  const { data, error } = await supabase\n    .from('businesses')\n    .insert(businessData)\n    .select()\n    .single();\n  \n  return { data, error };\n};\n\nexport const updateBusiness = async (userId: string, updates: BusinessUpdate) => {\n  const { data, error } = await supabase\n    .from('businesses')\n    .update(updates)\n    .eq('id', userId)\n    .select()\n    .single();\n\n  return { data, error };\n};\n\n// Category functions\nexport const getCategories = async () => {\n  const { data, error } = await supabase\n    .from('categories')\n    .select('*')\n    .order('name');\n\n  return { data, error };\n};\n\nexport const getInfluencerCategories = async (influencerId: string) => {\n  const { data, error } = await supabase\n    .from('influencer_categories')\n    .select(`\n      category_id,\n      is_primary,\n      categories (*)\n    `)\n    .eq('influencer_id', influencerId);\n\n  return { data, error };\n};\n\nexport const updateInfluencerCategories = async (influencerId: string, categoryIds: number[]) => {\n  // First, delete existing categories\n  await supabase\n    .from('influencer_categories')\n    .delete()\n    .eq('influencer_id', influencerId);\n\n  // Then insert new categories\n  if (categoryIds.length > 0) {\n    const categoryData = categoryIds.map((categoryId, index) => ({\n      influencer_id: influencerId,\n      category_id: categoryId,\n      is_primary: index === 0 // First category is primary\n    }));\n\n    const { data, error } = await supabase\n      .from('influencer_categories')\n      .insert(categoryData)\n      .select();\n\n    return { data, error };\n  }\n\n  return { data: [], error: null };\n};\n\nexport const getBusinessTargetCategories = async (businessId: string) => {\n  const { data, error } = await supabase\n    .from('business_target_categories')\n    .select(`\n      category_id,\n      categories (*)\n    `)\n    .eq('business_id', businessId);\n\n  return { data, error };\n};\n\nexport const updateBusinessTargetCategories = async (businessId: string, categoryIds: number[]) => {\n  // First, delete existing categories\n  await supabase\n    .from('business_target_categories')\n    .delete()\n    .eq('business_id', businessId);\n\n  // Then insert new categories\n  if (categoryIds.length > 0) {\n    const categoryData = categoryIds.map(categoryId => ({\n      business_id: businessId,\n      category_id: categoryId\n    }));\n\n    const { data, error } = await supabase\n      .from('business_target_categories')\n      .insert(categoryData)\n      .select();\n\n    return { data, error };\n  }\n\n  return { data: [], error: null };\n};\n\n// Combined profile functions\nexport const getFullProfile = async (userId: string) => {\n  const { data: profile, error: profileError } = await getProfile(userId);\n  \n  if (profileError || !profile) {\n    return { data: null, error: profileError };\n  }\n\n  if (profile.user_type === 'influencer') {\n    const { data: influencer, error: influencerError } = await getInfluencer(userId);\n    return { \n      data: influencer ? { ...profile, influencer } : profile, \n      error: influencerError \n    };\n  } else if (profile.user_type === 'business') {\n    const { data: business, error: businessError } = await getBusiness(userId);\n    return { \n      data: business ? { ...profile, business } : profile, \n      error: businessError \n    };\n  }\n\n  return { data: profile, error: null };\n};\n\n// Upload avatar function\nexport const uploadAvatar = async (userId: string, file: File) => {\n  const fileExt = file.name.split('.').pop();\n  const fileName = `${userId}-${Math.random()}.${fileExt}`;\n  const filePath = `avatars/${fileName}`;\n\n  const { error: uploadError } = await supabase.storage\n    .from('avatars')\n    .upload(filePath, file);\n\n  if (uploadError) {\n    return { data: null, error: uploadError };\n  }\n\n  const { data } = supabase.storage\n    .from('avatars')\n    .getPublicUrl(filePath);\n\n  // Update profile with new avatar URL\n  const { error: updateError } = await updateProfile(userId, {\n    avatar_url: data.publicUrl,\n  });\n\n  if (updateError) {\n    return { data: null, error: updateError };\n  }\n\n  return { data: data.publicUrl, error: null };\n};\n\n// Platform and Pricing functions\nexport const getInfluencerPlatforms = async (influencerId: string) => {\n  const { data, error } = await supabase\n    .from('influencer_platforms')\n    .select(`\n      *,\n      platforms (*)\n    `)\n    .eq('influencer_id', influencerId)\n    .eq('is_active', true);\n\n  return { data, error };\n};\n\nexport const getInfluencerPricing = async (influencerId: string) => {\n  const { data, error } = await supabase\n    .from('influencer_platform_pricing')\n    .select(`\n      *,\n      platforms (*),\n      content_types (*)\n    `)\n    .eq('influencer_id', influencerId)\n    .eq('is_available', true);\n\n  return { data, error };\n};\n\nexport const updateInfluencerPlatforms = async (influencerId: string, platforms: any[]) => {\n  // First, delete existing platforms\n  await supabase\n    .from('influencer_platforms')\n    .delete()\n    .eq('influencer_id', influencerId);\n\n  // Then insert new platforms\n  if (platforms.length > 0) {\n    const platformData = platforms.map(platform => ({\n      influencer_id: influencerId,\n      platform_id: platform.platform_id,\n      handle: platform.handle || null,\n      followers_count: platform.followers_count || 0,\n      is_verified: false,\n      is_active: true\n    }));\n\n    const { data, error } = await supabase\n      .from('influencer_platforms')\n      .insert(platformData)\n      .select();\n\n    return { data, error };\n  }\n\n  return { data: [], error: null };\n};\n\nexport const updateInfluencerPricing = async (influencerId: string, pricing: any[]) => {\n  // First, delete existing pricing\n  await supabase\n    .from('influencer_platform_pricing')\n    .delete()\n    .eq('influencer_id', influencerId);\n\n  // Then insert new pricing\n  if (pricing.length > 0) {\n    const pricingData = pricing.map(price => ({\n      influencer_id: influencerId,\n      platform_id: price.platform_id,\n      content_type_id: price.content_type_id,\n      price: price.price,\n      currency: 'KM',\n      is_available: price.is_available !== false\n    }));\n\n    const { data, error } = await supabase\n      .from('influencer_platform_pricing')\n      .insert(pricingData)\n      .select();\n\n    return { data, error };\n  }\n\n  return { data: [], error: null };\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAgBO,MAAM,aAAa,OAAO;IAC/B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,gBAAgB,OAAO,QAAgB;IAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,gBAAgB,OAAO;IAClC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,aACP,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,MAAM,6BAA6B,OAAO;IAC/C,0CAA0C;IAC1C,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACpE,GAAG,CAAC,gCAAgC;QACnC,aAAa;QACb,eAAe;QACf,eAAe;QACf,WAAW;QACX,WAAW;QACX,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,aAAa;IACf;IAEF,IAAI,mBAAmB,CAAC,kBAAkB,eAAe,MAAM,KAAK,GAAG;QACrE,OAAO;YAAE,MAAM;YAAM,OAAO,mBAAmB;gBAAE,SAAS;YAAuB;QAAE;IACrF;IAEA,gCAAgC;IAChC,MAAM,aAAa,eAAe,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;IACjE,IAAI,CAAC,YAAY;QACf,OAAO;YAAE,MAAM;YAAM,OAAO;gBAAE,SAAS;YAAuB;QAAE;IAClE;IAEA,MAAM,OAAO;IAEb,6CAA6C;IAC7C,MAAM,kBAAkB;QACtB,IAAI,KAAK,EAAE;QACX,UAAU,KAAK,QAAQ;QACvB,WAAW,KAAK,SAAS;QACzB,YAAY,KAAK,UAAU;QAC3B,KAAK,KAAK,GAAG;QACb,UAAU,KAAK,QAAQ;QACvB,YAAY,KAAK,UAAU;QAC3B,QAAQ,KAAK,MAAM;QACnB,KAAK,KAAK,GAAG;QACb,aAAa,KAAK,WAAW;QAC7B,WAAW;eACL,KAAK,mBAAmB,GAAG,IAAI;gBAAC;oBAClC,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,QAAQ,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;oBAC3B,iBAAiB,KAAK,mBAAmB;oBACzC,aAAa,KAAK,WAAW;gBAC/B;aAAE,GAAG,EAAE;eACH,KAAK,gBAAgB,GAAG,IAAI;gBAAC;oBAC/B,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,QAAQ,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;oBAC3B,iBAAiB,KAAK,gBAAgB;oBACtC,aAAa;gBACf;aAAE,GAAG,EAAE;eACH,KAAK,mBAAmB,GAAG,IAAI;gBAAC;oBAClC,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,QAAQ,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;oBAC3B,iBAAiB,KAAK,mBAAmB;oBACzC,aAAa;gBACf;aAAE,GAAG,EAAE;SACR;QACD,YAAY,EAAE;QACd,SAAS;eACH,KAAK,cAAc,GAAG;gBAAC;oBACzB,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,OAAO,OAAO,KAAK,cAAc;oBACjC,UAAU;gBACZ;aAAE,GAAG,EAAE;eACH,KAAK,eAAe,GAAG;gBAAC;oBAC1B,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,OAAO,OAAO,KAAK,eAAe;oBAClC,UAAU;gBACZ;aAAE,GAAG,EAAE;eACH,KAAK,cAAc,GAAG;gBAAC;oBACzB,aAAa;oBACb,eAAe;oBACf,eAAe;oBACf,iBAAiB;oBACjB,mBAAmB;oBACnB,OAAO,OAAO,KAAK,cAAc;oBACjC,UAAU;gBACZ;aAAE,GAAG,EAAE;SACR;QACD,iBAAiB,EAAE;QACnB,iBAAiB,CAAC,KAAK,mBAAmB,IAAI,CAAC,IAC/B,CAAC,KAAK,gBAAgB,IAAI,CAAC,IAC3B,CAAC,KAAK,mBAAmB,IAAI,CAAC;QAC9C,WAAW,KAAK,GAAG,IACd;YAAC,KAAK,cAAc;YAAE,KAAK,eAAe;YAAE,KAAK,cAAc;SAAC,CAChE,MAAM,CAAC,SACP,GAAG,CAAC,YACJ;QACL,WAAW,KAAK,GAAG,IACd;YAAC,KAAK,cAAc;YAAE,KAAK,eAAe;YAAE,KAAK,cAAc;SAAC,CAChE,MAAM,CAAC,SACP,GAAG,CAAC,YACJ;IACP;IAEA,OAAO;QAAE,MAAM;QAAiB,OAAO;IAAK;AAC9C;AAEO,MAAM,gBAAgB,OAAO,QAAgB;IAClD,oCAAoC;IACpC,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,WAAW;IAEnD,IAAI,iBAAiB;QACnB,4BAA4B;QAC5B,OAAO,cAAc,QAAQ;IAC/B,OAAO;QACL,mCAAmC;QACnC,MAAM,cAA6B;YACjC,IAAI;YACJ,WAAW,QAAQ,SAAS,IAAI;YAChC,UAAU,QAAQ,QAAQ,IAAI;YAC9B,WAAW,QAAQ,SAAS,IAAI;YAChC,YAAY,QAAQ,UAAU,IAAI;YAClC,KAAK,QAAQ,GAAG,IAAI;YACpB,aAAa,QAAQ,WAAW,IAAI;YACpC,UAAU,QAAQ,QAAQ,IAAI;QAChC;QACA,OAAO,cAAc;IACvB;AACF;AAEO,MAAM,yBAAyB,OAAO,UAAkB;IAC7D,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,YACL,MAAM,CAAC,MACP,EAAE,CAAC,YAAY;IAElB,IAAI,eAAe;QACjB,QAAQ,MAAM,GAAG,CAAC,MAAM;IAC1B;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAE9B,IAAI,OAAO,OAAO;QAAE,WAAW;QAAO;IAAM;IAC5C,OAAO;QAAE,WAAW,KAAK,MAAM,KAAK;QAAG,OAAO;IAAK;AACrD;AAGO,MAAM,gBAAgB,OAAO;IAClC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,CAAC;;;IAGT,CAAC,EACA,EAAE,CAAC,MAAM,QACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,mBAAmB,OAAO;IACrC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,gBACP,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,mBAAmB,OAAO,QAAgB;IACrD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,iBAAiB,OAAO;IAQnC,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,eACL,MAAM,CAAC,CAAC;;;IAGT,CAAC;IAEH,IAAI,SAAS,OAAO;QAClB,QAAQ,MAAM,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAC,CAAC,CAAC;IACnD;IAEA,IAAI,SAAS,cAAc;QACzB,QAAQ,MAAM,GAAG,CAAC,uBAAuB,QAAQ,YAAY;IAC/D;IAEA,IAAI,SAAS,cAAc;QACzB,QAAQ,MAAM,GAAG,CAAC,uBAAuB,QAAQ,YAAY;IAC/D;IAEA,IAAI,SAAS,UAAU;QACrB,QAAQ,MAAM,EAAE,CAAC,qBAAqB,QAAQ,QAAQ;IACxD;IAEA,IAAI,SAAS,OAAO;QAClB,QAAQ,MAAM,KAAK,CAAC,QAAQ,KAAK;IACnC;IAEA,IAAI,SAAS,QAAQ;QACnB,QAAQ,MAAM,KAAK,CAAC,QAAQ,MAAM,EAAE,QAAQ,MAAM,GAAG,CAAC,QAAQ,KAAK,IAAI,EAAE,IAAI;IAC/E;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAC9B,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,MAAM,cAAc,OAAO;IAChC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,CAAC;;;IAGT,CAAC,EACA,EAAE,CAAC,MAAM,QACT,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,iBAAiB,OAAO;IACnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,cACP,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,iBAAiB,OAAO,QAAgB;IACnD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAGO,MAAM,gBAAgB;IAC3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,KACP,KAAK,CAAC;IAET,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yBACL,MAAM,CAAC,CAAC;;;;IAIT,CAAC,EACA,EAAE,CAAC,iBAAiB;IAEvB,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,6BAA6B,OAAO,cAAsB;IACrE,oCAAoC;IACpC,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,yBACL,MAAM,GACN,EAAE,CAAC,iBAAiB;IAEvB,6BAA6B;IAC7B,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,MAAM,eAAe,YAAY,GAAG,CAAC,CAAC,YAAY,QAAU,CAAC;gBAC3D,eAAe;gBACf,aAAa;gBACb,YAAY,UAAU,EAAE,4BAA4B;YACtD,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yBACL,MAAM,CAAC,cACP,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,OAAO;QAAE,MAAM,EAAE;QAAE,OAAO;IAAK;AACjC;AAEO,MAAM,8BAA8B,OAAO;IAChD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,8BACL,MAAM,CAAC,CAAC;;;IAGT,CAAC,EACA,EAAE,CAAC,eAAe;IAErB,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,iCAAiC,OAAO,YAAoB;IACvE,oCAAoC;IACpC,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,8BACL,MAAM,GACN,EAAE,CAAC,eAAe;IAErB,6BAA6B;IAC7B,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,MAAM,eAAe,YAAY,GAAG,CAAC,CAAA,aAAc,CAAC;gBAClD,aAAa;gBACb,aAAa;YACf,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,8BACL,MAAM,CAAC,cACP,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,OAAO;QAAE,MAAM,EAAE;QAAE,OAAO;IAAK;AACjC;AAGO,MAAM,iBAAiB,OAAO;IACnC,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,WAAW;IAEhE,IAAI,gBAAgB,CAAC,SAAS;QAC5B,OAAO;YAAE,MAAM;YAAM,OAAO;QAAa;IAC3C;IAEA,IAAI,QAAQ,SAAS,KAAK,cAAc;QACtC,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,cAAc;QACzE,OAAO;YACL,MAAM,aAAa;gBAAE,GAAG,OAAO;gBAAE;YAAW,IAAI;YAChD,OAAO;QACT;IACF,OAAO,IAAI,QAAQ,SAAS,KAAK,YAAY;QAC3C,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,YAAY;QACnE,OAAO;YACL,MAAM,WAAW;gBAAE,GAAG,OAAO;gBAAE;YAAS,IAAI;YAC5C,OAAO;QACT;IACF;IAEA,OAAO;QAAE,MAAM;QAAS,OAAO;IAAK;AACtC;AAGO,MAAM,eAAe,OAAO,QAAgB;IACjD,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;IACxC,MAAM,WAAW,GAAG,OAAO,CAAC,EAAE,KAAK,MAAM,GAAG,CAAC,EAAE,SAAS;IACxD,MAAM,WAAW,CAAC,QAAQ,EAAE,UAAU;IAEtC,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAClD,IAAI,CAAC,WACL,MAAM,CAAC,UAAU;IAEpB,IAAI,aAAa;QACf,OAAO;YAAE,MAAM;YAAM,OAAO;QAAY;IAC1C;IAEA,MAAM,EAAE,IAAI,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAC9B,IAAI,CAAC,WACL,YAAY,CAAC;IAEhB,qCAAqC;IACrC,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,cAAc,QAAQ;QACzD,YAAY,KAAK,SAAS;IAC5B;IAEA,IAAI,aAAa;QACf,OAAO;YAAE,MAAM;YAAM,OAAO;QAAY;IAC1C;IAEA,OAAO;QAAE,MAAM,KAAK,SAAS;QAAE,OAAO;IAAK;AAC7C;AAGO,MAAM,yBAAyB,OAAO;IAC3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wBACL,MAAM,CAAC,CAAC;;;IAGT,CAAC,EACA,EAAE,CAAC,iBAAiB,cACpB,EAAE,CAAC,aAAa;IAEnB,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,uBAAuB,OAAO;IACzC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,+BACL,MAAM,CAAC,CAAC;;;;IAIT,CAAC,EACA,EAAE,CAAC,iBAAiB,cACpB,EAAE,CAAC,gBAAgB;IAEtB,OAAO;QAAE;QAAM;IAAM;AACvB;AAEO,MAAM,4BAA4B,OAAO,cAAsB;IACpE,mCAAmC;IACnC,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,wBACL,MAAM,GACN,EAAE,CAAC,iBAAiB;IAEvB,4BAA4B;IAC5B,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,MAAM,eAAe,UAAU,GAAG,CAAC,CAAA,WAAY,CAAC;gBAC9C,eAAe;gBACf,aAAa,SAAS,WAAW;gBACjC,QAAQ,SAAS,MAAM,IAAI;gBAC3B,iBAAiB,SAAS,eAAe,IAAI;gBAC7C,aAAa;gBACb,WAAW;YACb,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,wBACL,MAAM,CAAC,cACP,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,OAAO;QAAE,MAAM,EAAE;QAAE,OAAO;IAAK;AACjC;AAEO,MAAM,0BAA0B,OAAO,cAAsB;IAClE,iCAAiC;IACjC,MAAM,sHAAA,CAAA,WAAQ,CACX,IAAI,CAAC,+BACL,MAAM,GACN,EAAE,CAAC,iBAAiB;IAEvB,0BAA0B;IAC1B,IAAI,QAAQ,MAAM,GAAG,GAAG;QACtB,MAAM,cAAc,QAAQ,GAAG,CAAC,CAAA,QAAS,CAAC;gBACxC,eAAe;gBACf,aAAa,MAAM,WAAW;gBAC9B,iBAAiB,MAAM,eAAe;gBACtC,OAAO,MAAM,KAAK;gBAClB,UAAU;gBACV,cAAc,MAAM,YAAY,KAAK;YACvC,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,+BACL,MAAM,CAAC,aACP,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,OAAO;QAAE,MAAM,EAAE;QAAE,OAAO;IAAK;AACjC", "debugId": null}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 536, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 591, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 761, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/navigation/DesktopNavigation.tsx"], "sourcesContent": ["'use client';\n\nimport { usePathname, useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Button } from '@/components/ui/button';\nimport { cn } from '@/lib/utils';\nimport {\n  LayoutDashboard,\n  User,\n  Settings,\n  Building2,\n  Users,\n  FileText,\n  MessageCircle,\n  DollarSign,\n  LogOut,\n  Menu,\n  Send,\n  Inbox\n} from 'lucide-react';\nimport { useState } from 'react';\nimport {\n  She<PERSON>,\n  SheetContent,\n  SheetHeader,\n  SheetTitle,\n  SheetTrigger,\n} from '@/components/ui/sheet';\n\ninterface SidebarItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  description?: string;\n}\n\ninterface DesktopNavigationProps {\n  userType: 'influencer' | 'business';\n  className?: string;\n}\n\nexport function DesktopNavigation({ userType, className }: DesktopNavigationProps) {\n  const pathname = usePathname();\n  const { signOut } = useAuth();\n  const router = useRouter();\n  const [isOpen, setIsOpen] = useState(false);\n\n  // Navigacija za influencer korisnike\n  const influencerNavigation: SidebarItem[] = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard/influencer',\n      icon: LayoutDashboard,\n      description: 'Pregled aktivnosti i statistika'\n    },\n    {\n      name: 'Moj račun',\n      href: '/dashboard/influencer/account',\n      icon: User,\n      description: 'Lični podaci i sigurnost'\n    },\n    {\n      name: 'Postavke profila',\n      href: '/dashboard/influencer/profile',\n      icon: Settings,\n      description: 'Javni profil i cijene'\n    },\n    {\n      name: 'Kampanje',\n      href: '/marketplace/campaigns',\n      icon: FileText,\n      description: 'Dostupne kampanje'\n    },\n    {\n      name: 'Ponude i aplikacije',\n      href: '/dashboard/influencer/offers',\n      icon: Inbox,\n      description: 'Direktne ponude i aplikacije'\n    },\n    {\n      name: 'Poruke',\n      href: '/dashboard/chat',\n      icon: MessageCircle,\n      description: 'Komunikacija sa brendovima'\n    },\n    {\n      name: 'Zarada',\n      href: '/dashboard/influencer/earnings',\n      icon: DollarSign,\n      description: 'Pregled zarade i isplate'\n    }\n  ];\n\n  // Navigacija za biznis korisnike\n  const businessNavigation: SidebarItem[] = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard/biznis',\n      icon: LayoutDashboard,\n      description: 'Pregled kampanja i statistika'\n    },\n    {\n      name: 'Moj račun',\n      href: '/dashboard/biznis/account',\n      icon: Building2,\n      description: 'Podaci o firmi i sigurnost'\n    },\n    {\n      name: 'Postavke profila',\n      href: '/dashboard/biznis/profile',\n      icon: Settings,\n      description: 'Javni profil firme'\n    },\n    {\n      name: 'Moje kampanje',\n      href: '/dashboard/campaigns',\n      icon: FileText,\n      description: 'Upravljanje kampanjama'\n    },\n    {\n      name: 'Aplikacije',\n      href: '/dashboard/biznis/applications',\n      icon: FileText,\n      description: 'Aplikacije na kampanje'\n    },\n    {\n      name: 'Moje ponude',\n      href: '/dashboard/biznis/offers',\n      icon: Send,\n      description: 'Direktne ponude influencerima'\n    },\n    {\n      name: 'Influenceri',\n      href: '/marketplace/influencers',\n      icon: Users,\n      description: 'Pronađi influencere'\n    },\n    {\n      name: 'Poruke',\n      href: '/dashboard/chat',\n      icon: MessageCircle,\n      description: 'Komunikacija sa influencerima'\n    }\n  ];\n\n  const navigation = userType === 'influencer' ? influencerNavigation : businessNavigation;\n\n  const handleSignOut = async () => {\n    try {\n      await signOut();\n      router.push('/');\n      setIsOpen(false);\n    } catch (error) {\n      console.error('Error signing out:', error);\n    }\n  };\n\n  return (\n    <div className={cn(\"hidden md:block\", className)}>\n      {/* Desktop Navigation Trigger */}\n      <Sheet open={isOpen} onOpenChange={setIsOpen}>\n        <SheetTrigger asChild>\n          <Button\n            variant=\"outline\"\n            size=\"icon\"\n            className=\"fixed top-4 left-4 z-40 bg-card border-border shadow-lg hover:bg-accent\"\n          >\n            <Menu className=\"h-4 w-4\" />\n            <span className=\"sr-only\">Otvori navigaciju</span>\n          </Button>\n        </SheetTrigger>\n        \n        <SheetContent side=\"left\" className=\"w-80 p-0\">\n          <div className=\"flex flex-col h-full\">\n            {/* Header */}\n            <SheetHeader className=\"p-6 border-b border-border\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-primary rounded-lg flex items-center justify-center\">\n                  <span className=\"text-primary-foreground font-bold text-lg\">🔗</span>\n                </div>\n                <SheetTitle className=\"text-lg font-bold text-foreground\">\n                  InfluConnect\n                </SheetTitle>\n              </div>\n            </SheetHeader>\n\n            {/* Navigation */}\n            <nav className=\"flex-1 px-4 py-6 space-y-2 overflow-y-auto\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href || pathname.startsWith(item.href + '/');\n                \n                return (\n                  <Link \n                    key={item.name} \n                    href={item.href}\n                    onClick={() => setIsOpen(false)}\n                  >\n                    <div className={cn(\n                      \"group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-colors\",\n                      isActive\n                        ? \"bg-primary text-primary-foreground\"\n                        : \"text-muted-foreground hover:bg-accent hover:text-accent-foreground\"\n                    )}>\n                      <item.icon className=\"flex-shrink-0 h-5 w-5 mr-3\" />\n                      <div className=\"flex-1\">\n                        <div className=\"font-medium\">{item.name}</div>\n                        {item.description && (\n                          <div className=\"text-xs opacity-75 mt-0.5\">\n                            {item.description}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </Link>\n                );\n              })}\n            </nav>\n\n            {/* Footer */}\n            <div className=\"p-4 border-t border-border\">\n              <Button\n                variant=\"ghost\"\n                onClick={handleSignOut}\n                className=\"w-full justify-start text-destructive hover:text-destructive hover:bg-destructive/10\"\n              >\n                <LogOut className=\"h-4 w-4 mr-2\" />\n                Odjava\n              </Button>\n            </div>\n          </div>\n        </SheetContent>\n      </Sheet>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AAtBA;;;;;;;;;;AA0CO,SAAS,kBAAkB,EAAE,QAAQ,EAAE,SAAS,EAA0B;IAC/E,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qCAAqC;IACrC,MAAM,uBAAsC;QAC1C;YACE,MAAM;YACN,MAAM;YACN,MAAM,4NAAA,CAAA,kBAAe;YACrB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,8MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;YACX,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,wNAAA,CAAA,gBAAa;YACnB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kNAAA,CAAA,aAAU;YAChB,aAAa;QACf;KACD;IAED,iCAAiC;IACjC,MAAM,qBAAoC;QACxC;YACE,MAAM;YACN,MAAM;YACN,MAAM,4NAAA,CAAA,kBAAe;YACrB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,gNAAA,CAAA,YAAS;YACf,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,8MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,8MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;YACX,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,wNAAA,CAAA,gBAAa;YACnB,aAAa;QACf;KACD;IAED,MAAM,aAAa,aAAa,eAAe,uBAAuB;IAEtE,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;YACZ,UAAU;QACZ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;kBAEpC,cAAA,8OAAC,iIAAA,CAAA,QAAK;YAAC,MAAM;YAAQ,cAAc;;8BACjC,8OAAC,iIAAA,CAAA,eAAY;oBAAC,OAAO;8BACnB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;8BAI9B,8OAAC,iIAAA,CAAA,eAAY;oBAAC,MAAK;oBAAO,WAAU;8BAClC,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,iIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;sDAE9D,8OAAC,iIAAA,CAAA,aAAU;4CAAC,WAAU;sDAAoC;;;;;;;;;;;;;;;;;0CAO9D,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,WAAW,aAAa,KAAK,IAAI,IAAI,SAAS,UAAU,CAAC,KAAK,IAAI,GAAG;oCAE3E,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,SAAS,IAAM,UAAU;kDAEzB,cAAA,8OAAC;4CAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,sFACA,WACI,uCACA;;8DAEJ,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;8DACrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAe,KAAK,IAAI;;;;;;wDACtC,KAAK,WAAW,kBACf,8OAAC;4DAAI,WAAU;sEACZ,KAAK,WAAW;;;;;;;;;;;;;;;;;;uCAfpB,KAAK,IAAI;;;;;gCAsBpB;;;;;;0CAIF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD", "debugId": null}}, {"offset": {"line": 1101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/navigation/MobileBottomNavigation.tsx"], "sourcesContent": ["'use client';\n\nimport { usePathname } from 'next/navigation';\nimport Link from 'next/link';\nimport { cn } from '@/lib/utils';\nimport {\n  LayoutDashboard,\n  FileText,\n  Inbox,\n  MessageCircle,\n  Menu,\n  User,\n  Settings,\n  DollarSign,\n  LogOut\n} from 'lucide-react';\nimport { useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport {\n  Sheet,\n  <PERSON>et<PERSON>ontent,\n  SheetHeader,\n  SheetTitle,\n  SheetTrigger,\n} from '@/components/ui/sheet';\nimport { Button } from '@/components/ui/button';\n\ninterface MobileBottomNavigationProps {\n  userType: 'influencer' | 'business';\n}\n\ninterface NavItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  label: string;\n}\n\ninterface MenuNavItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  description?: string;\n}\n\nexport function MobileBottomNavigation({ userType }: MobileBottomNavigationProps) {\n  const pathname = usePathname();\n  const { signOut } = useAuth();\n  const router = useRouter();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  // Glavne 4 ikonice za influencere\n  const influencerMainNav: NavItem[] = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard/influencer',\n      icon: LayoutDashboard,\n      label: 'Home'\n    },\n    {\n      name: 'Kampanje',\n      href: '/marketplace/campaigns',\n      icon: FileText,\n      label: 'Prilike'\n    },\n    {\n      name: 'Ponude',\n      href: '/dashboard/influencer/offers',\n      icon: Inbox,\n      label: 'Ponude'\n    },\n    {\n      name: 'Poruke',\n      href: '/dashboard/chat',\n      icon: MessageCircle,\n      label: 'Poruke'\n    }\n  ];\n\n  // Glavne 4 ikonice za biznis korisnike\n  const businessMainNav: NavItem[] = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard/biznis',\n      icon: LayoutDashboard,\n      label: 'Home'\n    },\n    {\n      name: 'Kampanje',\n      href: '/dashboard/campaigns',\n      icon: FileText,\n      label: 'Kampanje'\n    },\n    {\n      name: 'Aplikacije',\n      href: '/dashboard/biznis/applications',\n      icon: Inbox,\n      label: 'Aplikacije'\n    },\n    {\n      name: 'Poruke',\n      href: '/dashboard/chat',\n      icon: MessageCircle,\n      label: 'Poruke'\n    }\n  ];\n\n  // Ostali elementi za hamburger meni - influenceri\n  const influencerMenuNav: MenuNavItem[] = [\n    {\n      name: 'Moj račun',\n      href: '/dashboard/influencer/account',\n      icon: User,\n      description: 'Lični podaci i sigurnost'\n    },\n    {\n      name: 'Postavke profila',\n      href: '/dashboard/influencer/profile',\n      icon: Settings,\n      description: 'Javni profil i cijene'\n    },\n    {\n      name: 'Zarada',\n      href: '/dashboard/influencer/earnings',\n      icon: DollarSign,\n      description: 'Pregled zarade i isplate'\n    }\n  ];\n\n  // Ostali elementi za hamburger meni - biznis\n  const businessMenuNav: MenuNavItem[] = [\n    {\n      name: 'Moj račun',\n      href: '/dashboard/biznis/account',\n      icon: User,\n      description: 'Podaci o firmi i sigurnost'\n    },\n    {\n      name: 'Postavke profila',\n      href: '/dashboard/biznis/profile',\n      icon: Settings,\n      description: 'Javni profil firme'\n    },\n    {\n      name: 'Influenceri',\n      href: '/marketplace/influencers',\n      icon: User,\n      description: 'Pronađi influencere'\n    }\n  ];\n\n  const mainNavigation = userType === 'influencer' ? influencerMainNav : businessMainNav;\n  const menuNavigation = userType === 'influencer' ? influencerMenuNav : businessMenuNav;\n\n  const handleSignOut = async () => {\n    try {\n      await signOut();\n      router.push('/');\n      setIsMenuOpen(false);\n    } catch (error) {\n      console.error('Error signing out:', error);\n    }\n  };\n\n  const isActive = (href: string) => {\n    return pathname === href || pathname.startsWith(href + '/');\n  };\n\n  return (\n    <>\n      {/* Mobile Bottom Navigation */}\n      <div className=\"fixed bottom-0 left-0 right-0 z-50 bg-card border-t border-border md:hidden\">\n        <div className=\"flex items-center justify-around py-2\">\n          {/* Glavne 4 ikonice */}\n          {mainNavigation.map((item) => (\n            <Link key={item.name} href={item.href}>\n              <div className={cn(\n                \"flex flex-col items-center justify-center p-2 min-w-[60px] rounded-lg transition-colors\",\n                isActive(item.href)\n                  ? \"text-primary bg-primary/10\"\n                  : \"text-muted-foreground hover:text-foreground hover:bg-accent\"\n              )}>\n                <item.icon className=\"h-5 w-5 mb-1\" />\n                <span className=\"text-xs font-medium\">{item.label}</span>\n              </div>\n            </Link>\n          ))}\n\n          {/* Hamburger Menu */}\n          <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>\n            <SheetTrigger asChild>\n              <div className=\"flex flex-col items-center justify-center p-2 min-w-[60px] rounded-lg transition-colors text-muted-foreground hover:text-foreground hover:bg-accent cursor-pointer\">\n                <Menu className=\"h-5 w-5 mb-1\" />\n                <span className=\"text-xs font-medium\">Više</span>\n              </div>\n            </SheetTrigger>\n            <SheetContent side=\"bottom\" className=\"h-auto max-h-[80vh]\">\n              <SheetHeader>\n                <SheetTitle>Meni</SheetTitle>\n              </SheetHeader>\n              \n              <div className=\"grid gap-4 py-4\">\n                {menuNavigation.map((item) => (\n                  <Link \n                    key={item.name} \n                    href={item.href}\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    <div className={cn(\n                      \"flex items-center space-x-3 p-3 rounded-lg transition-colors\",\n                      isActive(item.href)\n                        ? \"bg-primary text-primary-foreground\"\n                        : \"hover:bg-accent\"\n                    )}>\n                      <item.icon className=\"h-5 w-5\" />\n                      <div className=\"flex-1\">\n                        <div className=\"font-medium\">{item.name}</div>\n                        {item.description && (\n                          <div className=\"text-sm opacity-75 mt-0.5\">\n                            {item.description}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </Link>\n                ))}\n                \n                {/* Odjava */}\n                <Button\n                  variant=\"ghost\"\n                  onClick={handleSignOut}\n                  className=\"flex items-center justify-start space-x-3 p-3 w-full text-destructive hover:text-destructive hover:bg-destructive/10\"\n                >\n                  <LogOut className=\"h-5 w-5\" />\n                  <span className=\"font-medium\">Odjava</span>\n                </Button>\n              </div>\n            </SheetContent>\n          </Sheet>\n        </div>\n      </div>\n\n      {/* Spacer za bottom navigation */}\n      <div className=\"h-16 md:hidden\" />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AAEA;AAOA;AA1BA;;;;;;;;;;;AA8CO,SAAS,uBAAuB,EAAE,QAAQ,EAA+B;IAC9E,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,kCAAkC;IAClC,MAAM,oBAA+B;QACnC;YACE,MAAM;YACN,MAAM;YACN,MAAM,4NAAA,CAAA,kBAAe;YACrB,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,8MAAA,CAAA,WAAQ;YACd,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;QACT;KACD;IAED,uCAAuC;IACvC,MAAM,kBAA6B;QACjC;YACE,MAAM;YACN,MAAM;YACN,MAAM,4NAAA,CAAA,kBAAe;YACrB,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,8MAAA,CAAA,WAAQ;YACd,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;QACT;KACD;IAED,kDAAkD;IAClD,MAAM,oBAAmC;QACvC;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kNAAA,CAAA,aAAU;YAChB,aAAa;QACf;KACD;IAED,6CAA6C;IAC7C,MAAM,kBAAiC;QACrC;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,aAAa;QACf;KACD;IAED,MAAM,iBAAiB,aAAa,eAAe,oBAAoB;IACvE,MAAM,iBAAiB,aAAa,eAAe,oBAAoB;IAEvE,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;YACZ,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,OAAO,aAAa,QAAQ,SAAS,UAAU,CAAC,OAAO;IACzD;IAEA,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBAEZ,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,4JAAA,CAAA,UAAI;gCAAiB,MAAM,KAAK,IAAI;0CACnC,cAAA,8OAAC;oCAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,2FACA,SAAS,KAAK,IAAI,IACd,+BACA;;sDAEJ,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,8OAAC;4CAAK,WAAU;sDAAuB,KAAK,KAAK;;;;;;;;;;;;+BAR1C,KAAK,IAAI;;;;;sCActB,8OAAC,iIAAA,CAAA,QAAK;4BAAC,MAAM;4BAAY,cAAc;;8CACrC,8OAAC,iIAAA,CAAA,eAAY;oCAAC,OAAO;8CACnB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;;;;;;8CAG1C,8OAAC,iIAAA,CAAA,eAAY;oCAAC,MAAK;oCAAS,WAAU;;sDACpC,8OAAC,iIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC,iIAAA,CAAA,aAAU;0DAAC;;;;;;;;;;;sDAGd,8OAAC;4CAAI,WAAU;;gDACZ,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,4JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,SAAS,IAAM,cAAc;kEAE7B,cAAA,8OAAC;4DAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,gEACA,SAAS,KAAK,IAAI,IACd,uCACA;;8EAEJ,8OAAC,KAAK,IAAI;oEAAC,WAAU;;;;;;8EACrB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAAe,KAAK,IAAI;;;;;;wEACtC,KAAK,WAAW,kBACf,8OAAC;4EAAI,WAAU;sFACZ,KAAK,WAAW;;;;;;;;;;;;;;;;;;uDAfpB,KAAK,IAAI;;;;;8DAwBlB,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS;oDACT,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS1C,8OAAC;gBAAI,WAAU;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 1458, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/navigation/ResponsiveNavigation.tsx"], "sourcesContent": ["'use client';\n\nimport { DesktopNavigation } from './DesktopNavigation';\nimport { MobileBottomNavigation } from './MobileBottomNavigation';\n\ninterface ResponsiveNavigationProps {\n  userType: 'influencer' | 'business';\n}\n\nexport function ResponsiveNavigation({ userType }: ResponsiveNavigationProps) {\n  return (\n    <>\n      {/* Desktop Navigation - prikazuje se samo na desktop uređajima */}\n      <DesktopNavigation userType={userType} />\n      \n      {/* Mobile Bottom Navigation - prikazuje se samo na mobile uređajima */}\n      <MobileBottomNavigation userType={userType} />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASO,SAAS,qBAAqB,EAAE,QAAQ,EAA6B;IAC1E,qBACE;;0BAEE,8OAAC,qJAAA,CAAA,oBAAiB;gBAAC,UAAU;;;;;;0BAG7B,8OAAC,0JAAA,CAAA,yBAAsB;gBAAC,UAAU;;;;;;;;AAGxC", "debugId": null}}, {"offset": {"line": 1492, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1536, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction ScrollArea({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\n  return (\n    <ScrollAreaPrimitive.Root\n      data-slot=\"scroll-area\"\n      className={cn(\"relative\", className)}\n      {...props}\n    >\n      <ScrollAreaPrimitive.Viewport\n        data-slot=\"scroll-area-viewport\"\n        className=\"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1\"\n      >\n        {children}\n      </ScrollAreaPrimitive.Viewport>\n      <ScrollBar />\n      <ScrollAreaPrimitive.Corner />\n    </ScrollAreaPrimitive.Root>\n  )\n}\n\nfunction ScrollBar({\n  className,\n  orientation = \"vertical\",\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\n  return (\n    <ScrollAreaPrimitive.ScrollAreaScrollbar\n      data-slot=\"scroll-area-scrollbar\"\n      orientation={orientation}\n      className={cn(\n        \"flex touch-none p-px transition-colors select-none\",\n        orientation === \"vertical\" &&\n          \"h-full w-2.5 border-l border-l-transparent\",\n        orientation === \"horizontal\" &&\n          \"h-2.5 flex-col border-t border-t-transparent\",\n        className\n      )}\n      {...props}\n    >\n      <ScrollAreaPrimitive.ScrollAreaThumb\n        data-slot=\"scroll-area-thumb\"\n        className=\"bg-border relative flex-1 rounded-full\"\n      />\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>\n  )\n}\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAC3B,aAAU;gBACV,WAAU;0BAET;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAGjC;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,UAAU,EACxB,GAAG,OACkE;IACrE,qBACE,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,aAAU;QACV,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,gBAAgB,cACd,8CACF,gBAAgB,gBACd,gDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAClC,aAAU;YACV,WAAU;;;;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 1604, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1864, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/lib/notifications.ts"], "sourcesContent": ["import { supabase } from './supabase';\n\nexport interface Notification {\n  id: string;\n  user_id: string;\n  type: string;\n  title: string;\n  message: string;\n  data: Record<string, any>;\n  read: boolean;\n  created_at: string;\n  updated_at: string;\n}\n\nexport type NotificationType = \n  | 'offer_received'\n  | 'offer_accepted'\n  | 'offer_rejected'\n  | 'campaign_application'\n  | 'campaign_accepted'\n  | 'campaign_rejected'\n  | 'message_received'\n  | 'payment_received';\n\n// Create a new notification\nexport async function createNotification(\n  userId: string,\n  type: NotificationType,\n  title: string,\n  message: string,\n  data: Record<string, any> = {}\n) {\n  const { data: notification, error } = await supabase.rpc('create_notification', {\n    p_user_id: userId,\n    p_type: type,\n    p_title: title,\n    p_message: message,\n    p_data: data\n  });\n\n  if (error) {\n    console.error('Error creating notification:', error);\n    return { data: null, error };\n  }\n\n  return { data: notification, error: null };\n}\n\n// Get user notifications\nexport async function getUserNotifications(userId?: string, limit = 50) {\n  let query = supabase\n    .from('notifications')\n    .select('*')\n    .order('created_at', { ascending: false })\n    .limit(limit);\n\n  if (userId) {\n    query = query.eq('user_id', userId);\n  }\n\n  const { data, error } = await query;\n\n  if (error) {\n    console.error('Error fetching notifications:', error);\n    return { data: null, error };\n  }\n\n  return { data, error: null };\n}\n\n// Mark notification as read\nexport async function markNotificationAsRead(notificationId: string) {\n  const { data, error } = await supabase\n    .from('notifications')\n    .update({ read: true })\n    .eq('id', notificationId)\n    .select()\n    .single();\n\n  if (error) {\n    console.error('Error marking notification as read:', error);\n    return { data: null, error };\n  }\n\n  return { data, error: null };\n}\n\n// Mark all notifications as read for user\nexport async function markAllNotificationsAsRead(userId: string) {\n  const { data, error } = await supabase\n    .from('notifications')\n    .update({ read: true })\n    .eq('user_id', userId)\n    .eq('read', false);\n\n  if (error) {\n    console.error('Error marking all notifications as read:', error);\n    return { data: null, error };\n  }\n\n  return { data, error: null };\n}\n\n// Get unread notification count\nexport async function getUnreadNotificationCount(userId: string) {\n  const { count, error } = await supabase\n    .from('notifications')\n    .select('*', { count: 'exact', head: true })\n    .eq('user_id', userId)\n    .eq('read', false);\n\n  if (error) {\n    console.error('Error getting unread count:', error);\n    return { count: 0, error };\n  }\n\n  return { count: count || 0, error: null };\n}\n\n// Delete notification\nexport async function deleteNotification(notificationId: string) {\n  const { error } = await supabase\n    .from('notifications')\n    .delete()\n    .eq('id', notificationId);\n\n  if (error) {\n    console.error('Error deleting notification:', error);\n    return { error };\n  }\n\n  return { error: null };\n}\n\n// Helper functions for specific notification types\n\nexport async function notifyOfferReceived(\n  influencerId: string,\n  businessName: string,\n  offerTitle: string,\n  offerId: string\n) {\n  return createNotification(\n    influencerId,\n    'offer_received',\n    'Nova direktna ponuda',\n    `${businessName} vam je poslao ponudu: \"${offerTitle}\"`,\n    { offer_id: offerId, business_name: businessName }\n  );\n}\n\nexport async function notifyOfferAccepted(\n  businessId: string,\n  influencerName: string,\n  offerTitle: string,\n  offerId: string\n) {\n  return createNotification(\n    businessId,\n    'offer_accepted',\n    'Ponuda prihvaćena',\n    `${influencerName} je prihvatio vašu ponudu: \"${offerTitle}\"`,\n    { offer_id: offerId, influencer_name: influencerName }\n  );\n}\n\nexport async function notifyOfferRejected(\n  businessId: string,\n  influencerName: string,\n  offerTitle: string,\n  offerId: string\n) {\n  return createNotification(\n    businessId,\n    'offer_rejected',\n    'Ponuda odbijena',\n    `${influencerName} je odbio vašu ponudu: \"${offerTitle}\"`,\n    { offer_id: offerId, influencer_name: influencerName }\n  );\n}\n\nexport async function notifyCampaignApplication(\n  businessId: string,\n  influencerName: string,\n  campaignTitle: string,\n  applicationId: string\n) {\n  return createNotification(\n    businessId,\n    'campaign_application',\n    'Nova aplikacija na kampanju',\n    `${influencerName} se prijavio na kampanju: \"${campaignTitle}\"`,\n    { application_id: applicationId, influencer_name: influencerName }\n  );\n}\n\nexport async function notifyCampaignAccepted(\n  influencerId: string,\n  businessName: string,\n  campaignTitle: string,\n  applicationId: string\n) {\n  return createNotification(\n    influencerId,\n    'campaign_accepted',\n    'Aplikacija prihvaćena',\n    `${businessName} je prihvatio vašu aplikaciju za kampanju: \"${campaignTitle}\"`,\n    { application_id: applicationId, business_name: businessName }\n  );\n}\n\nexport async function notifyCampaignRejected(\n  influencerId: string,\n  businessName: string,\n  campaignTitle: string,\n  applicationId: string\n) {\n  return createNotification(\n    influencerId,\n    'campaign_rejected',\n    'Aplikacija odbijena',\n    `${businessName} je odbio vašu aplikaciju za kampanju: \"${campaignTitle}\"`,\n    { application_id: applicationId, business_name: businessName }\n  );\n}\n\nexport async function notifyMessageReceived(\n  userId: string,\n  senderName: string,\n  conversationId: string\n) {\n  return createNotification(\n    userId,\n    'message_received',\n    'Nova poruka',\n    `${senderName} vam je poslao novu poruku`,\n    { conversation_id: conversationId, sender_name: senderName }\n  );\n}\n\nexport async function notifyPaymentReceived(\n  influencerId: string,\n  amount: number,\n  currency: string,\n  campaignTitle: string\n) {\n  return createNotification(\n    influencerId,\n    'payment_received',\n    'Plaćanje primljeno',\n    `Primili ste plaćanje od ${amount} ${currency} za kampanju: \"${campaignTitle}\"`,\n    { amount, currency, campaign_title: campaignTitle }\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;;AAyBO,eAAe,mBACpB,MAAc,EACd,IAAsB,EACtB,KAAa,EACb,OAAe,EACf,OAA4B,CAAC,CAAC;IAE9B,MAAM,EAAE,MAAM,YAAY,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,uBAAuB;QAC9E,WAAW;QACX,QAAQ;QACR,SAAS;QACT,WAAW;QACX,QAAQ;IACV;IAEA,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,OAAO;QAAE,MAAM;QAAc,OAAO;IAAK;AAC3C;AAGO,eAAe,qBAAqB,MAAe,EAAE,QAAQ,EAAE;IACpE,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM,GACvC,KAAK,CAAC;IAET,IAAI,QAAQ;QACV,QAAQ,MAAM,EAAE,CAAC,WAAW;IAC9B;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAE9B,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,OAAO;QAAE;QAAM,OAAO;IAAK;AAC7B;AAGO,eAAe,uBAAuB,cAAsB;IACjE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC;QAAE,MAAM;IAAK,GACpB,EAAE,CAAC,MAAM,gBACT,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,OAAO;QAAE;QAAM,OAAO;IAAK;AAC7B;AAGO,eAAe,2BAA2B,MAAc;IAC7D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC;QAAE,MAAM;IAAK,GACpB,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,QAAQ;IAEd,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;YAAE,MAAM;YAAM;QAAM;IAC7B;IAEA,OAAO;QAAE;QAAM,OAAO;IAAK;AAC7B;AAGO,eAAe,2BAA2B,MAAc;IAC7D,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACpC,IAAI,CAAC,iBACL,MAAM,CAAC,KAAK;QAAE,OAAO;QAAS,MAAM;IAAK,GACzC,EAAE,CAAC,WAAW,QACd,EAAE,CAAC,QAAQ;IAEd,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YAAE,OAAO;YAAG;QAAM;IAC3B;IAEA,OAAO;QAAE,OAAO,SAAS;QAAG,OAAO;IAAK;AAC1C;AAGO,eAAe,mBAAmB,cAAsB;IAC7D,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,iBACL,MAAM,GACN,EAAE,CAAC,MAAM;IAEZ,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YAAE;QAAM;IACjB;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB;AAIO,eAAe,oBACpB,YAAoB,EACpB,YAAoB,EACpB,UAAkB,EAClB,OAAe;IAEf,OAAO,mBACL,cACA,kBACA,wBACA,GAAG,aAAa,wBAAwB,EAAE,WAAW,CAAC,CAAC,EACvD;QAAE,UAAU;QAAS,eAAe;IAAa;AAErD;AAEO,eAAe,oBACpB,UAAkB,EAClB,cAAsB,EACtB,UAAkB,EAClB,OAAe;IAEf,OAAO,mBACL,YACA,kBACA,qBACA,GAAG,eAAe,4BAA4B,EAAE,WAAW,CAAC,CAAC,EAC7D;QAAE,UAAU;QAAS,iBAAiB;IAAe;AAEzD;AAEO,eAAe,oBACpB,UAAkB,EAClB,cAAsB,EACtB,UAAkB,EAClB,OAAe;IAEf,OAAO,mBACL,YACA,kBACA,mBACA,GAAG,eAAe,wBAAwB,EAAE,WAAW,CAAC,CAAC,EACzD;QAAE,UAAU;QAAS,iBAAiB;IAAe;AAEzD;AAEO,eAAe,0BACpB,UAAkB,EAClB,cAAsB,EACtB,aAAqB,EACrB,aAAqB;IAErB,OAAO,mBACL,YACA,wBACA,+BACA,GAAG,eAAe,2BAA2B,EAAE,cAAc,CAAC,CAAC,EAC/D;QAAE,gBAAgB;QAAe,iBAAiB;IAAe;AAErE;AAEO,eAAe,uBACpB,YAAoB,EACpB,YAAoB,EACpB,aAAqB,EACrB,aAAqB;IAErB,OAAO,mBACL,cACA,qBACA,yBACA,GAAG,aAAa,4CAA4C,EAAE,cAAc,CAAC,CAAC,EAC9E;QAAE,gBAAgB;QAAe,eAAe;IAAa;AAEjE;AAEO,eAAe,uBACpB,YAAoB,EACpB,YAAoB,EACpB,aAAqB,EACrB,aAAqB;IAErB,OAAO,mBACL,cACA,qBACA,uBACA,GAAG,aAAa,wCAAwC,EAAE,cAAc,CAAC,CAAC,EAC1E;QAAE,gBAAgB;QAAe,eAAe;IAAa;AAEjE;AAEO,eAAe,sBACpB,MAAc,EACd,UAAkB,EAClB,cAAsB;IAEtB,OAAO,mBACL,QACA,oBACA,eACA,GAAG,WAAW,0BAA0B,CAAC,EACzC;QAAE,iBAAiB;QAAgB,aAAa;IAAW;AAE/D;AAEO,eAAe,sBACpB,YAAoB,EACpB,MAAc,EACd,QAAgB,EAChB,aAAqB;IAErB,OAAO,mBACL,cACA,oBACA,sBACA,CAAC,wBAAwB,EAAE,OAAO,CAAC,EAAE,SAAS,eAAe,EAAE,cAAc,CAAC,CAAC,EAC/E;QAAE;QAAQ;QAAU,gBAAgB;IAAc;AAEtD", "debugId": null}}, {"offset": {"line": 2036, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/notifications/NotificationDropdown.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuLabel,\n  DropdownMenuTrigger,\n  DropdownMenuSeparator,\n  DropdownMenuItem,\n} from '@/components/ui/dropdown-menu';\nimport { \n  Bell, \n  Check, \n  CheckCheck,\n  Inbox,\n  MessageCircle,\n  DollarSign,\n  FileText,\n  Building2,\n  User\n} from 'lucide-react';\nimport { \n  getUserNotifications, \n  markNotificationAsRead, \n  markAllNotificationsAsRead,\n  getUnreadNotificationCount,\n  type Notification \n} from '@/lib/notifications';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { formatDistanceToNow } from 'date-fns';\nimport { hr } from 'date-fns/locale';\nimport { toast } from 'sonner';\nimport Link from 'next/link';\n\nexport function NotificationDropdown() {\n  const { user } = useAuth();\n  const [notifications, setNotifications] = useState<Notification[]>([]);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isOpen, setIsOpen] = useState(false);\n\n  useEffect(() => {\n    if (user) {\n      loadNotifications();\n      loadUnreadCount();\n    }\n  }, [user]);\n\n  const loadNotifications = async () => {\n    if (!user) return;\n    \n    setIsLoading(true);\n    try {\n      const { data, error } = await getUserNotifications(user.id, 20);\n      if (error) {\n        console.error('Error loading notifications:', error);\n      } else {\n        setNotifications(data || []);\n      }\n    } catch (error) {\n      console.error('Error loading notifications:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loadUnreadCount = async () => {\n    if (!user) return;\n    \n    try {\n      const { count, error } = await getUnreadNotificationCount(user.id);\n      if (error) {\n        console.error('Error loading unread count:', error);\n      } else {\n        setUnreadCount(count);\n      }\n    } catch (error) {\n      console.error('Error loading unread count:', error);\n    }\n  };\n\n  const handleMarkAsRead = async (notificationId: string) => {\n    try {\n      const { error } = await markNotificationAsRead(notificationId);\n      if (error) {\n        toast.error('Greška pri označavanju notifikacije');\n      } else {\n        setNotifications(prev => \n          prev.map(n => n.id === notificationId ? { ...n, read: true } : n)\n        );\n        setUnreadCount(prev => Math.max(0, prev - 1));\n      }\n    } catch (error) {\n      console.error('Error marking notification as read:', error);\n      toast.error('Greška pri označavanju notifikacije');\n    }\n  };\n\n  const handleMarkAllAsRead = async () => {\n    if (!user) return;\n    \n    try {\n      const { error } = await markAllNotificationsAsRead(user.id);\n      if (error) {\n        toast.error('Greška pri označavanju notifikacija');\n      } else {\n        setNotifications(prev => prev.map(n => ({ ...n, read: true })));\n        setUnreadCount(0);\n        toast.success('Sve notifikacije su označene kao pročitane');\n      }\n    } catch (error) {\n      console.error('Error marking all notifications as read:', error);\n      toast.error('Greška pri označavanju notifikacija');\n    }\n  };\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'offer_received':\n      case 'offer_accepted':\n      case 'offer_rejected':\n        return <Inbox className=\"h-4 w-4\" />;\n      case 'campaign_application':\n      case 'campaign_accepted':\n      case 'campaign_rejected':\n        return <FileText className=\"h-4 w-4\" />;\n      case 'message_received':\n        return <MessageCircle className=\"h-4 w-4\" />;\n      case 'payment_received':\n        return <DollarSign className=\"h-4 w-4\" />;\n      default:\n        return <Bell className=\"h-4 w-4\" />;\n    }\n  };\n\n  const getNotificationLink = (notification: Notification) => {\n    switch (notification.type) {\n      case 'offer_received':\n      case 'offer_accepted':\n      case 'offer_rejected':\n        if (notification.data.offer_id) {\n          return `/dashboard/influencer/offers/${notification.data.offer_id}`;\n        }\n        return '/dashboard/influencer/offers';\n      case 'campaign_application':\n      case 'campaign_accepted':\n      case 'campaign_rejected':\n        return '/dashboard/campaigns';\n      case 'message_received':\n        return '/dashboard/messages';\n      default:\n        return '/dashboard';\n    }\n  };\n\n  if (!user) return null;\n\n  return (\n    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n          <Bell className=\"h-5 w-5\" />\n          {unreadCount > 0 && (\n            <Badge \n              variant=\"destructive\" \n              className=\"absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n            >\n              {unreadCount > 99 ? '99+' : unreadCount}\n            </Badge>\n          )}\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\" className=\"w-80\">\n        <DropdownMenuLabel className=\"flex items-center justify-between p-4\">\n          <h3 className=\"font-semibold\">Notifikacije</h3>\n          {unreadCount > 0 && (\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={handleMarkAllAsRead}\n              className=\"text-xs\"\n            >\n              <CheckCheck className=\"h-3 w-3 mr-1\" />\n              Označi sve\n            </Button>\n          )}\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        \n        <ScrollArea className=\"h-96\">\n          {isLoading ? (\n            <div className=\"flex items-center justify-center p-8\">\n              <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-primary\"></div>\n            </div>\n          ) : notifications.length === 0 ? (\n            <div className=\"flex flex-col items-center justify-center p-8 text-center\">\n              <Bell className=\"h-8 w-8 text-muted-foreground mb-2\" />\n              <p className=\"text-sm text-muted-foreground\">Nema novih notifikacija</p>\n            </div>\n          ) : (\n            <div className=\"space-y-1\">\n              {notifications.map((notification) => (\n                <div\n                  key={notification.id}\n                  className={`p-3 hover:bg-muted/50 transition-colors border-l-2 ${\n                    notification.read ? 'border-transparent' : 'border-primary'\n                  }`}\n                >\n                  <Link \n                    href={getNotificationLink(notification)}\n                    onClick={() => {\n                      if (!notification.read) {\n                        handleMarkAsRead(notification.id);\n                      }\n                      setIsOpen(false);\n                    }}\n                    className=\"block\"\n                  >\n                    <div className=\"flex items-start gap-3\">\n                      <div className=\"flex-shrink-0 mt-0.5\">\n                        {getNotificationIcon(notification.type)}\n                      </div>\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-start justify-between\">\n                          <h4 className={`text-sm font-medium ${\n                            notification.read ? 'text-muted-foreground' : 'text-foreground'\n                          }`}>\n                            {notification.title}\n                          </h4>\n                          {!notification.read && (\n                            <Button\n                              variant=\"ghost\"\n                              size=\"sm\"\n                              onClick={(e) => {\n                                e.preventDefault();\n                                e.stopPropagation();\n                                handleMarkAsRead(notification.id);\n                              }}\n                              className=\"h-6 w-6 p-0 ml-2\"\n                            >\n                              <Check className=\"h-3 w-3\" />\n                            </Button>\n                          )}\n                        </div>\n                        <p className={`text-xs mt-1 ${\n                          notification.read ? 'text-muted-foreground' : 'text-muted-foreground'\n                        }`}>\n                          {notification.message}\n                        </p>\n                        <p className=\"text-xs text-muted-foreground mt-1\">\n                          {formatDistanceToNow(new Date(notification.created_at), { \n                            addSuffix: true, \n                            locale: hr \n                          })}\n                        </p>\n                      </div>\n                    </div>\n                  </Link>\n                </div>\n              ))}\n            </div>\n          )}\n        </ScrollArea>\n        \n        {notifications.length > 0 && (\n          <>\n            <DropdownMenuSeparator />\n            <div className=\"p-2\">\n              <Button variant=\"ghost\" size=\"sm\" className=\"w-full\" asChild>\n                <Link href=\"/dashboard/notifications\">\n                  Pogledaj sve notifikacije\n                </Link>\n              </Button>\n            </div>\n          </>\n        )}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAOA;AACA;AACA;AACA;AACA;AApCA;;;;;;;;;;;;;;AAsCO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;YACA;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,oBAAoB;QACxB,IAAI,CAAC,MAAM;QAEX,aAAa;QACb,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,2HAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,EAAE,EAAE;YAC5D,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,gCAAgC;YAChD,OAAO;gBACL,iBAAiB,QAAQ,EAAE;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,2HAAA,CAAA,6BAA0B,AAAD,EAAE,KAAK,EAAE;YACjE,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,+BAA+B;YAC/C,OAAO;gBACL,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,2HAAA,CAAA,yBAAsB,AAAD,EAAE;YAC/C,IAAI,OAAO;gBACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,iBAAiB;4BAAE,GAAG,CAAC;4BAAE,MAAM;wBAAK,IAAI;gBAEjE,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,2HAAA,CAAA,6BAA0B,AAAD,EAAE,KAAK,EAAE;YAC1D,IAAI,OAAO;gBACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,iBAAiB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC;4BAAE,GAAG,CAAC;4BAAE,MAAM;wBAAK,CAAC;gBAC5D,eAAe;gBACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,kNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ,aAAa,IAAI;YACvB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,IAAI,aAAa,IAAI,CAAC,QAAQ,EAAE;oBAC9B,OAAO,CAAC,6BAA6B,EAAE,aAAa,IAAI,CAAC,QAAQ,EAAE;gBACrE;gBACA,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC,4IAAA,CAAA,eAAY;QAAC,MAAM;QAAQ,cAAc;;0BACxC,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAK,WAAU;;sCAC1C,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACf,cAAc,mBACb,8OAAC,iIAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,WAAU;sCAET,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAKpC,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAM;gBAAM,WAAU;;kCACzC,8OAAC,4IAAA,CAAA,oBAAiB;wBAAC,WAAU;;0CAC3B,8OAAC;gCAAG,WAAU;0CAAgB;;;;;;4BAC7B,cAAc,mBACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAK7C,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCAEtB,8OAAC,0IAAA,CAAA,aAAU;wBAAC,WAAU;kCACnB,0BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;mCAEf,cAAc,MAAM,KAAK,kBAC3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;iDAG/C,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,8OAAC;oCAEC,WAAW,CAAC,mDAAmD,EAC7D,aAAa,IAAI,GAAG,uBAAuB,kBAC3C;8CAEF,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,oBAAoB;wCAC1B,SAAS;4CACP,IAAI,CAAC,aAAa,IAAI,EAAE;gDACtB,iBAAiB,aAAa,EAAE;4CAClC;4CACA,UAAU;wCACZ;wCACA,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,oBAAoB,aAAa,IAAI;;;;;;8DAExC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAW,CAAC,oBAAoB,EAClC,aAAa,IAAI,GAAG,0BAA0B,mBAC9C;8EACC,aAAa,KAAK;;;;;;gEAEpB,CAAC,aAAa,IAAI,kBACjB,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,CAAC;wEACR,EAAE,cAAc;wEAChB,EAAE,eAAe;wEACjB,iBAAiB,aAAa,EAAE;oEAClC;oEACA,WAAU;8EAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAIvB,8OAAC;4DAAE,WAAW,CAAC,aAAa,EAC1B,aAAa,IAAI,GAAG,0BAA0B,yBAC9C;sEACC,aAAa,OAAO;;;;;;sEAEvB,8OAAC;4DAAE,WAAU;sEACV,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,aAAa,UAAU,GAAG;gEACtD,WAAW;gEACX,QAAQ,2IAAA,CAAA,KAAE;4DACZ;;;;;;;;;;;;;;;;;;;;;;;mCAlDH,aAAa,EAAE;;;;;;;;;;;;;;;oBA6D7B,cAAc,MAAM,GAAG,mBACtB;;0CACE,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0CACtB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAS,OAAO;8CAC1D,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtD", "debugId": null}}, {"offset": {"line": 2508, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/dashboard/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { getProfile } from '@/lib/profiles';\nimport { ResponsiveNavigation } from '@/components/navigation/ResponsiveNavigation';\nimport { NotificationDropdown } from '@/components/notifications/NotificationDropdown';\nimport { Loader2 } from 'lucide-react';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n  requiredUserType?: 'influencer' | 'business';\n}\n\nexport function DashboardLayout({ children, requiredUserType }: DashboardLayoutProps) {\n  const { user, loading: authLoading } = useAuth();\n  const router = useRouter();\n  const [profile, setProfile] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (authLoading) return;\n\n    if (!user) {\n      router.push('/prijava');\n      return;\n    }\n\n    loadProfile();\n  }, [user, authLoading, router]);\n\n  const loadProfile = async () => {\n    try {\n      setLoading(true);\n      const { data, error } = await getProfile(user!.id);\n\n      if (error) {\n        console.error('Profile loading error:', error);\n        if (error.message && error.message.includes('No rows')) {\n          router.push('/profil/kreiranje');\n          return;\n        }\n        setError('Greška pri učitavanju profila');\n        return;\n      }\n\n      if (!data) {\n        router.push('/profil/kreiranje');\n        return;\n      }\n\n      setProfile(data);\n\n      // Provjeri da li korisnik ima pravo pristupa ovoj stranici\n      if (requiredUserType && data.user_type !== requiredUserType) {\n        // Preusmjeri na odgovarajući dashboard\n        if (data.user_type === 'influencer') {\n          router.push('/dashboard/influencer');\n        } else if (data.user_type === 'business') {\n          router.push('/dashboard/biznis');\n        }\n        return;\n      }\n    } catch (err) {\n      console.error('Unexpected error in loadProfile:', err);\n      setError('Neočekivana greška');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Loading state\n  if (authLoading || loading) {\n    return (\n      <div className=\"min-h-screen bg-background flex items-center justify-center\">\n        <div className=\"flex flex-col items-center space-y-4\">\n          <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\n          <p className=\"text-muted-foreground\">Učitavanje...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-background flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold text-foreground mb-2\">Greška</h2>\n          <p className=\"text-muted-foreground mb-4\">{error}</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90\"\n          >\n            Pokušaj ponovo\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // No profile state\n  if (!profile) {\n    return null; // Router redirect will handle this\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Responsive Navigation */}\n      <ResponsiveNavigation userType={profile.user_type} />\n\n      {/* Main Content - full width without sidebar */}\n      <div className=\"flex flex-col min-h-screen\">\n        {/* Header */}\n        <header className=\"bg-card border-b border-border px-6 py-4 md:pl-20\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-foreground\">\n                {profile.user_type === 'influencer' ? 'Influencer Dashboard' : 'Biznis Dashboard'}\n              </h1>\n              <p className=\"text-muted-foreground\">\n                Dobrodošli, {profile.full_name || profile.username}\n              </p>\n            </div>\n\n            {/* User Info */}\n            <div className=\"flex items-center space-x-3\">\n              <NotificationDropdown />\n              {profile.avatar_url && (\n                <img\n                  src={profile.avatar_url}\n                  alt={profile.username}\n                  className=\"w-8 h-8 rounded-full object-cover\"\n                />\n              )}\n              <div className=\"text-right hidden sm:block\">\n                <p className=\"text-sm font-medium text-foreground\">\n                  {profile.full_name || profile.username}\n                </p>\n                <p className=\"text-xs text-muted-foreground\">\n                  @{profile.username}\n                </p>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        {/* Page Content */}\n        <main className=\"flex-1 overflow-auto pb-16 md:pb-0\">\n          <div className=\"p-6\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAeO,SAAS,gBAAgB,EAAE,QAAQ,EAAE,gBAAgB,EAAwB;IAClF,MAAM,EAAE,IAAI,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC5C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;QAEjB,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,CAAC;YACZ;QACF;QAEA;IACF,GAAG;QAAC;QAAM;QAAa;KAAO;IAE9B,MAAM,cAAc;QAClB,IAAI;YACF,WAAW;YACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAM,EAAE;YAEjD,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;oBACtD,OAAO,IAAI,CAAC;oBACZ;gBACF;gBACA,SAAS;gBACT;YACF;YAEA,IAAI,CAAC,MAAM;gBACT,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,WAAW;YAEX,2DAA2D;YAC3D,IAAI,oBAAoB,KAAK,SAAS,KAAK,kBAAkB;gBAC3D,uCAAuC;gBACvC,IAAI,KAAK,SAAS,KAAK,cAAc;oBACnC,OAAO,IAAI,CAAC;gBACd,OAAO,IAAI,KAAK,SAAS,KAAK,YAAY;oBACxC,OAAO,IAAI,CAAC;gBACd;gBACA;YACF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oCAAoC;YAClD,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,gBAAgB;IAChB,IAAI,eAAe,SAAS;QAC1B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0C;;;;;;kCACxD,8OAAC;wBAAE,WAAU;kCAA8B;;;;;;kCAC3C,8OAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,mBAAmB;IACnB,IAAI,CAAC,SAAS;QACZ,OAAO,MAAM,mCAAmC;IAClD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,wJAAA,CAAA,uBAAoB;gBAAC,UAAU,QAAQ,SAAS;;;;;;0BAGjD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,QAAQ,SAAS,KAAK,eAAe,yBAAyB;;;;;;sDAEjE,8OAAC;4CAAE,WAAU;;gDAAwB;gDACtB,QAAQ,SAAS,IAAI,QAAQ,QAAQ;;;;;;;;;;;;;8CAKtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,2JAAA,CAAA,uBAAoB;;;;;wCACpB,QAAQ,UAAU,kBACjB,8OAAC;4CACC,KAAK,QAAQ,UAAU;4CACvB,KAAK,QAAQ,QAAQ;4CACrB,WAAU;;;;;;sDAGd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,QAAQ,SAAS,IAAI,QAAQ,QAAQ;;;;;;8DAExC,8OAAC;oDAAE,WAAU;;wDAAgC;wDACzC,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ5B,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 2802, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2897, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2921, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2947, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2970, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/app/dashboard/influencer/profile/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport * as z from 'zod';\nimport { DashboardLayout } from '@/components/dashboard/DashboardLayout';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { getProfile, updateProfile, getInfluencer, updateInfluencer } from '@/lib/profiles';\nimport { Loader2, Save, User, Camera, Globe, DollarSign } from 'lucide-react';\nimport { toast } from 'sonner';\n\nconst profileSchema = z.object({\n  username: z.string().min(3, 'Username mora imati najmanje 3 karaktera'),\n  bio: z.string().max(500, 'Bio može imati maksimalno 500 karaktera').optional(),\n  location: z.string().optional(),\n  website_url: z.string().url('Neispravna URL adresa').optional().or(z.literal('')),\n  instagram_handle: z.string().optional(),\n  instagram_followers: z.number().min(0).optional(),\n  tiktok_handle: z.string().optional(),\n  tiktok_followers: z.number().min(0).optional(),\n  youtube_handle: z.string().optional(),\n  youtube_subscribers: z.number().min(0).optional(),\n  price_per_post: z.number().min(0).optional(),\n  price_per_story: z.number().min(0).optional(),\n  price_per_reel: z.number().min(0).optional(),\n});\n\ntype ProfileForm = z.infer<typeof profileSchema>;\n\nexport default function InfluencerProfilePage() {\n  const { user } = useAuth();\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [profile, setProfile] = useState<any>(null);\n  const [influencer, setInfluencer] = useState<any>(null);\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset,\n    setValue\n  } = useForm<ProfileForm>({\n    resolver: zodResolver(profileSchema),\n  });\n\n  useEffect(() => {\n    if (user) {\n      loadData();\n    }\n  }, [user]);\n\n  const loadData = async () => {\n    try {\n      setLoading(true);\n      \n      // Load profile\n      const { data: profileData, error: profileError } = await getProfile(user!.id);\n      if (profileError || !profileData) {\n        toast.error('Greška pri učitavanju profila');\n        return;\n      }\n      setProfile(profileData);\n\n      // Load influencer data\n      const { data: influencerData, error: influencerError } = await getInfluencer(user!.id);\n      if (influencerError || !influencerData) {\n        toast.error('Greška pri učitavanju influencer podataka');\n        return;\n      }\n      setInfluencer(influencerData);\n      \n      // Popuni formu sa postojećim podacima\n      reset({\n        username: profileData.username || '',\n        bio: profileData.bio || '',\n        location: profileData.location || '',\n        website_url: profileData.website_url || '',\n        instagram_handle: influencerData.instagram_handle || '',\n        instagram_followers: influencerData.instagram_followers || 0,\n        tiktok_handle: influencerData.tiktok_handle || '',\n        tiktok_followers: influencerData.tiktok_followers || 0,\n        youtube_handle: influencerData.youtube_handle || '',\n        youtube_subscribers: influencerData.youtube_subscribers || 0,\n        price_per_post: influencerData.price_per_post || 0,\n        price_per_story: influencerData.price_per_story || 0,\n        price_per_reel: influencerData.price_per_reel || 0,\n      });\n    } catch (err) {\n      toast.error('Neočekivana greška');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const onSubmit = async (data: ProfileForm) => {\n    if (!user) return;\n\n    setSaving(true);\n    try {\n      // Update profile\n      const { error: profileError } = await updateProfile(user.id, {\n        username: data.username,\n        bio: data.bio || null,\n        location: data.location || null,\n        website_url: data.website_url || null,\n      });\n\n      if (profileError) {\n        if (profileError.message.includes('username')) {\n          toast.error('Username je već zauzet');\n        } else {\n          toast.error('Greška pri ažuriranju profila');\n        }\n        return;\n      }\n\n      // Update influencer data\n      const { error: influencerError } = await updateInfluencer(user.id, {\n        instagram_handle: data.instagram_handle || null,\n        instagram_followers: data.instagram_followers || 0,\n        tiktok_handle: data.tiktok_handle || null,\n        tiktok_followers: data.tiktok_followers || 0,\n        youtube_handle: data.youtube_handle || null,\n        youtube_subscribers: data.youtube_subscribers || 0,\n        price_per_post: data.price_per_post || null,\n        price_per_story: data.price_per_story || null,\n        price_per_reel: data.price_per_reel || null,\n      });\n\n      if (influencerError) {\n        toast.error('Greška pri ažuriranju influencer podataka');\n        return;\n      }\n\n      toast.success('Profil je uspješno ažuriran');\n      loadData(); // Refresh data\n    } catch (err) {\n      toast.error('Neočekivana greška');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <DashboardLayout requiredUserType=\"influencer\">\n        <div className=\"flex items-center justify-center h-64\">\n          <Loader2 className=\"h-8 w-8 animate-spin\" />\n        </div>\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout requiredUserType=\"influencer\">\n      <div className=\"max-w-4xl mx-auto space-y-6\">\n        {/* Header */}\n        <div>\n          <h1 className=\"text-3xl font-bold\">Postavke profila</h1>\n          <p className=\"text-muted-foreground mt-2\">\n            Upravljajte svojim javnim profilom koji vide brendovi i korisnici\n          </p>\n        </div>\n\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n          {/* Osnovne informacije */}\n          <Card>\n            <CardHeader>\n              <div className=\"flex items-center space-x-2\">\n                <User className=\"h-5 w-5\" />\n                <CardTitle>Osnovne informacije</CardTitle>\n              </div>\n              <CardDescription>\n                Ovi podaci se prikazuju na vašem javnom profilu\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <Label htmlFor=\"username\">Username *</Label>\n                  <Input\n                    id=\"username\"\n                    {...register('username')}\n                    placeholder=\"@vasusername\"\n                  />\n                  {errors.username && (\n                    <p className=\"text-sm text-destructive mt-1\">\n                      {errors.username.message}\n                    </p>\n                  )}\n                </div>\n\n                <div>\n                  <Label htmlFor=\"location\">Lokacija</Label>\n                  <Input\n                    id=\"location\"\n                    {...register('location')}\n                    placeholder=\"Sarajevo, BiH\"\n                  />\n                </div>\n              </div>\n\n              <div>\n                <Label htmlFor=\"bio\">Bio</Label>\n                <Textarea\n                  id=\"bio\"\n                  {...register('bio')}\n                  placeholder=\"Opišite sebe u nekoliko rečenica...\"\n                  rows={4}\n                />\n                {errors.bio && (\n                  <p className=\"text-sm text-destructive mt-1\">\n                    {errors.bio.message}\n                  </p>\n                )}\n              </div>\n\n              <div>\n                <Label htmlFor=\"website_url\">Website</Label>\n                <Input\n                  id=\"website_url\"\n                  {...register('website_url')}\n                  placeholder=\"https://vaswebsite.com\"\n                />\n                {errors.website_url && (\n                  <p className=\"text-sm text-destructive mt-1\">\n                    {errors.website_url.message}\n                  </p>\n                )}\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Društvene mreže */}\n          <Card>\n            <CardHeader>\n              <div className=\"flex items-center space-x-2\">\n                <Globe className=\"h-5 w-5\" />\n                <CardTitle>Društvene mreže</CardTitle>\n              </div>\n              <CardDescription>\n                Dodajte svoje profile na društvenim mrežama\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              {/* Instagram */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <Label htmlFor=\"instagram_handle\">Instagram handle</Label>\n                  <Input\n                    id=\"instagram_handle\"\n                    {...register('instagram_handle')}\n                    placeholder=\"@vasinstagram\"\n                  />\n                </div>\n                <div>\n                  <Label htmlFor=\"instagram_followers\">Instagram pratilaca</Label>\n                  <Input\n                    id=\"instagram_followers\"\n                    type=\"number\"\n                    {...register('instagram_followers', { valueAsNumber: true })}\n                    placeholder=\"1000\"\n                  />\n                </div>\n              </div>\n\n              {/* TikTok */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <Label htmlFor=\"tiktok_handle\">TikTok handle</Label>\n                  <Input\n                    id=\"tiktok_handle\"\n                    {...register('tiktok_handle')}\n                    placeholder=\"@vastiktok\"\n                  />\n                </div>\n                <div>\n                  <Label htmlFor=\"tiktok_followers\">TikTok pratilaca</Label>\n                  <Input\n                    id=\"tiktok_followers\"\n                    type=\"number\"\n                    {...register('tiktok_followers', { valueAsNumber: true })}\n                    placeholder=\"500\"\n                  />\n                </div>\n              </div>\n\n              {/* YouTube */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <Label htmlFor=\"youtube_handle\">YouTube kanal</Label>\n                  <Input\n                    id=\"youtube_handle\"\n                    {...register('youtube_handle')}\n                    placeholder=\"@vaskanal\"\n                  />\n                </div>\n                <div>\n                  <Label htmlFor=\"youtube_subscribers\">YouTube pretplatnika</Label>\n                  <Input\n                    id=\"youtube_subscribers\"\n                    type=\"number\"\n                    {...register('youtube_subscribers', { valueAsNumber: true })}\n                    placeholder=\"200\"\n                  />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Cijene */}\n          <Card>\n            <CardHeader>\n              <div className=\"flex items-center space-x-2\">\n                <DollarSign className=\"h-5 w-5\" />\n                <CardTitle>Cijene</CardTitle>\n              </div>\n              <CardDescription>\n                Postavite svoje cijene za različite tipove sadržaja (u KM)\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div>\n                  <Label htmlFor=\"price_per_post\">Cijena po postu</Label>\n                  <Input\n                    id=\"price_per_post\"\n                    type=\"number\"\n                    {...register('price_per_post', { valueAsNumber: true })}\n                    placeholder=\"100\"\n                  />\n                </div>\n                <div>\n                  <Label htmlFor=\"price_per_story\">Cijena po story</Label>\n                  <Input\n                    id=\"price_per_story\"\n                    type=\"number\"\n                    {...register('price_per_story', { valueAsNumber: true })}\n                    placeholder=\"50\"\n                  />\n                </div>\n                <div>\n                  <Label htmlFor=\"price_per_reel\">Cijena po reel</Label>\n                  <Input\n                    id=\"price_per_reel\"\n                    type=\"number\"\n                    {...register('price_per_reel', { valueAsNumber: true })}\n                    placeholder=\"150\"\n                  />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Save Button */}\n          <div className=\"flex justify-end\">\n            <Button type=\"submit\" disabled={saving}>\n              {saving ? (\n                <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n              ) : (\n                <Save className=\"h-4 w-4 mr-2\" />\n              )}\n              Sačuvaj promjene\n            </Button>\n          </div>\n        </form>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAfA;;;;;;;;;;;;;;;;AAiBA,MAAM,gBAAgB,+IAAA,CAAA,SAAQ,CAAC;IAC7B,UAAU,+IAAA,CAAA,SAAQ,GAAG,GAAG,CAAC,GAAG;IAC5B,KAAK,+IAAA,CAAA,SAAQ,GAAG,GAAG,CAAC,KAAK,2CAA2C,QAAQ;IAC5E,UAAU,+IAAA,CAAA,SAAQ,GAAG,QAAQ;IAC7B,aAAa,+IAAA,CAAA,SAAQ,GAAG,GAAG,CAAC,yBAAyB,QAAQ,GAAG,EAAE,CAAC,+IAAA,CAAA,UAAS,CAAC;IAC7E,kBAAkB,+IAAA,CAAA,SAAQ,GAAG,QAAQ;IACrC,qBAAqB,+IAAA,CAAA,SAAQ,GAAG,GAAG,CAAC,GAAG,QAAQ;IAC/C,eAAe,+IAAA,CAAA,SAAQ,GAAG,QAAQ;IAClC,kBAAkB,+IAAA,CAAA,SAAQ,GAAG,GAAG,CAAC,GAAG,QAAQ;IAC5C,gBAAgB,+IAAA,CAAA,SAAQ,GAAG,QAAQ;IACnC,qBAAqB,+IAAA,CAAA,SAAQ,GAAG,GAAG,CAAC,GAAG,QAAQ;IAC/C,gBAAgB,+IAAA,CAAA,SAAQ,GAAG,GAAG,CAAC,GAAG,QAAQ;IAC1C,iBAAiB,+IAAA,CAAA,SAAQ,GAAG,GAAG,CAAC,GAAG,QAAQ;IAC3C,gBAAgB,+IAAA,CAAA,SAAQ,GAAG,GAAG,CAAC,GAAG,QAAQ;AAC5C;AAIe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC5C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAElD,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACL,QAAQ,EACT,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAe;QACvB,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;KAAK;IAET,MAAM,WAAW;QACf,IAAI;YACF,WAAW;YAEX,eAAe;YACf,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAM,EAAE;YAC5E,IAAI,gBAAgB,CAAC,aAAa;gBAChC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YACA,WAAW;YAEX,uBAAuB;YACvB,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,KAAM,EAAE;YACrF,IAAI,mBAAmB,CAAC,gBAAgB;gBACtC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YACA,cAAc;YAEd,sCAAsC;YACtC,MAAM;gBACJ,UAAU,YAAY,QAAQ,IAAI;gBAClC,KAAK,YAAY,GAAG,IAAI;gBACxB,UAAU,YAAY,QAAQ,IAAI;gBAClC,aAAa,YAAY,WAAW,IAAI;gBACxC,kBAAkB,eAAe,gBAAgB,IAAI;gBACrD,qBAAqB,eAAe,mBAAmB,IAAI;gBAC3D,eAAe,eAAe,aAAa,IAAI;gBAC/C,kBAAkB,eAAe,gBAAgB,IAAI;gBACrD,gBAAgB,eAAe,cAAc,IAAI;gBACjD,qBAAqB,eAAe,mBAAmB,IAAI;gBAC3D,gBAAgB,eAAe,cAAc,IAAI;gBACjD,iBAAiB,eAAe,eAAe,IAAI;gBACnD,gBAAgB,eAAe,cAAc,IAAI;YACnD;QACF,EAAE,OAAO,KAAK;YACZ,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,MAAM;QAEX,UAAU;QACV,IAAI;YACF,iBAAiB;YACjB,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,EAAE,EAAE;gBAC3D,UAAU,KAAK,QAAQ;gBACvB,KAAK,KAAK,GAAG,IAAI;gBACjB,UAAU,KAAK,QAAQ,IAAI;gBAC3B,aAAa,KAAK,WAAW,IAAI;YACnC;YAEA,IAAI,cAAc;gBAChB,IAAI,aAAa,OAAO,CAAC,QAAQ,CAAC,aAAa;oBAC7C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd,OAAO;oBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;gBACA;YACF;YAEA,yBAAyB;YACzB,MAAM,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,EAAE,EAAE;gBACjE,kBAAkB,KAAK,gBAAgB,IAAI;gBAC3C,qBAAqB,KAAK,mBAAmB,IAAI;gBACjD,eAAe,KAAK,aAAa,IAAI;gBACrC,kBAAkB,KAAK,gBAAgB,IAAI;gBAC3C,gBAAgB,KAAK,cAAc,IAAI;gBACvC,qBAAqB,KAAK,mBAAmB,IAAI;gBACjD,gBAAgB,KAAK,cAAc,IAAI;gBACvC,iBAAiB,KAAK,eAAe,IAAI;gBACzC,gBAAgB,KAAK,cAAc,IAAI;YACzC;YAEA,IAAI,iBAAiB;gBACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,YAAY,eAAe;QAC7B,EAAE,OAAO,KAAK;YACZ,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,UAAU;QACZ;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC,kJAAA,CAAA,kBAAe;YAAC,kBAAiB;sBAChC,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,iNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;;;;;;;;;;;IAI3B;IAEA,qBACE,8OAAC,kJAAA,CAAA,kBAAe;QAAC,kBAAiB;kBAChC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,8OAAC;oBAAK,UAAU,aAAa;oBAAW,WAAU;;sCAEhD,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;;;;;;;sDAEb,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAW;;;;;;sEAC1B,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACF,GAAG,SAAS,WAAW;4DACxB,aAAY;;;;;;wDAEb,OAAO,QAAQ,kBACd,8OAAC;4DAAE,WAAU;sEACV,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;8DAK9B,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAW;;;;;;sEAC1B,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACF,GAAG,SAAS,WAAW;4DACxB,aAAY;;;;;;;;;;;;;;;;;;sDAKlB,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAM;;;;;;8DACrB,8OAAC,oIAAA,CAAA,WAAQ;oDACP,IAAG;oDACF,GAAG,SAAS,MAAM;oDACnB,aAAY;oDACZ,MAAM;;;;;;gDAEP,OAAO,GAAG,kBACT,8OAAC;oDAAE,WAAU;8DACV,OAAO,GAAG,CAAC,OAAO;;;;;;;;;;;;sDAKzB,8OAAC;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAc;;;;;;8DAC7B,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACF,GAAG,SAAS,cAAc;oDAC3B,aAAY;;;;;;gDAEb,OAAO,WAAW,kBACjB,8OAAC;oDAAE,WAAU;8DACV,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;sCAQrC,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;;;;;;;sDAEb,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDAErB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAmB;;;;;;sEAClC,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACF,GAAG,SAAS,mBAAmB;4DAChC,aAAY;;;;;;;;;;;;8DAGhB,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAsB;;;;;;sEACrC,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACJ,GAAG,SAAS,uBAAuB;gEAAE,eAAe;4DAAK,EAAE;4DAC5D,aAAY;;;;;;;;;;;;;;;;;;sDAMlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAgB;;;;;;sEAC/B,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACF,GAAG,SAAS,gBAAgB;4DAC7B,aAAY;;;;;;;;;;;;8DAGhB,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAmB;;;;;;sEAClC,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACJ,GAAG,SAAS,oBAAoB;gEAAE,eAAe;4DAAK,EAAE;4DACzD,aAAY;;;;;;;;;;;;;;;;;;sDAMlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAiB;;;;;;sEAChC,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACF,GAAG,SAAS,iBAAiB;4DAC9B,aAAY;;;;;;;;;;;;8DAGhB,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAsB;;;;;;sEACrC,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACJ,GAAG,SAAS,uBAAuB;gEAAE,eAAe;4DAAK,EAAE;4DAC5D,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQtB,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;;;;;;;sDAEb,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAiB;;;;;;kEAChC,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACJ,GAAG,SAAS,kBAAkB;4DAAE,eAAe;wDAAK,EAAE;wDACvD,aAAY;;;;;;;;;;;;0DAGhB,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAkB;;;;;;kEACjC,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACJ,GAAG,SAAS,mBAAmB;4DAAE,eAAe;wDAAK,EAAE;wDACxD,aAAY;;;;;;;;;;;;0DAGhB,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAiB;;;;;;kEAChC,8OAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACJ,GAAG,SAAS,kBAAkB;4DAAE,eAAe;wDAAK,EAAE;wDACvD,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,UAAU;;oCAC7B,uBACC,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAChB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}]}