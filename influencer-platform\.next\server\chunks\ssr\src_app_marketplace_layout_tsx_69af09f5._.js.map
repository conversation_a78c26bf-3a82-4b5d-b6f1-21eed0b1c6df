{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/DEV/Software/1%20Influencer%20Marketing/influencer-platform/src/app/marketplace/layout.tsx"], "sourcesContent": ["import { Metadata } from 'next';\n\nexport const metadata: Metadata = {\n  title: 'Marketplace | InfluConnect',\n  description: 'Pronađite savršenog influencera za vašu kampanju',\n};\n\nexport default function MarketplaceLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return children;\n}\n"], "names": [], "mappings": ";;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,kBAAkB,EACxC,QAAQ,EAGT;IACC,OAAO;AACT", "debugId": null}}]}